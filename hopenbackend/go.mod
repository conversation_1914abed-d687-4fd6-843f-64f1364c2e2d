module encore.app

go 1.23.0

toolchain go1.24.3

require (
	encore.dev v1.46.1
	github.com/eclipse/paho.mqtt.golang v1.5.0
	github.com/go-redis/redis/v8 v8.11.5
	github.com/gorilla/websocket v1.5.3

	// Added dependencies for enterprise implementations
	github.com/jackc/pgx/v5 v5.7.5
	github.com/prometheus/client_golang v1.20.5
)

require github.com/golang-jwt/jwt/v5 v5.2.2

require (
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/dgryski/go-rendezvous v0.0.0-20200823014737-9f7001d12a5f // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20240606120523-5a60cdf6a761 // indirect
	github.com/jackc/puddle/v2 v2.2.2 // indirect
	github.com/klauspost/compress v1.17.11 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.60.1 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/stretchr/testify v1.10.0 // indirect
	golang.org/x/crypto v0.37.0 // indirect
	golang.org/x/net v0.37.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	golang.org/x/sys v0.32.0 // indirect
	golang.org/x/text v0.24.0 // indirect
	google.golang.org/protobuf v1.35.2 // indirect
)
