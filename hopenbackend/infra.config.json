{"$schema": "https://encore.dev/schemas/infra.schema.json", "metadata": {"app_id": "hopen-backend", "env_name": "production", "env_type": "production", "cloud": "docker", "base_url": "http://localhost:4000"}, "sql_servers": [{"host": "localhost:5432", "tls_config": {"disabled": true}, "databases": {"hopen": {"name": "hopen", "username": "postgres", "password": {"$env": "DB_PASSWORD"}}}}], "auth": [{"type": "key", "id": 1, "key": {"$env": "AUTH_KEY"}}], "secrets": {"AUTH_DOMAIN": {"$env": "AUTH_DOMAIN"}, "AUTH_CLIENT_ID": {"$env": "AUTH_CLIENT_ID"}, "AUTH_CLIENT_SECRET": {"$env": "AUTH_CLIENT_SECRET"}, "AUTH_REDIRECT_URL": {"$env": "AUTH_REDIRECT_URL"}}}