// Local development secrets for Hopen Backend
// This file contains configuration for local development environment

// JWT Configuration
JWT_SECRET: "hopen-production-jwt-secret-256-bit-key-2024-secure-token-generation"
JWT_REFRESH_SECRET: "hopen-production-refresh-secret-256-bit-key-2024-secure-refresh"

// Google OAuth Configuration
GOOGLE_CLIENT_ID: "257996495540-8555p6h5grhi9qmqrts661sro96kfd7.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET: "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"

// Apple Sign-In Configuration
APPLE_TEAM_ID: "8623VTFKZF"
APPLE_KEY_ID: "CW89ABQD5U"
APPLE_CLIENT_ID: "com.hopenapp.hopen"
APPLE_PRIVATE_KEY: """*****************************************************************************************************************************************************************************************************************************************************************"""

// Firebase Configuration
FIREBASE_PROJECT_ID: "hopen-id"
FIREBASE_PRIVATE_KEY_ID: "257996495540"
FIREBASE_CLIENT_EMAIL: "<EMAIL>"
FIREBASE_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----"

// AWS SES Configuration
AWS_REGION: "us-east-1"
AWS_ACCESS_KEY_ID: "AKIA..."
AWS_SECRET_ACCESS_KEY: "..."
AWS_SES_FROM_EMAIL: "<EMAIL>"

// PostHog Analytics
POSTHOG_API_KEY: "phc_..."
POSTHOG_HOST: "https://app.posthog.com"

// Database Configurations
POSTGRES_HOST: "postgres"
POSTGRES_PORT: 5432
POSTGRES_DB: "hopen"
POSTGRES_USER: "hopen"
POSTGRES_PASSWORD: "hopen123"
DATABASE_URL: "***************************************/hopen?sslmode=disable"

ARANGODB_HOST: "arangodb"
ARANGODB_PORT: 8529
ARANGODB_DATABASE: "hopen_social"
ARANGODB_USERNAME: "root"
ARANGODB_PASSWORD: "hopen123"

CASSANDRA_HOSTS: "cassandra:9042"
CASSANDRA_KEYSPACE: "hopen_messages"

VALKEY_HOST: "valkey"
VALKEY_PORT: 6379

// MinIO Configuration
MINIO_ENDPOINT: "minio:9000"
MINIO_ACCESS_KEY: "hopen"
MINIO_SECRET_KEY: "hopen123"
MINIO_USE_SSL: false

// EMQX Configuration
EMQX_HOST: "emqx"
EMQX_PORT: 1883
EMQX_USERNAME: "admin"
EMQX_PASSWORD: "public"
MQTT_URL: "mqtt://emqx:1883"

// NATS Configuration
NATS_URL: "nats://nats:4222"
NATS_CONNECTION_NAME: "hopen-backend-prod"
NATS_ENABLE_JETSTREAM: true

// Ory Stack Configuration
KRATOS_PUBLIC_URL: "http://kratos:4433"
KRATOS_ADMIN_URL: "http://kratos:4434"
HYDRA_PUBLIC_URL: "http://hydra:4444"
HYDRA_ADMIN_URL: "http://hydra:4445"
HYDRA_JWKS_URL: "http://hydra:4444/.well-known/jwks.json"

// WebRTC Configuration
WEBRTC_HOST: "webrtc-server"
WEBRTC_PORT: 8080
WEBRTC_STUN_PORT: 3478

// Environment Configuration
ENVIRONMENT: "development"
NODE_ENV: "development"
LOG_LEVEL: "debug"
PORT: 4000

// FCM Configuration
FCMServerKey: "development-fcm-key-placeholder-not-functional"
FCMAPIKey: "your-fcm-api-key"

// Monitoring Configuration
PROMETHEUS_ENABLED: true
PROMETHEUS_METRICS_PATH: "/metrics"
PROMETHEUS_PORT: 9090 