package notification

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"encore.app/microservices/user"
)

// Initialize the push notification service database
var notificationDB = sqldb.NewDatabase("notification", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

//encore:service
type Service struct{}

// ServiceInfo provides information about the notification service.
type ServiceInfo struct {
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// Info returns information about the notification service.
//encore:api public method=GET path=/api/notification/
func Info(ctx context.Context) (*ServiceInfo, error) {
	return &ServiceInfo{
		Message: "Notification service ready",
		Data: map[string]interface{}{
			"description": "Push notifications, in-app notifications, and email notifications",
			"endpoints": []string{
				"POST /notifications/send",
				"GET /notifications/user/:userID",
				"PUT /notifications/:notificationID/read",
			},
		},
	}, nil
}

// Notification data payload (simplified for API schema compatibility)
type NotificationData struct {
	BubbleID     string `json:"bubble_id,omitempty"`
	MessageID    string `json:"message_id,omitempty"`
	ActionType   string `json:"action_type,omitempty"`
	CustomString string `json:"custom_string,omitempty"`
	CustomNumber int    `json:"custom_number,omitempty"`
}

// SendNotificationRequest defines the structure for sending notifications
type SendNotificationRequest struct {
	UserID    string           `json:"user_id"`
	Type      string           `json:"type"`
	Title     string           `json:"title"`
	Message   string           `json:"message"`
	Data      NotificationData `json:"data,omitempty"`
	SendInApp bool             `json:"send_in_app"`
	SendPush  bool             `json:"send_push"`
	SendEmail bool             `json:"send_email"`
	Token     string           `json:"token,omitempty"` // FCM token for direct push
}

// SendNotificationResponse defines the response for sending notifications
type SendNotificationResponse struct {
	Success bool   `json:"success"`
	ID      string `json:"id,omitempty"`
	Message string `json:"message,omitempty"`
}

// GetNotificationsRequest defines the request for getting user notifications
type GetNotificationsRequest struct {
	UserID string `json:"user_id"`
	Limit  int    `json:"limit,omitempty"`
	Offset int    `json:"offset,omitempty"`
}

// NotificationItem represents a single notification
type NotificationItem struct {
	ID        string           `json:"id"`
	UserID    string           `json:"user_id"`
	Type      string           `json:"type"`
	Title     string           `json:"title"`
	Message   string           `json:"message"`
	Data      NotificationData `json:"data,omitempty"`
	Read      bool             `json:"read"`
	CreatedAt time.Time        `json:"created_at"`
}

// GetNotificationsResponse defines the response for getting notifications
type GetNotificationsResponse struct {
	Notifications []NotificationItem `json:"notifications"`
	Total         int                `json:"total"`
}

// HealthResponse defines the health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}

// Send notification endpoint
//encore:api auth method=POST path=/notifications/send
func SendNotification(ctx context.Context, req *SendNotificationRequest) (*SendNotificationResponse, error) {
	rlog.Info("Sending notification", "user_id", req.UserID, "type", req.Type, "title", req.Title)

	// Validate request
	if req.UserID == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("user_id is required").Err()
	}
	if req.Type == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("type is required").Err()
	}
	if req.Title == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("title is required").Err()
	}
	if req.Message == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("message is required").Err()
	}

	var notificationID string

	// Store in-app notification if requested
	if req.SendInApp {
		dataJSON, _ := json.Marshal(req.Data)
		err := notificationDB.QueryRow(ctx, `
			INSERT INTO notifications (user_id, type, title, message, data, read, created_at)
			VALUES ($1, $2, $3, $4, $5, false, NOW())
			RETURNING id
		`, req.UserID, req.Type, req.Title, req.Message, dataJSON).Scan(&notificationID)
		if err != nil {
			rlog.Error("failed to store notification", "error", err)
			return nil, errs.B().Code(errs.Internal).Msg("failed to store notification").Err()
		}
	}

	// Send push notification if requested
	if req.SendPush {
		err := sendPushNotification(ctx, req)
		if err != nil {
			rlog.Error("failed to send push notification", "error", err)
			// Don't fail the entire request for push notification failures
		}
	}

	// Send email notification if requested
	if req.SendEmail {
		err := sendEmailNotification(ctx, req)
		if err != nil {
			rlog.Error("failed to send email notification", "error", err)
			// Don't fail the entire request for email notification failures
		}
	}

	return &SendNotificationResponse{
		Success: true,
		ID:      notificationID,
		Message: "Notification sent successfully",
	}, nil
}

// Get user notifications endpoint
//encore:api auth method=GET path=/notifications/user/:userID
func GetUserNotifications(ctx context.Context, userID string) (*GetNotificationsResponse, error) {
	rlog.Info("Getting user notifications", "user_id", userID)

	rows, err := notificationDB.Query(ctx, `
		SELECT id, user_id, type, title, message, data, read, created_at
		FROM notifications
		WHERE user_id = $1
		ORDER BY created_at DESC
		LIMIT 50
	`, userID)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to get notifications").Err()
	}
	defer rows.Close()

	var notifications []NotificationItem
	for rows.Next() {
		var n NotificationItem
		var dataJSON []byte
		err := rows.Scan(&n.ID, &n.UserID, &n.Type, &n.Title, &n.Message, &dataJSON, &n.Read, &n.CreatedAt)
		if err != nil {
			continue
		}
		
		if len(dataJSON) > 0 {
			json.Unmarshal(dataJSON, &n.Data)
		}
		
		notifications = append(notifications, n)
	}

	return &GetNotificationsResponse{
		Notifications: notifications,
		Total:         len(notifications),
	}, nil
}

// Mark notification as read endpoint
//encore:api auth method=PUT path=/notifications/:notificationID/read
func MarkAsRead(ctx context.Context, notificationID string) (*SendNotificationResponse, error) {
	rlog.Info("Marking notification as read", "notification_id", notificationID)

	_, err := notificationDB.Exec(ctx, `
		UPDATE notifications
		SET read = true
		WHERE id = $1
	`, notificationID)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to update notification").Err()
	}

	return &SendNotificationResponse{
		Success: true,
		Message: "Notification marked as read",
	}, nil
}

// Health check endpoint
//encore:api public method=GET path=/notifications/health
func Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:  "healthy",
		Service: "notification",
		Version: "1.0.0",
	}, nil
}

// Helper functions

func sendPushNotification(ctx context.Context, req *SendNotificationRequest) error {
	// Get FCM tokens for the user if not provided directly
	if req.Token == "" {
		// Get tokens from user service
		tokens, err := getUserFCMTokens(ctx, req.UserID)
		if err != nil {
			return fmt.Errorf("failed to get FCM tokens: %w", err)
		}
		
		for _, token := range tokens {
			// TODO: Implement actual FCM push notification sending
			rlog.Info("Would send FCM notification", "token", token, "title", req.Title)
		}
	} else {
		// Send to specific token
		rlog.Info("Would send FCM notification to token", "token", req.Token, "title", req.Title)
	}
	
	return nil
}

func sendEmailNotification(ctx context.Context, req *SendNotificationRequest) error {
	// Get user email
	userObj, err := user.Get(ctx, req.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user: %w", err)
	}
	
	// TODO: Implement actual email sending via AWS SES
	rlog.Info("Would send email notification", "email", userObj.Email, "subject", req.Title)
	
	return nil
}

func getUserFCMTokens(ctx context.Context, userID string) ([]string, error) {
	// Get FCM tokens from user service
	resp, err := user.GetFCMTokens(ctx, &user.GetFCMTokensParams{UserID: userID})
	if err != nil {
		return nil, err
	}
	
	return resp.Tokens, nil
} 