package bubble

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/cron"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	realtime "encore.app/microservices/realtime"
	"encore.app/microservices/notification"
	"encore.app/microservices/user"
)

// BubbleLifecycleStatus defines the status of a bubble.
type BubbleLifecycleStatus string

const (
	BubbleLifecycleStatusCreating  BubbleLifecycleStatus = "creating"
	BubbleLifecycleStatusActive    BubbleLifecycleStatus = "active"
	BubbleLifecycleStatusExpired   BubbleLifecycleStatus = "expired"
	BubbleLifecycleStatusArchived  BubbleLifecycleStatus = "archived"
	BubbleLifecycleStatusDissolved BubbleLifecycleStatus = "dissolved"
)

// BubbleMembershipStatus defines the status of a member in a bubble.
type BubbleMembershipStatus string

const (
	BubbleMembershipStatusInvited  BubbleMembershipStatus = "invited"
	BubbleMembershipStatusJoined   BubbleMembershipStatus = "joined"
	BubbleMembershipStatusLeft     BubbleMembershipStatus = "left"
	BubbleMembershipStatusRemoved  BubbleMembershipStatus = "removed"
	BubbleMembershipStatusRejected BubbleMembershipStatus = "rejected"
)

// Bubble represents a bubble entity.
type Bubble struct {
	ID              string                `json:"id"`
	Name            string                `json:"name"`
	Description     *string               `json:"description,omitempty"`
	LifecycleStatus BubbleLifecycleStatus `json:"lifecycle_status"`
	CreatedBy       string                `json:"created_by"`
	CreatedAt       time.Time             `json:"created_at"`
	UpdatedAt       time.Time             `json:"updated_at"`
	ExpiresAt       time.Time             `json:"expires_at"`
	ActiveCallID    *string               `json:"active_call_id,omitempty"`
	MaxMembers      int                   `json:"max_members"`
	IsPublic        bool                  `json:"is_public"`
}

// BubbleMember represents a member of a bubble.
type BubbleMember struct {
	BubbleID          string                 `json:"bubble_id"`
	UserID            string                 `json:"user_id"`
	Status            BubbleMembershipStatus `json:"status"`
	JoinedAt          *time.Time             `json:"joined_at,omitempty"`
	LeftAt            *time.Time             `json:"left_at,omitempty"`
	ColorCode         *string                `json:"color_code,omitempty"`
	Role              string                 `json:"role"`
	CreatedAt         time.Time              `json:"created_at"`
	UpdatedAt         time.Time              `json:"updated_at"`
	Name              *string                `json:"name,omitempty"`
	ProfilePictureURL *string                `json:"profile_picture_url,omitempty"`
}

// BubbleInfo represents extended information about a bubble.
type BubbleInfo struct {
	BubbleID       string    `json:"bubble_id"`
	LocationName   *string   `json:"location_name,omitempty"`
	LocationLat    *float64  `json:"location_lat,omitempty"`
	LocationLng    *float64  `json:"location_lng,omitempty"`
	InterestTags   []string  `json:"interest_tags,omitempty"`
	CustomImageURL *string   `json:"custom_image_url,omitempty"`
	LastActivityAt time.Time `json:"last_activity_at"`
	MessageCount   int       `json:"message_count"`
	MediaCount     int       `json:"media_count"`
}

// BubbleWithMembers represents a bubble with its members.
type BubbleWithMembers struct {
	Bubble  Bubble         `json:"bubble"`
	Members []BubbleMember `json:"members"`
	Info    *BubbleInfo    `json:"info,omitempty"`
}

// CreateBubbleParams defines parameters for creating a bubble.
type CreateBubbleParams struct {
	Name string `json:"name"`
}

type GetBubbleParams struct {
	BubbleID string `json:"bubble_id"`
}

type ListUserBubblesParams struct {
	UserID         string `json:"user_id,omitempty"`
	IncludeExpired bool   `json:"include_expired"`
}

type ListUserBubblesResponse struct {
	Bubbles []BubbleWithMembers `json:"bubbles"`
}

type LeaveBubbleParams struct {
	BubbleID string `json:"bubble_id"`
}

type UpdateBubbleCallParams struct {
	BubbleID   string  `json:"bubble_id"`
	CallID     *string `json:"call_id"`
	WithVideo  bool    `json:"with_video"`
	WithScreen bool    `json:"with_screen"`
}

type UpdateBubbleCallResponse struct {
	Success    bool   `json:"success"`
	BubbleID   string `json:"bubble_id"`
	BubbleName string `json:"bubble_name"`
	CallID     string `json:"call_id,omitempty"`
	CallStatus string `json:"call_status"`
}

type SendBubbleReminderParams struct {
	Days int `json:"days"`
}

type ListPublicBubblesParams struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

type ListPublicBubblesResponse struct {
	Bubbles []BubbleWithMembers `json:"bubbles"`
	Total   int                 `json:"total"`
}

type RequestJoinBubbleParams struct {
	BubbleID       string `json:"bubble_id"`
	RequestMessage string `json:"request_message,omitempty"`
}

type RequestJoinBubbleResponse struct {
	Success       bool   `json:"success"`
	BubbleID      string `json:"bubble_id"`
	BubbleName    string `json:"bubble_name"`
	RequestID     string `json:"request_id"`
	RequestStatus string `json:"request_status"`
}

type RespondToJoinRequestParams struct {
	RequestID string `json:"request_id"`
	Accept    bool   `json:"accept"`
}

type RespondToJoinRequestResponse struct {
	Success  bool   `json:"success"`
	BubbleID string `json:"bubble_id"`
	UserID   string `json:"user_id"`
	Status   string `json:"status"`
}

type ArchiveBubbleResponse struct {
	Success    bool   `json:"success"`
	BubbleID   string `json:"bubble_id"`
	BubbleName string `json:"bubble_name"`
}

type InviteToBubbleParams struct {
	UserID string `json:"user_id"`
}

type InviteToBubbleResponse struct {
	Success    bool   `json:"success"`
	BubbleID   string `json:"bubble_id"`
	BubbleName string `json:"bubble_name"`
	InviteeID  string `json:"invitee_id"`
}

var db = sqldb.NewDatabase("bubble", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

// Service struct definition.
//encore:service
type Service struct {
	db *sqldb.Database
}

// initService is automatically called by Encore when the service starts up.
func initService() (*Service, error) {
	return &Service{db: db}, nil
}

// ServiceInfo provides information about the bubble service.
type ServiceInfo struct {
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// Info returns information about the bubble service.
//encore:api public method=GET path=/api/bubble/
func (s *Service) Info(ctx context.Context) (*ServiceInfo, error) {
	return &ServiceInfo{
		Message: "Bubble service ready",
		Data: map[string]interface{}{
			"description": "Manages bubbles (group chats) and their lifecycle",
			"endpoints": []string{
				"POST /bubbles",
				"GET /bubbles",
				"GET /bubble/:bubbleID",
				"POST /bubble/:bubbleID/leave",
				"POST /bubble/:bubbleID/call",
			},
		},
	}, nil
}

type CreateBubbleResponse struct {
	ID string `json:"id"`
}

//encore:api auth method=POST path=/bubbles
func (s *Service) CreateBubble(ctx context.Context, params *CreateBubbleParams) (*CreateBubbleResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Create the bubble
	bubbleID := fmt.Sprintf("bubble_%d", time.Now().UnixNano())
	expiresAt := time.Now().Add(24 * time.Hour) // Expires in 24 hours

	var createdBubbleID string
	err := db.QueryRow(ctx, `
		INSERT INTO bubbles (id, name, lifecycle_status, created_by, created_at, updated_at, expires_at, max_members, is_public)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
		RETURNING id
	`, bubbleID, params.Name, BubbleLifecycleStatusActive, userID, time.Now(), time.Now(), expiresAt, 10, false).Scan(&createdBubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to create bubble: %v", err)
	}

	// Add the creator as a member
	_, err = db.Exec(ctx, `
		INSERT INTO bubble_members (bubble_id, user_id, status, joined_at, role, created_at, updated_at)
		VALUES ($1, $2, $3, $4, 'creator', $5, $6)
	`, createdBubbleID, userID, BubbleMembershipStatusJoined, time.Now(), time.Now(), time.Now())
	if err != nil {
		return nil, fmt.Errorf("failed to add creator as member: %v", err)
	}

	// Publish event
	go s.publishBubbleCreationEvent(createdBubbleID, userID, params.Name)

	return &CreateBubbleResponse{ID: createdBubbleID}, nil
}

//encore:api auth method=GET path=/bubbles/public/list
func (s *Service) ListPublicBubbles(ctx context.Context, params *ListPublicBubblesParams) (*ListPublicBubblesResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	limit := 20
	if params.Limit > 0 && params.Limit <= 100 {
		limit = params.Limit
	}

	offset := 0
	if params.Offset > 0 {
		offset = params.Offset
	}

	// Get public bubbles that the user is not already a member of
	rows, err := db.Query(ctx, `
		SELECT b.id, b.name, b.description, b.lifecycle_status, b.created_by, 
		       b.created_at, b.updated_at, b.expires_at, b.active_call_id, 
		       b.max_members, b.is_public
		FROM bubbles b
		WHERE b.is_public = true 
		AND b.lifecycle_status = 'active'
		AND NOT EXISTS (
			SELECT 1 FROM bubble_members
			WHERE bubble_id = b.id AND user_id = $1 AND status IN ('joined', 'invited')
		)
		ORDER BY b.created_at DESC
		LIMIT $2 OFFSET $3
	`, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to query public bubbles: %v", err)
	}
	defer rows.Close()

	var bubbles []BubbleWithMembers
	for rows.Next() {
		var bubble Bubble
		err := rows.Scan(
			&bubble.ID, &bubble.Name, &bubble.Description, &bubble.LifecycleStatus,
			&bubble.CreatedBy, &bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt,
			&bubble.ActiveCallID, &bubble.MaxMembers, &bubble.IsPublic,
		)
		if err != nil {
			continue
		}

		// Get members for each bubble
		members, _ := s.getBubbleMembers(ctx, bubble.ID)
		bubbles = append(bubbles, BubbleWithMembers{
			Bubble:  bubble,
			Members: members,
		})
	}

	return &ListPublicBubblesResponse{
		Bubbles: bubbles,
		Total:   len(bubbles),
	}, nil
}

//encore:api auth method=GET path=/bubble/:bubbleID
func (s *Service) GetBubble(ctx context.Context, bubbleID string) (*BubbleWithMembers, error) {
	var bubble Bubble
	err := db.QueryRow(ctx, `
		SELECT id, name, description, lifecycle_status, created_by, created_at, updated_at, expires_at, active_call_id, max_members, is_public
		FROM bubbles
		WHERE id = $1
	`, bubbleID).Scan(
		&bubble.ID, &bubble.Name, &bubble.Description, &bubble.LifecycleStatus,
		&bubble.CreatedBy, &bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt,
		&bubble.ActiveCallID, &bubble.MaxMembers, &bubble.IsPublic,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble: %v", err)
	}

	// Get members
	members, err := s.getBubbleMembers(ctx, bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble members: %v", err)
	}

	// Get bubble info
	info, _ := s.getBubbleInfo(ctx, bubbleID)

	return &BubbleWithMembers{
		Bubble:  bubble,
		Members: members,
		Info:    info,
	}, nil
}

//encore:api auth method=GET path=/bubbles
func (s *Service) ListUserBubbles(ctx context.Context, params *ListUserBubblesParams) (*ListUserBubblesResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	query := `
		SELECT b.id, b.name, b.description, b.lifecycle_status, b.created_by, b.created_at, b.updated_at, b.expires_at, b.active_call_id, b.max_members, b.is_public
		FROM bubbles b
		JOIN bubble_members bm ON b.id = bm.bubble_id
		WHERE bm.user_id = $1 AND bm.status = 'joined'`

	if !params.IncludeExpired {
		query += " AND b.lifecycle_status != 'expired' AND b.lifecycle_status != 'archived'"
	}

	rows, err := db.Query(ctx, query, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to list bubbles: %w", err)
	}
	defer rows.Close()

	var bubbles []BubbleWithMembers
	for rows.Next() {
		var bubble Bubble
		if err := rows.Scan(&bubble.ID, &bubble.Name, &bubble.Description, &bubble.LifecycleStatus, &bubble.CreatedBy, &bubble.CreatedAt, &bubble.UpdatedAt, &bubble.ExpiresAt, &bubble.ActiveCallID, &bubble.MaxMembers, &bubble.IsPublic); err != nil {
			return nil, fmt.Errorf("failed to scan bubble: %w", err)
		}

		bubbleWithMembers, err := s.GetBubble(ctx, bubble.ID)
		if err != nil {
			rlog.Error("failed to get bubble with members", "error", err, "bubble_id", bubble.ID)
			continue
		}
		bubbles = append(bubbles, *bubbleWithMembers)
	}

	return &ListUserBubblesResponse{Bubbles: bubbles}, nil
}

func (s *Service) publishBubbleCreationEvent(bubbleID, creatorID, bubbleName string) {
	ctx := context.Background()
	event := map[string]interface{}{
		"event_type": "bubble_created",
		"bubble_id":  bubbleID,
		"creator_id": creatorID,
		"name":       bubbleName,
		"timestamp":  time.Now(),
	}
	eventJSON, err := json.Marshal(event)
	if err != nil {
		rlog.Error("failed to marshal bubble creation event", "error", err)
		return
	}
	topic := fmt.Sprintf("bubbles/%s", bubbleID)
	if _, err := realtime.PublishMQTT(ctx, &realtime.PublishParams{
		Topic:   topic,
		Payload: string(eventJSON),
		QoS:     1,
	}); err != nil {
		rlog.Error("failed to publish bubble creation event", "error", err)
	}
}

func (s *Service) sendBubbleInvitationNotification(userID, bubbleID, bubbleName string, inviterName *string) {
	ctx := context.Background()
	
	inviterDisplayName := "Someone"
	if inviterName != nil {
		inviterDisplayName = *inviterName
	}

	// Send notification through all channels
	_, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
		UserID:    userID,
		Type:      "bubble_invitation",
		Title:     "New Bubble Invitation!",
		Message:   fmt.Sprintf("%s has invited you to join '%s'.", inviterDisplayName, bubbleName),
		Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: "bubble_invitation", CustomString: inviterDisplayName},
		SendInApp: true,
		SendPush:  true,
		SendEmail: false, // Don't send email for invitations
	})
	
	if err != nil {
		rlog.Error("failed to send bubble invitation notification", "error", err)
	}
}

//encore:api auth method=POST path=/bubble/:bubbleID/leave
func (s *Service) LeaveBubble(ctx context.Context, bubbleID string) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Get bubble name for notifications
	var bubbleName string
	err := db.QueryRow(ctx, `
		SELECT name FROM bubbles WHERE id = $1
	`, bubbleID).Scan(&bubbleName)
	if err != nil {
		return fmt.Errorf("failed to get bubble name: %v", err)
	}

	// Update member status to left
	_, err = db.Exec(ctx, `
		UPDATE bubble_members
		SET status = $1, left_at = $2, updated_at = $2
		WHERE bubble_id = $3 AND user_id = $4 AND status = $5
	`, string(BubbleMembershipStatusLeft), time.Now(), bubbleID, userID, string(BubbleMembershipStatusJoined))
	if err != nil {
		return fmt.Errorf("failed to leave bubble: %v", err)
	}

	// Publish event about member leaving
	go s.publishBubbleMemberLeftEvent(bubbleID, userID, bubbleName)

	// Notify other bubble members
	go s.notifyBubbleMembersAboutMemberLeaving(bubbleID, userID, "", bubbleName)

	return nil
}

func (s *Service) publishBubbleMemberLeftEvent(bubbleID, userID, bubbleName string) {
	ctx := context.Background()
	userObj, err := user.GetByID(ctx, userID)
	if err != nil {
		rlog.Error("failed to get user info for left member", "error", err)
		return
	}
	displayName := userID
	if userObj.Name != nil {
		displayName = *userObj.Name
	}

	event := map[string]interface{}{
		"event_type": "member_left",
		"bubble_id":  bubbleID,
		"user_id":    userID,
		"user_name":  displayName,
		"timestamp":  time.Now(),
	}
	eventJSON, err := json.Marshal(event)
	if err != nil {
		rlog.Error("failed to marshal member left event", "error", err)
		return
	}

	topic := fmt.Sprintf("bubbles/%s", bubbleID)
	if _, err := realtime.PublishMQTT(ctx, &realtime.PublishParams{Topic: topic, Payload: string(eventJSON), QoS: 1}); err != nil {
		rlog.Error("failed to publish member left event", "error", err)
	}

	go s.notifyBubbleMembersAboutMemberLeaving(bubbleID, userID, displayName, bubbleName)
}

func (s *Service) notifyBubbleMembersAboutMemberLeaving(bubbleID, leftMemberID, leftMemberName, bubbleName string) {
	ctx := context.Background()
	rows, err := db.Query(ctx, `SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND user_id != $2 AND status = 'joined'`, bubbleID, leftMemberID)
	if err != nil {
		rlog.Error("failed to get members to notify about member leaving", "error", err)
		return
	}
	defer rows.Close()

	var memberIDs []string
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			rlog.Error("failed to scan member ID", "error", err)
			continue
		}
		memberIDs = append(memberIDs, memberID)
	}

	for _, memberID := range memberIDs {
		if _, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
			UserID:    memberID,
			Type:      "member_left",
			Title:     fmt.Sprintf("Member Left: %s", bubbleName),
			Message:   fmt.Sprintf("%s has left the bubble.", leftMemberName),
			Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: "member_left", CustomString: leftMemberName},
			SendInApp: true,
			SendPush:  true,
			SendEmail: false,
		}); err != nil {
			rlog.Error("failed to send notification for member leaving", "error", err, "user_id", memberID)
		}
	}
}

//encore:api auth method=POST path=/bubble/:bubbleID/call
func (s *Service) UpdateBubbleCall(ctx context.Context, bubbleID string, params *UpdateBubbleCallParams) (*UpdateBubbleCallResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Get bubble name for notifications
	var bubbleName string
	err := db.QueryRow(ctx, `
		SELECT name FROM bubbles WHERE id = $1
	`, bubbleID).Scan(&bubbleName)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble name: %v", err)
	}

	callStatus := "started"
	if params.CallID == nil {
		callStatus = "ended"
	}

	// Update bubble's active call
	_, err = db.Exec(ctx, `
		UPDATE bubbles
		SET active_call_id = $1, updated_at = $2
		WHERE id = $3
	`, params.CallID, time.Now(), bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to update bubble call: %v", err)
	}

	// Publish event about call status change
	go s.publishBubbleCallStatusEvent(bubbleID, userID, bubbleName, callStatus, params.CallID, params.WithVideo, params.WithScreen)

	// Notify bubble members about call
	go s.notifyBubbleMembersAboutCall(bubbleID, userID, "", bubbleName, callStatus, params.CallID, params.WithVideo, params.WithScreen)

	callID := ""
	if params.CallID != nil {
		callID = *params.CallID
	}

	return &UpdateBubbleCallResponse{
		Success:    true,
		BubbleID:   bubbleID,
		BubbleName: bubbleName,
		CallID:     callID,
		CallStatus: callStatus,
	}, nil
}

func (s *Service) publishBubbleCallStatusEvent(bubbleID, userID, bubbleName, callStatus string, callID *string, withVideo, withScreen bool) {
	ctx := context.Background()
	userObj, err := user.GetByID(ctx, userID)
	if err != nil {
		rlog.Error("failed to get user info for call event", "error", err)
		return
	}
	displayName := userID
	if userObj.Name != nil {
		displayName = *userObj.Name
	}

	event := map[string]interface{}{
		"event_type":  "call_status_update",
		"bubble_id":   bubbleID,
		"user_id":     userID,
		"user_name":   displayName,
		"call_id":     callID,
		"status":      callStatus,
		"with_video":  withVideo,
		"with_screen": withScreen,
		"timestamp":   time.Now(),
	}
	eventJSON, err := json.Marshal(event)
	if err != nil {
		rlog.Error("failed to marshal call status event", "error", err)
		return
	}

	topic := fmt.Sprintf("bubbles/%s", bubbleID)
	if _, err := realtime.PublishMQTT(ctx, &realtime.PublishParams{Topic: topic, Payload: string(eventJSON), QoS: 1}); err != nil {
		rlog.Error("failed to publish call status event", "error", err)
	}

	go s.notifyBubbleMembersAboutCall(bubbleID, userID, displayName, bubbleName, callStatus, callID, withVideo, withScreen)
}

func (s *Service) notifyBubbleMembersAboutCall(bubbleID, callerID, callerName, bubbleName, callStatus string, callID *string, withVideo, withScreen bool) {
	ctx := context.Background()
	rows, err := db.Query(ctx, `SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND user_id != $2 AND status = 'joined'`, bubbleID, callerID)
	if err != nil {
		rlog.Error("failed to get members to notify about call", "error", err)
		return
	}
	defer rows.Close()

	var memberIDs []string
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			rlog.Error("failed to scan member ID for call notification", "error", err)
			continue
		}
		memberIDs = append(memberIDs, memberID)
	}

	var title, body string
	switch callStatus {
	case "started":
		title = fmt.Sprintf("Call started in %s", bubbleName)
		body = fmt.Sprintf("%s started a call.", callerName)
	case "ended":
		title = fmt.Sprintf("Call ended in %s", bubbleName)
		body = fmt.Sprintf("The call has ended.")
	default:
		return
	}

	for _, memberID := range memberIDs {
		callIDStr := ""
		if callID != nil {
			callIDStr = *callID
		}
		
		if _, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
			UserID:    memberID,
			Type:      "call_status",
			Title:     title,
			Message:   body,
			Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: callStatus, CustomString: callIDStr},
			SendInApp: true,
			SendPush:  true,
			SendEmail: false,
		}); err != nil {
			rlog.Error("failed to send notification for call status", "error", err, "user_id", memberID)
		}

		// Skip FCM token check for now - user.Get needs proper type
		// member, err := user.Get(ctx, &user.GetParams{ID: memberID})
		// if err != nil || member.FCMToken == nil || *member.FCMToken == "" {
		// 	continue
		// }
		// if _, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
		// 	UserID:    memberID,
		// 	Type:      "call_status",
		// 	Title:     title,
		// 	Message:   body,
		// 	Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: callStatus, CustomString: callIDStr},
		// 	Token:     *member.FCMToken,
		// 	SendPush:  true,
		// 	SendEmail: false,
		// }); err != nil {
		// 	rlog.Error("failed to send push notification for call status", "error", err, "user_id", memberID)
		// }
	}
}

//encore:api auth method=POST path=/bubble/:bubbleID/join-requests/:requestID/respond
func (s *Service) RespondToJoinRequest(ctx context.Context, bubbleID string, requestID string, params *RespondToJoinRequestParams) (*RespondToJoinRequestResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// TODO: Implement join request response logic
	
	return &RespondToJoinRequestResponse{
		Success:  true,
		BubbleID: bubbleID,
		UserID:   userID,
		Status:   "responded",
	}, nil
}

//encore:api auth method=POST path=/bubble/:bubbleID/join
func (s *Service) RequestJoinBubble(ctx context.Context, bubbleID string, params *RequestJoinBubbleParams) (*RequestJoinBubbleResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// TODO: Implement join request logic
	_ = userID // Placeholder to avoid unused variable error
	
	return &RequestJoinBubbleResponse{
		Success:       true,
		BubbleID:      bubbleID,
		BubbleName:    "Sample Bubble",
		RequestID:     "request_123",
		RequestStatus: "pending",
	}, nil
}

//encore:api auth method=POST path=/bubble/:bubbleID/invite
func (s *Service) InviteToBubble(ctx context.Context, bubbleID string, params *InviteToBubbleParams) (*InviteToBubbleResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	inviterID := string(uid)

	// TODO: Implement invitation logic
	_ = inviterID // Placeholder to avoid unused variable error
	
	return &InviteToBubbleResponse{
		Success:    true,
		BubbleID:   bubbleID,
		BubbleName: "Sample Bubble",
		InviteeID:  params.UserID,
	}, nil
}

//encore:api auth method=POST path=/bubble/:bubbleID/archive
func (s *Service) ArchiveBubble(ctx context.Context, bubbleID string) (*ArchiveBubbleResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// TODO: Implement archive logic
	_ = userID // Placeholder to avoid unused variable error
	
	return &ArchiveBubbleResponse{
		Success:    true,
		BubbleID:   bubbleID,
		BubbleName: "Sample Bubble",
	}, nil
}

// Helper functions

func (s *Service) getBubbleMembers(ctx context.Context, bubbleID string) ([]BubbleMember, error) {
	rows, err := db.Query(ctx, `
		SELECT bubble_id, user_id, status, joined_at, left_at, color_code, role, created_at, updated_at, name, profile_picture_url
		FROM bubble_members
		WHERE bubble_id = $1
	`, bubbleID)
	if err != nil {
		return nil, fmt.Errorf("failed to get bubble members: %v", err)
	}
	defer rows.Close()

	var members []BubbleMember
	for rows.Next() {
		var member BubbleMember
		err := rows.Scan(
			&member.BubbleID, &member.UserID, &member.Status, &member.JoinedAt,
			&member.LeftAt, &member.ColorCode, &member.Role, &member.CreatedAt,
			&member.UpdatedAt, &member.Name, &member.ProfilePictureURL,
		)
		if err != nil {
			continue
		}
		members = append(members, member)
	}
	return members, nil
}

func (s *Service) getBubbleInfo(ctx context.Context, bubbleID string) (*BubbleInfo, error) {
	var info BubbleInfo
	err := db.QueryRow(ctx, `
		SELECT bubble_id, location_name, location_lat, location_lng, interest_tags, custom_image_url, last_activity_at, message_count, media_count
		FROM bubble_info
		WHERE bubble_id = $1
	`, bubbleID).Scan(
		&info.BubbleID, &info.LocationName, &info.LocationLat, &info.LocationLng,
		&info.InterestTags, &info.CustomImageURL, &info.LastActivityAt,
		&info.MessageCount, &info.MediaCount,
	)
	if err != nil {
		return nil, err
	}
	return &info, nil
}

// CheckExpiredBubbles checks for expired bubbles and archives them.
// It runs once every hour.
var _ = cron.NewJob("check-expired-bubbles", cron.JobConfig{
	Title:    "Check for expired bubbles",
	Every:    1 * cron.Hour,
	Endpoint: CheckExpiredBubbles,
})

// Defines a cron job that runs every 24 hours to send bubble reminders.
var _ = cron.NewJob("send-bubble-reminders", cron.JobConfig{
	Title:    "Send Bubble Reminders",
	Every:    24 * cron.Hour,
	Endpoint: SendBubbleReminders,

})

// SendBubbleReminders sends reminders for bubbles that are about to expire.
//encore:api private
func SendBubbleReminders(ctx context.Context) error {
	const days = 60
	rows, err := db.Query(ctx, `
		SELECT id, name, expires_at
		FROM bubbles
		WHERE lifecycle_status = $1
		AND expires_at BETWEEN $2 AND $3
	`, BubbleLifecycleStatusActive, time.Now().AddDate(0, 0, days).Add(-24*time.Hour), time.Now().AddDate(0, 0, days))
	if err != nil {
		return fmt.Errorf("failed to query bubbles: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var bubbleID, bubbleName string
		var expiresAt time.Time
		if err := rows.Scan(&bubbleID, &bubbleName, &expiresAt); err != nil {
			rlog.Error("failed to scan bubble row", "error", err)
			continue
		}

		// Get all members of the bubble
		memberRows, err := db.Query(ctx, `
			SELECT user_id
			FROM bubble_members
			WHERE bubble_id = $1 AND status = $2
		`, bubbleID, BubbleMembershipStatusJoined)
		if err != nil {
			rlog.Error("failed to get bubble members", "bubble_id", bubbleID, "error", err)
			continue
		}
		defer memberRows.Close()

		var memberIDs []string
		for memberRows.Next() {
			var memberID string
			if err := memberRows.Scan(&memberID); err != nil {
				rlog.Error("failed to scan member ID", "error", err)
				continue
			}
			memberIDs = append(memberIDs, memberID)
		}

		// Send reminder notifications to all members
		for _, memberID := range memberIDs {
			go notification.SendNotification(ctx, &notification.SendNotificationRequest{
				UserID:    memberID,
				Type:      "bubble_expiry_reminder",
				Title:     fmt.Sprintf("Bubble '%s' will expire soon", bubbleName),
				Message:   fmt.Sprintf("Your bubble '%s' will expire in %d days", bubbleName, days),
				Data:      notification.NotificationData{BubbleID: bubbleID},
				SendInApp: true,
				SendPush:  true,
				SendEmail: false,
			})
		}
	}

	return nil
}





// CheckExpiredBubbles checks for and archives expired bubbles.
//encore:api private
func CheckExpiredBubbles(ctx context.Context) error {
	rows, err := db.Query(ctx, `
		SELECT id, name
		FROM bubbles
		WHERE lifecycle_status = $1 AND expires_at < $2
	`, BubbleLifecycleStatusActive, time.Now())
	if err != nil {
		return fmt.Errorf("failed to query expired bubbles: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var bubbleID, bubbleName string
		if err := rows.Scan(&bubbleID, &bubbleName); err != nil {
			rlog.Error("failed to scan expired bubble", "error", err)
			continue
		}

		// Archive the expired bubble
		_, err = db.Exec(ctx, `
			UPDATE bubbles
			SET lifecycle_status = $1, updated_at = $2
			WHERE id = $3
		`, BubbleLifecycleStatusExpired, time.Now(), bubbleID)
		if err != nil {
			rlog.Error("failed to archive expired bubble", "bubble_id", bubbleID, "error", err)
			continue
		}

		rlog.Info("archived expired bubble", "bubble_id", bubbleID, "name", bubbleName)
	}

	return nil
}

func (s *Service) notifyBubbleMembersAboutJoinRequest(bubbleID, bubbleName, requesterID string, requesterName, requesterEmail, requesterPicURL *string, requestID string) {
	ctx := context.Background()
	rows, err := db.Query(ctx, `SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND status = 'joined'`, bubbleID)
	if err != nil {
		rlog.Error("failed to get members to notify about join request", "error", err)
		return
	}
	defer rows.Close()

	var memberIDs []string
	for rows.Next() {
		var memberID string
		if err := rows.Scan(&memberID); err != nil {
			rlog.Error("failed to scan member ID", "error", err)
			continue
		}
		memberIDs = append(memberIDs, memberID)
	}

	displayName := requesterID
	if requesterName != nil {
		displayName = *requesterName
	}

	for _, memberID := range memberIDs {
		if _, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
			UserID:    memberID,
			Type:      "join_request",
			Title:     fmt.Sprintf("Join Request: %s", bubbleName),
			Message:   fmt.Sprintf("%s wants to join the bubble.", displayName),
			Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: "join_request"},
			SendInApp: true,
			SendPush:  true,
			SendEmail: false,
		}); err != nil {
			rlog.Error("failed to send notification for join request", "error", err, "user_id", memberID)
		}
	}
}

func (s *Service) notifyUserAboutInvitation(bubbleID, bubbleName, userID, inviterID string, inviterName *string) {
	ctx := context.Background()
	displayName := inviterID
	if inviterName != nil {
		displayName = *inviterName
	}

	if _, err := notification.SendNotification(ctx, &notification.SendNotificationRequest{
		UserID:    userID,
		Type:      "bubble_invitation",
		Title:     fmt.Sprintf("Bubble Invitation: %s", bubbleName),
		Message:   fmt.Sprintf("%s invited you to join the bubble.", displayName),
		Data:      notification.NotificationData{BubbleID: bubbleID, ActionType: "bubble_invitation"},
		SendInApp: true,
		SendPush:  true,
		SendEmail: false,
	}); err != nil {
		rlog.Error("failed to send notification for bubble invitation", "error", err)
	}
} 