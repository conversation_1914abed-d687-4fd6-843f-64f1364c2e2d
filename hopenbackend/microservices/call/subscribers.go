package call

import (
	"context"
	"encoding/json"

	"encore.dev/pubsub"
	"encore.dev/rlog"
)

// Global signaling hub instance
var signalingHub *SignalingHub

// Initialize the signaling hub
func init() {
	signalingHub = NewSignalingHub()
	// Start the signaling hub in a goroutine
	go signalingHub.Run(context.Background())
}

// CallEventsSubscriber handles call events
var _ = pubsub.NewSubscription(CallEventsTopic, "call-events-handler", pubsub.SubscriptionConfig{
	Handler: handleCallEvent,
})

// SignalingSubscriber handles signaling messages
var _ = pubsub.NewSubscription(SignalingTopic, "signaling-handler", pubsub.SubscriptionConfig{
	Handler: handleSignalingMessage,
})

// handleCallEvent processes call events
func handleCallEvent(ctx context.Context, event *CallEvent) error {
	rlog.Info("Processing call event", 
		"type", event.Type, 
		"call_id", event.CallID, 
		"user_id", event.UserID)

	switch event.Type {
	case "call_initiated":
		return handleCallInitiated(ctx, event)
	case "user_joined":
		return handleUserJoined(ctx, event)
	case "call_ended":
		return handleCallEnded(ctx, event)
	case "state_changed":
		return handleStateChanged(ctx, event)
	default:
		rlog.Warn("Unknown call event type", "type", event.Type)
	}

	return nil
}

// handleSignalingMessage processes signaling messages
func handleSignalingMessage(ctx context.Context, message *SignalingMessage) error {
	rlog.Info("Processing signaling message", 
		"type", message.Type, 
		"call_id", message.CallID, 
		"from", message.FromUser,
		"to", message.ToUser)

	// Forward signaling message through WebSocket hub
	if message.ToUser != nil {
		// Send to specific user
		signalingHub.SendToUser(message.CallID, *message.ToUser, message)
	} else {
		// Broadcast to all participants except sender
		signalingHub.BroadcastToCall(message.CallID, message, message.FromUser)
	}

	return nil
}

// Call event handlers

func handleCallInitiated(ctx context.Context, event *CallEvent) error {
	// Get call details
	call, err := (&Service{}).getCall(ctx, event.CallID)
	if err != nil {
		return err
	}

	// Get participants
	participants, err := (&Service{}).getCallParticipants(ctx, event.CallID)
	if err != nil {
		return err
	}

	// Send notifications to invited participants
	for _, participant := range participants {
		if participant.Status == "invited" {
			// Create notification data
			notificationData := map[string]interface{}{
				"call_id":     event.CallID,
				"caller_id":   call.CallerID,
				"call_type":   call.Type,
				"with_video":  call.WithVideo,
				"with_audio":  call.WithAudio,
				"with_screen": call.WithScreen,
			}

			// Send in-app notification
			go sendCallNotification(ctx, participant.UserID, "incoming_call", notificationData)

			// Send push notification
			go sendPushNotification(ctx, participant.UserID, "Incoming Call", "You have an incoming call", notificationData)
		}
	}

	return nil
}

func handleUserJoined(ctx context.Context, event *CallEvent) error {
	// Create signaling message for other participants
	joinMessage := &SignalingMessage{
		Type:      "user-joined",
		CallID:    event.CallID,
		FromUser:  event.UserID,
		Data:      event.Data,
		Timestamp: event.Timestamp,
	}

	// Broadcast to other participants
	signalingHub.BroadcastToCall(event.CallID, joinMessage, event.UserID)

	return nil
}

func handleCallEnded(ctx context.Context, event *CallEvent) error {
	// Create signaling message for participants
	endMessage := &SignalingMessage{
		Type:      "call-ended",
		CallID:    event.CallID,
		FromUser:  event.UserID,
		Data:      event.Data,
		Timestamp: event.Timestamp,
	}

	// Broadcast to all participants
	signalingHub.BroadcastToCall(event.CallID, endMessage, "")

	// Clean up WebRTC sessions
	go cleanupWebRTCSessions(ctx, event.CallID)

	return nil
}

func handleStateChanged(ctx context.Context, event *CallEvent) error {
	// Create signaling message for state change
	stateMessage := &SignalingMessage{
		Type:      "state-changed",
		CallID:    event.CallID,
		FromUser:  event.UserID,
		Data:      event.Data,
		Timestamp: event.Timestamp,
	}

	// Broadcast to other participants
	signalingHub.BroadcastToCall(event.CallID, stateMessage, event.UserID)

	return nil
}

// Helper functions

func sendCallNotification(ctx context.Context, userID, notificationType string, data map[string]interface{}) {
	// This would integrate with the notification service
	// For now, just log the notification
	rlog.Info("Sending call notification", 
		"user_id", userID, 
		"type", notificationType, 
		"data", data)

	// TODO: Integrate with notification microservice
	// notification.SendInAppNotification(ctx, userID, notificationType, data)
}

func sendPushNotification(ctx context.Context, userID, title, body string, data map[string]interface{}) {
	// This would integrate with the notification service for push notifications
	rlog.Info("Sending push notification", 
		"user_id", userID, 
		"title", title, 
		"body", body, 
		"data", data)

	// TODO: Integrate with notification microservice
	// notification.SendPushNotification(ctx, userID, title, body, data)
}

func cleanupWebRTCSessions(ctx context.Context, callID string) {
	// Clean up WebRTC sessions for ended call
	_, err := callDB.Exec(ctx, `
		UPDATE webrtc_sessions 
		SET connection_state = 'closed', updated_at = NOW()
		WHERE call_id = $1 AND connection_state != 'closed'
	`, callID)
	if err != nil {
		rlog.Error("Failed to cleanup WebRTC sessions", "error", err, "call_id", callID)
	} else {
		rlog.Info("Cleaned up WebRTC sessions", "call_id", callID)
	}
} 