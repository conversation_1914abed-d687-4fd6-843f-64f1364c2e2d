package call

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"encore.dev/rlog"
	"github.com/gorilla/websocket"
)

// SignalingHub manages WebSocket connections for WebRTC signaling
type SignalingHub struct {
	clients    map[string]*SignalingClient // callID -> client map
	register   chan *SignalingClient
	unregister chan *SignalingClient
	broadcast  chan *SignalingBroadcast
	mutex      sync.RWMutex
}

// SignalingClient represents a WebSocket client for signaling
type SignalingClient struct {
	ID     string
	CallID string
	UserID string
	Conn   *websocket.Conn
	Send   chan *SignalingMessage
	Hub    *SignalingHub
}

// SignalingBroadcast represents a message to broadcast to call participants
type SignalingBroadcast struct {
	CallID  string
	Message *SignalingMessage
	Exclude string // UserID to exclude from broadcast
}

// WebSocket upgrader
var upgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		// Allow all origins for development
		// In production, implement proper origin checking
		return true
	},
}

// NewSignalingHub creates a new signaling hub
func NewSignalingHub() *SignalingHub {
	return &SignalingHub{
		clients:    make(map[string]*SignalingClient),
		register:   make(chan *SignalingClient),
		unregister: make(chan *SignalingClient),
		broadcast:  make(chan *SignalingBroadcast),
	}
}

// Run starts the signaling hub
func (h *SignalingHub) Run(ctx context.Context) {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case broadcast := <-h.broadcast:
			h.broadcastMessage(broadcast)

		case <-ctx.Done():
			h.shutdown()
			return
		}
	}
}

// ServeWS handles WebSocket upgrade for signaling
func (h *SignalingHub) ServeWS(w http.ResponseWriter, r *http.Request, callID, userID string) {
	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		rlog.Error("WebSocket upgrade failed", "error", err)
		return
	}

	client := &SignalingClient{
		ID:     fmt.Sprintf("%s_%s", callID, userID),
		CallID: callID,
		UserID: userID,
		Conn:   conn,
		Send:   make(chan *SignalingMessage, 256),
		Hub:    h,
	}

	h.register <- client

	// Start client goroutines
	go client.writePump()
	go client.readPump()
}

// BroadcastToCall sends a message to all participants in a call
func (h *SignalingHub) BroadcastToCall(callID string, message *SignalingMessage, excludeUser string) {
	broadcast := &SignalingBroadcast{
		CallID:  callID,
		Message: message,
		Exclude: excludeUser,
	}
	h.broadcast <- broadcast
}

// SendToUser sends a message to a specific user in a call
func (h *SignalingHub) SendToUser(callID, userID string, message *SignalingMessage) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	clientID := fmt.Sprintf("%s_%s", callID, userID)
	if client, exists := h.clients[clientID]; exists {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.clients, clientID)
		}
	}
}

// Client methods

func (c *SignalingClient) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(512 * 1024) // 512KB
	c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	for {
		var message SignalingMessage
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				rlog.Error("WebSocket read error", "error", err)
			}
			break
		}

		// Set message metadata
		message.CallID = c.CallID
		message.FromUser = c.UserID
		message.Timestamp = time.Now()

		// Handle the signaling message
		c.handleSignalingMessage(&message)
	}
}

func (c *SignalingClient) writePump() {
	ticker := time.NewTicker(54 * time.Second)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteJSON(message); err != nil {
				rlog.Error("WebSocket write error", "error", err)
				return
			}

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(10 * time.Second))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (c *SignalingClient) handleSignalingMessage(message *SignalingMessage) {
	rlog.Info("Received signaling message", 
		"type", message.Type, 
		"call_id", message.CallID, 
		"from", message.FromUser,
		"to", message.ToUser)

	switch message.Type {
	case "offer", "answer":
		// Handle SDP offer/answer
		c.handleSDPMessage(message)
	case "ice-candidate":
		// Handle ICE candidate
		c.handleICECandidate(message)
	case "join":
		// Handle user joining call
		c.handleJoin(message)
	case "leave":
		// Handle user leaving call
		c.handleLeave(message)
	case "state-change":
		// Handle media state changes (video/audio/screen)
		c.handleStateChange(message)
	default:
		rlog.Warn("Unknown signaling message type", "type", message.Type)
	}
}

func (c *SignalingClient) handleSDPMessage(message *SignalingMessage) {
	// Store SDP in database for persistence
	ctx := context.Background()
	
	if message.Type == "offer" {
		// Store local SDP for the sender
		_, err := callDB.Exec(ctx, `
			INSERT INTO webrtc_sessions (id, call_id, user_id, peer_id, local_sdp, connection_state, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, 'connecting', NOW(), NOW())
			ON CONFLICT (call_id, user_id, peer_id) 
			DO UPDATE SET local_sdp = $5, connection_state = 'connecting', updated_at = NOW()
		`, generateSessionID(), c.CallID, c.UserID, getTargetPeerID(message), getSDP(message))
		if err != nil {
			rlog.Error("Failed to store offer SDP", "error", err)
		}
	} else if message.Type == "answer" {
		// Store remote SDP for the receiver
		_, err := callDB.Exec(ctx, `
			UPDATE webrtc_sessions 
			SET remote_sdp = $1, connection_state = 'connecting', updated_at = NOW()
			WHERE call_id = $2 AND user_id = $3 AND peer_id = $4
		`, getSDP(message), c.CallID, getTargetPeerID(message), c.UserID)
		if err != nil {
			rlog.Error("Failed to store answer SDP", "error", err)
		}
	}

	// Forward message to target user or broadcast to all
	if message.ToUser != nil {
		c.Hub.SendToUser(c.CallID, *message.ToUser, message)
	} else {
		c.Hub.BroadcastToCall(c.CallID, message, c.UserID)
	}
}

func (c *SignalingClient) handleICECandidate(message *SignalingMessage) {
	// Store ICE candidate in database
	ctx := context.Background()
	
	candidateJSON, _ := json.Marshal(message.Data)
	_, err := callDB.Exec(ctx, `
		UPDATE webrtc_sessions 
		SET ice_candidates = ice_candidates || $1::jsonb, updated_at = NOW()
		WHERE call_id = $2 AND user_id = $3
	`, string(candidateJSON), c.CallID, c.UserID)
	if err != nil {
		rlog.Error("Failed to store ICE candidate", "error", err)
	}

	// Forward ICE candidate to target user or broadcast
	if message.ToUser != nil {
		c.Hub.SendToUser(c.CallID, *message.ToUser, message)
	} else {
		c.Hub.BroadcastToCall(c.CallID, message, c.UserID)
	}
}

func (c *SignalingClient) handleJoin(message *SignalingMessage) {
	// Notify other participants that user joined
	joinMessage := &SignalingMessage{
		Type:      "user-joined",
		CallID:    c.CallID,
		FromUser:  c.UserID,
		Data:      message.Data,
		Timestamp: time.Now(),
	}
	c.Hub.BroadcastToCall(c.CallID, joinMessage, c.UserID)
}

func (c *SignalingClient) handleLeave(message *SignalingMessage) {
	// Notify other participants that user left
	leaveMessage := &SignalingMessage{
		Type:      "user-left",
		CallID:    c.CallID,
		FromUser:  c.UserID,
		Data:      message.Data,
		Timestamp: time.Now(),
	}
	c.Hub.BroadcastToCall(c.CallID, leaveMessage, c.UserID)
}

func (c *SignalingClient) handleStateChange(message *SignalingMessage) {
	// Broadcast state change to other participants
	stateMessage := &SignalingMessage{
		Type:      "state-changed",
		CallID:    c.CallID,
		FromUser:  c.UserID,
		Data:      message.Data,
		Timestamp: time.Now(),
	}
	c.Hub.BroadcastToCall(c.CallID, stateMessage, c.UserID)
}

// Hub internal methods

func (h *SignalingHub) registerClient(client *SignalingClient) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client.ID] = client
	rlog.Info("Client registered for signaling", "client_id", client.ID, "call_id", client.CallID, "user_id", client.UserID)

	// Send welcome message
	welcome := &SignalingMessage{
		Type: "welcome",
		Data: map[string]interface{}{
			"client_id": client.ID,
			"call_id":   client.CallID,
			"user_id":   client.UserID,
		},
		Timestamp: time.Now(),
	}
	client.Send <- welcome
}

func (h *SignalingHub) unregisterClient(client *SignalingClient) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, exists := h.clients[client.ID]; exists {
		delete(h.clients, client.ID)
		close(client.Send)
		rlog.Info("Client unregistered from signaling", "client_id", client.ID, "call_id", client.CallID, "user_id", client.UserID)

		// Notify other participants that user disconnected
		disconnectMessage := &SignalingMessage{
			Type:      "user-disconnected",
			CallID:    client.CallID,
			FromUser:  client.UserID,
			Timestamp: time.Now(),
		}
		h.BroadcastToCall(client.CallID, disconnectMessage, client.UserID)
	}
}

func (h *SignalingHub) broadcastMessage(broadcast *SignalingBroadcast) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for clientID, client := range h.clients {
		if client.CallID == broadcast.CallID && client.UserID != broadcast.Exclude {
			select {
			case client.Send <- broadcast.Message:
			default:
				close(client.Send)
				delete(h.clients, clientID)
			}
		}
	}
}

func (h *SignalingHub) shutdown() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	for _, client := range h.clients {
		close(client.Send)
		client.Conn.Close()
	}
}

// Helper functions

func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}

func getTargetPeerID(message *SignalingMessage) string {
	if message.ToUser != nil {
		return *message.ToUser
	}
	return "broadcast"
}

func getSDP(message *SignalingMessage) string {
	if sdp, ok := message.Data["sdp"].(string); ok {
		return sdp
	}
	if offer, ok := message.Data["offer"].(map[string]interface{}); ok {
		if sdp, ok := offer["sdp"].(string); ok {
			return sdp
		}
	}
	if answer, ok := message.Data["answer"].(map[string]interface{}); ok {
		if sdp, ok := answer["sdp"].(string); ok {
			return sdp
		}
	}
	return ""
} 