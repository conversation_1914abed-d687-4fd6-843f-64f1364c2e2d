package call

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/pubsub"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
)

// Database for call service
var callDB = sqldb.NewDatabase("call", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

// PubSub topics for call events
var CallEventsTopic = pubsub.NewTopic[*CallEvent]("call-events", pubsub.TopicConfig{
	DeliveryGuarantee: pubsub.AtLeastOnce,
})

var SignalingTopic = pubsub.NewTopic[*SignalingMessage]("call-signaling", pubsub.TopicConfig{
	DeliveryGuarantee: pubsub.AtLeastOnce,
})

//encore:service
type Service struct{}

// Call represents a call session
type Call struct {
	ID           string    `json:"id"`
	CallerID     string    `json:"caller_id"`
	CalleeID     *string   `json:"callee_id,omitempty"`
	BubbleID     *string   `json:"bubble_id,omitempty"`
	Type         string    `json:"type"` // "direct", "group"
	Status       string    `json:"status"` // "initiating", "ringing", "active", "ended", "declined", "missed"
	WithVideo    bool      `json:"with_video"`
	WithAudio    bool      `json:"with_audio"`
	WithScreen   bool      `json:"with_screen"`
	StartedAt    time.Time `json:"started_at"`
	EndedAt      *time.Time `json:"ended_at,omitempty"`
	Duration     *int      `json:"duration,omitempty"` // in seconds
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// CallParticipant represents a participant in a call
type CallParticipant struct {
	ID           string    `json:"id"`
	CallID       string    `json:"call_id"`
	UserID       string    `json:"user_id"`
	Status       string    `json:"status"` // "invited", "joined", "left", "declined"
	WithVideo    bool      `json:"with_video"`
	WithAudio    bool      `json:"with_audio"`
	WithScreen   bool      `json:"with_screen"`
	JoinedAt     *time.Time `json:"joined_at,omitempty"`
	LeftAt       *time.Time `json:"left_at,omitempty"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// WebRTC Session for peer connection management
type WebRTCSession struct {
	ID               string    `json:"id"`
	CallID           string    `json:"call_id"`
	UserID           string    `json:"user_id"`
	PeerID           string    `json:"peer_id"`
	LocalSDP         *string   `json:"local_sdp,omitempty"`
	RemoteSDP        *string   `json:"remote_sdp,omitempty"`
	ICECandidates    []string  `json:"ice_candidates"`
	ConnectionState  string    `json:"connection_state"` // "new", "connecting", "connected", "disconnected", "failed", "closed"
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// Call Event for PubSub
type CallEvent struct {
	Type     string                 `json:"type"`
	CallID   string                 `json:"call_id"`
	UserID   string                 `json:"user_id"`
	Data     map[string]interface{} `json:"data"`
	Timestamp time.Time             `json:"timestamp"`
}

// Signaling Message for WebRTC
type SignalingMessage struct {
	Type     string                 `json:"type"` // "offer", "answer", "ice-candidate", "join", "leave", "state-change"
	CallID   string                 `json:"call_id"`
	FromUser string                 `json:"from_user"`
	ToUser   *string                `json:"to_user,omitempty"` // null for broadcast
	Data     map[string]interface{} `json:"data"`
	Timestamp time.Time             `json:"timestamp"`
}

// API Request/Response Types

// InitiateCallParams for starting a new call
type InitiateCallParams struct {
	CalleeID   *string `json:"callee_id,omitempty"`   // For direct calls
	BubbleID   *string `json:"bubble_id,omitempty"`   // For group calls
	WithVideo  bool    `json:"with_video"`
	WithAudio  bool    `json:"with_audio"`
	WithScreen bool    `json:"with_screen"`
}

// InitiateCallResponse returns call details
type InitiateCallResponse struct {
	Call         Call              `json:"call"`
	Participants []CallParticipant `json:"participants"`
	SignalingURL string            `json:"signaling_url"`
}

// JoinCallParams for joining an existing call
type JoinCallParams struct {
	WithVideo  bool `json:"with_video"`
	WithAudio  bool `json:"with_audio"`
	WithScreen bool `json:"with_screen"`
}

// JoinCallResponse returns updated call state
type JoinCallResponse struct {
	Call         Call              `json:"call"`
	Participants []CallParticipant `json:"participants"`
	SignalingURL string            `json:"signaling_url"`
}

// EndCallResponse confirms call termination
type EndCallResponse struct {
	CallID   string `json:"call_id"`
	Duration int    `json:"duration"` // in seconds
	EndedAt  time.Time `json:"ended_at"`
}

// UpdateCallStateParams for updating call media state
type UpdateCallStateParams struct {
	WithVideo  *bool `json:"with_video,omitempty"`
	WithAudio  *bool `json:"with_audio,omitempty"`
	WithScreen *bool `json:"with_screen,omitempty"`
}

// SignalingParams for WebRTC signaling
type SignalingParams struct {
	Type     string                 `json:"type"`
	ToUser   *string                `json:"to_user,omitempty"`
	Data     map[string]interface{} `json:"data"`
}

// GetActiveCallsResponse returns user's active calls
type GetActiveCallsResponse struct {
	Calls []Call `json:"calls"`
}

// GetCallHistoryParams for call history
type GetCallHistoryParams struct {
	Limit  *int `json:"limit,omitempty"`
	Offset *int `json:"offset,omitempty"`
}

// GetCallHistoryResponse returns call history
type GetCallHistoryResponse struct {
	Calls []Call `json:"calls"`
	Total int    `json:"total"`
}

// Health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}

// API Endpoints

// Health check endpoint
//encore:api public method=GET path=/call/health
func (s *Service) Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:  "healthy",
		Service: "call",
		Version: "1.0.0",
	}, nil
}

// Initiate a new call (direct or group)
//encore:api auth method=POST path=/call/initiate
func (s *Service) InitiateCall(ctx context.Context, params *InitiateCallParams) (*InitiateCallResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	callerID := string(uid)

	// Validate parameters
	if params.CalleeID == nil && params.BubbleID == nil {
		return nil, fmt.Errorf("either callee_id or bubble_id must be provided")
	}
	if params.CalleeID != nil && params.BubbleID != nil {
		return nil, fmt.Errorf("cannot specify both callee_id and bubble_id")
	}

	// Determine call type
	callType := "direct"
	if params.BubbleID != nil {
		callType = "group"
	}

	// Create call record
	callID := generateCallID()
	now := time.Now()
	
	call := Call{
		ID:        callID,
		CallerID:  callerID,
		CalleeID:  params.CalleeID,
		BubbleID:  params.BubbleID,
		Type:      callType,
		Status:    "initiating",
		WithVideo: params.WithVideo,
		WithAudio: params.WithAudio,
		WithScreen: params.WithScreen,
		StartedAt: now,
		CreatedAt: now,
		UpdatedAt: now,
	}

	// Insert call into database
	_, err := callDB.Exec(ctx, `
		INSERT INTO calls (id, caller_id, callee_id, bubble_id, type, status, with_video, with_audio, with_screen, started_at, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
	`, call.ID, call.CallerID, call.CalleeID, call.BubbleID, call.Type, call.Status, 
	   call.WithVideo, call.WithAudio, call.WithScreen, call.StartedAt, call.CreatedAt, call.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create call: %v", err)
	}

	// Create participants
	participants := []CallParticipant{}
	
	// Add caller as participant
	callerParticipant := CallParticipant{
		ID:        generateParticipantID(),
		CallID:    callID,
		UserID:    callerID,
		Status:    "joined",
		WithVideo: params.WithVideo,
		WithAudio: params.WithAudio,
		WithScreen: params.WithScreen,
		JoinedAt:  &now,
		CreatedAt: now,
		UpdatedAt: now,
	}
	participants = append(participants, callerParticipant)

	// Add callee or bubble members as participants
	if callType == "direct" {
		calleeParticipant := CallParticipant{
			ID:        generateParticipantID(),
			CallID:    callID,
			UserID:    *params.CalleeID,
			Status:    "invited",
			WithVideo: false, // Will be set when they join
			WithAudio: false,
			WithScreen: false,
			CreatedAt: now,
			UpdatedAt: now,
		}
		participants = append(participants, calleeParticipant)
	} else {
		// For group calls, get bubble members
		bubbleMembers, err := s.getBubbleMembers(ctx, *params.BubbleID)
		if err != nil {
			return nil, fmt.Errorf("failed to get bubble members: %v", err)
		}

		for _, memberID := range bubbleMembers {
			if memberID != callerID { // Don't add caller twice
				participant := CallParticipant{
					ID:        generateParticipantID(),
					CallID:    callID,
					UserID:    memberID,
					Status:    "invited",
					WithVideo: false,
					WithAudio: false,
					WithScreen: false,
					CreatedAt: now,
					UpdatedAt: now,
				}
				participants = append(participants, participant)
			}
		}
	}

	// Insert participants
	for _, participant := range participants {
		_, err := callDB.Exec(ctx, `
			INSERT INTO call_participants (id, call_id, user_id, status, with_video, with_audio, with_screen, joined_at, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		`, participant.ID, participant.CallID, participant.UserID, participant.Status,
		   participant.WithVideo, participant.WithAudio, participant.WithScreen, participant.JoinedAt, participant.CreatedAt, participant.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to create participant: %v", err)
		}
	}

	// Publish call event
	go s.publishCallEvent("call_initiated", callID, callerID, map[string]interface{}{
		"call_type": callType,
		"with_video": params.WithVideo,
		"with_audio": params.WithAudio,
		"with_screen": params.WithScreen,
	})

	// Send notifications to invited participants
	go s.notifyCallParticipants(ctx, callID, "call_invitation", participants)

	return &InitiateCallResponse{
		Call:         call,
		Participants: participants,
		SignalingURL: fmt.Sprintf("ws://localhost:4000/call/%s/signaling", callID),
	}, nil
}

// Join an existing call
//encore:api auth method=POST path=/call/:callID/join
func (s *Service) JoinCall(ctx context.Context, callID string, params *JoinCallParams) (*JoinCallResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Get call details
	call, err := s.getCall(ctx, callID)
	if err != nil {
		return nil, fmt.Errorf("call not found: %v", err)
	}

	if call.Status == "ended" {
		return nil, fmt.Errorf("call has already ended")
	}

	// Update participant status
	now := time.Now()
	_, err = callDB.Exec(ctx, `
		UPDATE call_participants 
		SET status = 'joined', with_video = $1, with_audio = $2, with_screen = $3, joined_at = $4, updated_at = $5
		WHERE call_id = $6 AND user_id = $7
	`, params.WithVideo, params.WithAudio, params.WithScreen, now, now, callID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to update participant: %v", err)
	}

	// Update call status to active if it was initiating
	if call.Status == "initiating" {
		_, err = callDB.Exec(ctx, `
			UPDATE calls SET status = 'active', updated_at = $1 WHERE id = $2
		`, now, callID)
		if err != nil {
			return nil, fmt.Errorf("failed to update call status: %v", err)
		}
		call.Status = "active"
	}

	// Get updated participants
	participants, err := s.getCallParticipants(ctx, callID)
	if err != nil {
		return nil, fmt.Errorf("failed to get participants: %v", err)
	}

	// Publish call event
	go s.publishCallEvent("user_joined", callID, userID, map[string]interface{}{
		"with_video": params.WithVideo,
		"with_audio": params.WithAudio,
		"with_screen": params.WithScreen,
	})

	return &JoinCallResponse{
		Call:         *call,
		Participants: participants,
		SignalingURL: fmt.Sprintf("ws://localhost:4000/call/%s/signaling", callID),
	}, nil
}

// Leave/End a call
//encore:api auth method=POST path=/call/:callID/end
func (s *Service) EndCall(ctx context.Context, callID string) (*EndCallResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Get call details
	call, err := s.getCall(ctx, callID)
	if err != nil {
		return nil, fmt.Errorf("call not found: %v", err)
	}

	if call.Status == "ended" {
		return nil, fmt.Errorf("call has already ended")
	}

	now := time.Now()
	duration := int(now.Sub(call.StartedAt).Seconds())

	// Update call status
	_, err = callDB.Exec(ctx, `
		UPDATE calls SET status = 'ended', ended_at = $1, duration = $2, updated_at = $3 WHERE id = $4
	`, now, duration, now, callID)
	if err != nil {
		return nil, fmt.Errorf("failed to end call: %v", err)
	}

	// Update participant status
	_, err = callDB.Exec(ctx, `
		UPDATE call_participants SET status = 'left', left_at = $1, updated_at = $2 
		WHERE call_id = $3 AND user_id = $4 AND status = 'joined'
	`, now, now, callID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to update participant: %v", err)
	}

	// Publish call event
	go s.publishCallEvent("call_ended", callID, userID, map[string]interface{}{
		"duration": duration,
	})

	return &EndCallResponse{
		CallID:   callID,
		Duration: duration,
		EndedAt:  now,
	}, nil
}

// Update call state (video/audio/screen)
//encore:api auth method=PUT path=/call/:callID/state
func (s *Service) UpdateCallState(ctx context.Context, callID string, params *UpdateCallStateParams) (*CallParticipant, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Build update query dynamically
	updates := []string{}
	args := []interface{}{}
	argIndex := 1

	if params.WithVideo != nil {
		updates = append(updates, fmt.Sprintf("with_video = $%d", argIndex))
		args = append(args, *params.WithVideo)
		argIndex++
	}
	if params.WithAudio != nil {
		updates = append(updates, fmt.Sprintf("with_audio = $%d", argIndex))
		args = append(args, *params.WithAudio)
		argIndex++
	}
	if params.WithScreen != nil {
		updates = append(updates, fmt.Sprintf("with_screen = $%d", argIndex))
		args = append(args, *params.WithScreen)
		argIndex++
	}

	if len(updates) == 0 {
		return nil, fmt.Errorf("no state changes provided")
	}

	now := time.Now()
	updates = append(updates, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, now)
	argIndex++

	// Add WHERE conditions
	args = append(args, callID, userID)
	
	query := fmt.Sprintf(`
		UPDATE call_participants SET %s 
		WHERE call_id = $%d AND user_id = $%d
	`, strings.Join(updates, ", "), argIndex-1, argIndex)

	_, err := callDB.Exec(ctx, query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update call state: %v", err)
	}

	// Get updated participant
	participant, err := s.getCallParticipant(ctx, callID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated participant: %v", err)
	}

	// Publish state change event
	go s.publishCallEvent("state_changed", callID, userID, map[string]interface{}{
		"with_video": participant.WithVideo,
		"with_audio": participant.WithAudio,
		"with_screen": participant.WithScreen,
	})

	return participant, nil
}

// WebRTC Signaling endpoint
//encore:api auth method=POST path=/call/:callID/signal
func (s *Service) SendSignalingMessage(ctx context.Context, callID string, params *SignalingParams) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	// Validate user is participant in the call
	participant, err := s.getCallParticipant(ctx, callID, userID)
	if err != nil {
		return fmt.Errorf("user not in call: %v", err)
	}
	if participant.Status != "joined" {
		return fmt.Errorf("user not joined to call")
	}

	// Create signaling message
	message := &SignalingMessage{
		Type:      params.Type,
		CallID:    callID,
		FromUser:  userID,
		ToUser:    params.ToUser,
		Data:      params.Data,
		Timestamp: time.Now(),
	}

	// Publish signaling message
	_, err = SignalingTopic.Publish(ctx, message)
	if err != nil {
		return fmt.Errorf("failed to send signaling message: %v", err)
	}

	rlog.Info("Signaling message sent", "call_id", callID, "from", userID, "type", params.Type)
	return nil
}

// Get active calls for user
//encore:api auth method=GET path=/call/active
func (s *Service) GetActiveCalls(ctx context.Context) (*GetActiveCallsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	rows, err := callDB.Query(ctx, `
		SELECT c.id, c.caller_id, c.callee_id, c.bubble_id, c.type, c.status, 
		       c.with_video, c.with_audio, c.with_screen, c.started_at, c.ended_at, c.duration, c.created_at, c.updated_at
		FROM calls c
		JOIN call_participants cp ON c.id = cp.call_id
		WHERE cp.user_id = $1 AND c.status IN ('initiating', 'ringing', 'active')
		ORDER BY c.started_at DESC
	`, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active calls: %v", err)
	}
	defer rows.Close()

	calls := []Call{}
	for rows.Next() {
		var call Call
		err := rows.Scan(&call.ID, &call.CallerID, &call.CalleeID, &call.BubbleID, &call.Type, &call.Status,
			&call.WithVideo, &call.WithAudio, &call.WithScreen, &call.StartedAt, &call.EndedAt, &call.Duration, &call.CreatedAt, &call.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan call: %v", err)
		}
		calls = append(calls, call)
	}

	return &GetActiveCallsResponse{Calls: calls}, nil
}

// Get call history
//encore:api auth method=GET path=/call/history
func (s *Service) GetCallHistory(ctx context.Context, params *GetCallHistoryParams) (*GetCallHistoryResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	userID := string(uid)

	limit := 20
	offset := 0
	if params.Limit != nil {
		limit = *params.Limit
	}
	if params.Offset != nil {
		offset = *params.Offset
	}

	// Get total count
	var total int
	err := callDB.QueryRow(ctx, `
		SELECT COUNT(*)
		FROM calls c
		JOIN call_participants cp ON c.id = cp.call_id
		WHERE cp.user_id = $1
	`, userID).Scan(&total)
	if err != nil {
		return nil, fmt.Errorf("failed to get call count: %v", err)
	}

	// Get calls
	rows, err := callDB.Query(ctx, `
		SELECT c.id, c.caller_id, c.callee_id, c.bubble_id, c.type, c.status, 
		       c.with_video, c.with_audio, c.with_screen, c.started_at, c.ended_at, c.duration, c.created_at, c.updated_at
		FROM calls c
		JOIN call_participants cp ON c.id = cp.call_id
		WHERE cp.user_id = $1
		ORDER BY c.started_at DESC
		LIMIT $2 OFFSET $3
	`, userID, limit, offset)
	if err != nil {
		return nil, fmt.Errorf("failed to get call history: %v", err)
	}
	defer rows.Close()

	calls := []Call{}
	for rows.Next() {
		var call Call
		err := rows.Scan(&call.ID, &call.CallerID, &call.CalleeID, &call.BubbleID, &call.Type, &call.Status,
			&call.WithVideo, &call.WithAudio, &call.WithScreen, &call.StartedAt, &call.EndedAt, &call.Duration, &call.CreatedAt, &call.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to scan call: %v", err)
		}
		calls = append(calls, call)
	}

	return &GetCallHistoryResponse{
		Calls: calls,
		Total: total,
	}, nil
}

// Helper functions

func (s *Service) getCall(ctx context.Context, callID string) (*Call, error) {
	var call Call
	err := callDB.QueryRow(ctx, `
		SELECT id, caller_id, callee_id, bubble_id, type, status, with_video, with_audio, with_screen, 
		       started_at, ended_at, duration, created_at, updated_at
		FROM calls WHERE id = $1
	`, callID).Scan(&call.ID, &call.CallerID, &call.CalleeID, &call.BubbleID, &call.Type, &call.Status,
		&call.WithVideo, &call.WithAudio, &call.WithScreen, &call.StartedAt, &call.EndedAt, &call.Duration, &call.CreatedAt, &call.UpdatedAt)
	if err != nil {
		return nil, err
	}
	return &call, nil
}

func (s *Service) getCallParticipants(ctx context.Context, callID string) ([]CallParticipant, error) {
	rows, err := callDB.Query(ctx, `
		SELECT id, call_id, user_id, status, with_video, with_audio, with_screen, joined_at, left_at, created_at, updated_at
		FROM call_participants WHERE call_id = $1
	`, callID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	participants := []CallParticipant{}
	for rows.Next() {
		var participant CallParticipant
		err := rows.Scan(&participant.ID, &participant.CallID, &participant.UserID, &participant.Status,
			&participant.WithVideo, &participant.WithAudio, &participant.WithScreen, &participant.JoinedAt, &participant.LeftAt, &participant.CreatedAt, &participant.UpdatedAt)
		if err != nil {
			return nil, err
		}
		participants = append(participants, participant)
	}
	return participants, nil
}

func (s *Service) getCallParticipant(ctx context.Context, callID, userID string) (*CallParticipant, error) {
	var participant CallParticipant
	err := callDB.QueryRow(ctx, `
		SELECT id, call_id, user_id, status, with_video, with_audio, with_screen, joined_at, left_at, created_at, updated_at
		FROM call_participants WHERE call_id = $1 AND user_id = $2
	`, callID, userID).Scan(&participant.ID, &participant.CallID, &participant.UserID, &participant.Status,
		&participant.WithVideo, &participant.WithAudio, &participant.WithScreen, &participant.JoinedAt, &participant.LeftAt, &participant.CreatedAt, &participant.UpdatedAt)
	if err != nil {
		return nil, err
	}
	return &participant, nil
}

func (s *Service) getBubbleMembers(ctx context.Context, bubbleID string) ([]string, error) {
	// This would integrate with the bubble service
	// For now, return a placeholder implementation
	rows, err := callDB.Query(ctx, `
		SELECT user_id FROM bubble_members WHERE bubble_id = $1 AND status = 'joined'
	`, bubbleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	members := []string{}
	for rows.Next() {
		var memberID string
		err := rows.Scan(&memberID)
		if err != nil {
			return nil, err
		}
		members = append(members, memberID)
	}
	return members, nil
}

func (s *Service) publishCallEvent(eventType, callID, userID string, data map[string]interface{}) {
	event := &CallEvent{
		Type:      eventType,
		CallID:    callID,
		UserID:    userID,
		Data:      data,
		Timestamp: time.Now(),
	}

	ctx := context.Background()
	_, err := CallEventsTopic.Publish(ctx, event)
	if err != nil {
		rlog.Error("Failed to publish call event", "error", err, "event_type", eventType, "call_id", callID)
	}
}

func (s *Service) notifyCallParticipants(ctx context.Context, callID, notificationType string, participants []CallParticipant) {
	// This would integrate with the notification service
	// Implementation would send push notifications, in-app notifications, etc.
	for _, participant := range participants {
		if participant.Status == "invited" {
			rlog.Info("Sending call notification", "user_id", participant.UserID, "call_id", callID, "type", notificationType)
			// TODO: Call notification service
		}
	}
}

func generateCallID() string {
	return fmt.Sprintf("call_%d", time.Now().UnixNano())
}

func generateParticipantID() string {
	return fmt.Sprintf("participant_%d", time.Now().UnixNano())
} 