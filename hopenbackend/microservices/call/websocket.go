package call

import (
	"net/http"
	"strings"

	"encore.dev/beta/auth"
	"encore.dev/rlog"
)

// WebSocket endpoint for call signaling
//encore:api raw auth method=GET path=/call/:callID/signaling
func (s *Service) CallSignaling(w http.ResponseWriter, req *http.Request) {
	// Extract call ID from URL path
	pathParts := strings.Split(req.URL.Path, "/")
	if len(pathParts) < 4 {
		http.Error(w, "Invalid call ID", http.StatusBadRequest)
		return
	}
	callID := pathParts[2] // /call/{callID}/signaling

	// Get authenticated user
	uid, ok := auth.UserID()
	if !ok {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}
	userID := string(uid)

	// Validate user is participant in the call
	ctx := req.Context()
	participant, err := s.getCallParticipant(ctx, callID, userID)
	if err != nil {
		rlog.Error("User not found in call", "error", err, "call_id", callID, "user_id", userID)
		http.Error(w, "User not in call", http.StatusForbidden)
		return
	}

	if participant.Status != "joined" && participant.Status != "invited" {
		http.Error(w, "User not authorized for call", http.StatusForbidden)
		return
	}

	// Upgrade to WebSocket and handle signaling
	rlog.Info("Upgrading to WebSocket for call signaling", "call_id", callID, "user_id", userID)
	signalingHub.ServeWS(w, req, callID, userID)
} 