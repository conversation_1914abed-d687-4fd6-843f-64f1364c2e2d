-- Create calls table
CREATE TABLE calls (
    id VARCHAR(255) PRIMARY KEY,
    caller_id VARCHAR(255) NOT NULL,
    callee_id VARCHAR(255),
    bubble_id VARCHAR(255),
    type VARCHAR(50) NOT NULL CHECK (type IN ('direct', 'group')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('initiating', 'ringing', 'active', 'ended', 'declined', 'missed')),
    with_video BOOLEAN NOT NULL DEFAULT false,
    with_audio BOOLEAN NOT NULL DEFAULT true,
    with_screen BOOLEAN NOT NULL DEFAULT false,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration INTEGER, -- in seconds
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT calls_callee_or_bubble CHECK (
        (callee_id IS NOT NULL AND bubble_id IS NULL) OR 
        (callee_id IS NULL AND bubble_id IS NOT NULL)
    )
);

-- Create call_participants table
CREATE TABLE call_participants (
    id VARCHAR(255) PRIMARY KEY,
    call_id VARCHAR(255) NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL CHECK (status IN ('invited', 'joined', 'left', 'declined')),
    with_video BOOLEAN NOT NULL DEFAULT false,
    with_audio BOOLEAN NOT NULL DEFAULT true,
    with_screen BOOLEAN NOT NULL DEFAULT false,
    joined_at TIMESTAMP WITH TIME ZONE,
    left_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Unique constraint to prevent duplicate participants
    UNIQUE(call_id, user_id)
);

-- Create webrtc_sessions table for peer connection management
CREATE TABLE webrtc_sessions (
    id VARCHAR(255) PRIMARY KEY,
    call_id VARCHAR(255) NOT NULL REFERENCES calls(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL,
    peer_id VARCHAR(255) NOT NULL,
    local_sdp TEXT,
    remote_sdp TEXT,
    ice_candidates JSONB DEFAULT '[]'::jsonb,
    connection_state VARCHAR(50) NOT NULL DEFAULT 'new' CHECK (connection_state IN ('new', 'connecting', 'connected', 'disconnected', 'failed', 'closed')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Unique constraint for peer connections
    UNIQUE(call_id, user_id, peer_id)
);

-- Create indexes for better performance
CREATE INDEX idx_calls_caller_id ON calls(caller_id);
CREATE INDEX idx_calls_callee_id ON calls(callee_id);
CREATE INDEX idx_calls_bubble_id ON calls(bubble_id);
CREATE INDEX idx_calls_status ON calls(status);
CREATE INDEX idx_calls_started_at ON calls(started_at);

CREATE INDEX idx_call_participants_call_id ON call_participants(call_id);
CREATE INDEX idx_call_participants_user_id ON call_participants(user_id);
CREATE INDEX idx_call_participants_status ON call_participants(status);

CREATE INDEX idx_webrtc_sessions_call_id ON webrtc_sessions(call_id);
CREATE INDEX idx_webrtc_sessions_user_id ON webrtc_sessions(user_id);
CREATE INDEX idx_webrtc_sessions_connection_state ON webrtc_sessions(connection_state);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_calls_updated_at BEFORE UPDATE ON calls
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_call_participants_updated_at BEFORE UPDATE ON call_participants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_webrtc_sessions_updated_at BEFORE UPDATE ON webrtc_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 