package user

import (
	"context"
	"fmt"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/storage/sqldb"
)

// This is the service definition for the 'user' service.
// It manages user profiles, preferences, and device tokens for notifications.
// Learn more about services: https://encore.dev/docs/primitives/services-and-apis

// The 'user' database is defined here. Encore manages the connection and migrations.
// Learn more about databases: https://encore.dev/docs/primitives/databases
var userDB = sqldb.NewDatabase("user", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

//encore:service
type Service struct{}

// ServiceInfo provides information about the user service.
type ServiceInfo struct {
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data"`
}

// Info returns information about the user service.
//encore:api public method=GET path=/api/user/
func Info(ctx context.Context) (*ServiceInfo, error) {
	return &ServiceInfo{
		Message: "User service ready",
		Data: map[string]interface{}{
			"description": "User profile management and authentication",
			"endpoints": []string{
				"GET /users/by-id/:id",
				"GET /users/profile/:userID",
				"POST /users/fcm-token",
			},
		},
	}, nil
}

// User defines the structure for a user in the database.
type User struct {
	ID                   string     `json:"id"`
	Email                string     `json:"email"`
	FirstName            *string    `json:"first_name"`
	LastName             *string    `json:"last_name"`
	Username             *string    `json:"username"`
	Name                 *string    `json:"name"`
	PhoneNumber          *string    `json:"phone_number"`
	ProfilePictureURL    *string    `json:"profile_picture_url"`
	AvatarURL            *string    `json:"avatar_url"`
	StatusMessage        *string    `json:"status_message"`
	PrivacySettings      *string    `json:"privacy_settings"`
	NotificationSettings *string    `json:"notification_settings"`
	IsOnline             bool       `json:"is_online"`
	LastSeen             *time.Time `json:"last_seen"`
	CreatedAt            time.Time  `json:"created_at"`
	UpdatedAt            time.Time  `json:"updated_at"`
}

// GetUserParams defines the parameters for the Get endpoint.
type GetUserParams struct {
	ID string `json:"id"`
}

// Get retrieves a user by their ID.
//encore:api public method=GET path=/users/by-id/:id
func Get(ctx context.Context, id string) (*User, error) {
	var u User
	err := userDB.QueryRow(ctx, `
		SELECT id, email, first_name, last_name, username, name, phone_number,
		       profile_picture_url, avatar_url, status_message, privacy_settings,
		       notification_settings, is_online, last_seen, created_at, updated_at
		FROM users
		WHERE id = $1
	`, id).Scan(
		&u.ID, &u.Email, &u.FirstName, &u.LastName, &u.Username, &u.Name, &u.PhoneNumber,
		&u.ProfilePictureURL, &u.AvatarURL, &u.StatusMessage, &u.PrivacySettings,
		&u.NotificationSettings, &u.IsOnline, &u.LastSeen, &u.CreatedAt, &u.UpdatedAt,
	)
	return &u, err
}

// GetByID retrieves a user by their ID (alias for Get for compatibility).
func GetByID(ctx context.Context, id string) (*User, error) {
	return Get(ctx, id)
}

// Me retrieves the currently authenticated user's profile.
//encore:api auth
func Me(ctx context.Context) (*User, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("not authenticated")
	}
	return Get(ctx, string(uid))
}

// CreateParams defines the parameters for creating a user.
type CreateParams struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Username  string `json:"username,omitempty"`
}

// Create creates a new user.
// This is an internal endpoint that should only be called from the auth service upon signup.
//encore:api private path=/user/create
func Create(ctx context.Context, p *CreateParams) (*User, error) {
	_, err := userDB.Exec(ctx, `
		INSERT INTO users (id, email, first_name, last_name, username)
		VALUES ($1, $2, $3, $4, $5)
	`, p.ID, p.Email, nullIfEmpty(p.FirstName), nullIfEmpty(p.LastName), nullIfEmpty(p.Username))
	if err != nil {
		return nil, fmt.Errorf("could not create user: %w", err)
	}
	return Get(ctx, p.ID)
}

// CreateOrUpdateParams defines the parameters for creating or updating a user.
type CreateOrUpdateParams struct {
	ID        string `json:"id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Username  string `json:"username,omitempty"`
	AvatarURL string `json:"avatar_url,omitempty"`
}

// CreateOrUpdate creates a new user or updates an existing one.
// This is used for OAuth sign-ins where the user might already exist.
//encore:api private path=/user/create-or-update
func CreateOrUpdate(ctx context.Context, p *CreateOrUpdateParams) (*User, error) {
	_, err := userDB.Exec(ctx, `
		INSERT INTO users (id, email, first_name, last_name, username, avatar_url)
		VALUES ($1, $2, $3, $4, $5, $6)
		ON CONFLICT (id) DO UPDATE SET
			email = EXCLUDED.email,
			first_name = COALESCE(EXCLUDED.first_name, users.first_name),
			last_name = COALESCE(EXCLUDED.last_name, users.last_name),
			username = COALESCE(EXCLUDED.username, users.username),
			avatar_url = COALESCE(EXCLUDED.avatar_url, users.avatar_url),
			updated_at = NOW()
	`, p.ID, p.Email, nullIfEmpty(p.FirstName), nullIfEmpty(p.LastName), nullIfEmpty(p.Username), nullIfEmpty(p.AvatarURL))
	if err != nil {
		return nil, fmt.Errorf("could not create or update user: %w", err)
	}
	return Get(ctx, p.ID)
}

// nullIfEmpty returns nil if the string is empty, otherwise returns the string pointer
func nullIfEmpty(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}

// GetProfileParams defines the parameters for the GetUserProfile endpoint.
type GetProfileParams struct {
	UserID string `path:"userID"`
}

// GetUserProfile retrieves the profile for a given user ID.
//encore:api public method=GET path=/users/profile/:userID
func GetUserProfile(ctx context.Context, userID string) (*User, error) {
	return Get(ctx, userID)
}

// UpdateFCMTokenRequest defines the request body for updating an FCM token.
type UpdateFCMTokenRequest struct {
	UserID    string `json:"user_id"`
	FCMToken  string `json:"fcm_token"`
	DeviceID  string `json:"device_id"`
}

// UpdateFCMToken registers or updates a device's FCM token for push notifications.
// This endpoint would typically be protected by auth.
//encore:api public method=POST path=/users/fcm-token
func UpdateFCMToken(ctx context.Context, req *UpdateFCMTokenRequest) error {
	_, err := userDB.Exec(ctx, `
		INSERT INTO fcm_tokens (user_id, token, device_id)
		VALUES ($1, $2, $3)
		ON CONFLICT (user_id, device_id) DO UPDATE SET token = EXCLUDED.token, last_used_at = NOW()
	`, req.UserID, req.FCMToken, req.DeviceID)
	return err
}

// GetFCMTokensParams defines the parameters for the GetFCMTokens endpoint.
type GetFCMTokensParams struct {
	UserID string `json:"user_id"`
}

// GetFCMTokensResponse defines the response for getting FCM tokens.
type GetFCMTokensResponse struct {
	Tokens []string `json:"tokens"`
}

// GetFCMTokens retrieves all FCM tokens for a given user ID.
// This is an internal API, meant to be called by other services like the notification service.
//encore:api private path=/user/fcm-tokens
func GetFCMTokens(ctx context.Context, params *GetFCMTokensParams) (*GetFCMTokensResponse, error) {
	rows, err := userDB.Query(ctx, `
		SELECT token FROM fcm_tokens WHERE user_id = $1
	`, params.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var tokens []string
	for rows.Next() {
		var token string
		if err := rows.Scan(&token); err != nil {
			return nil, err
		}
		tokens = append(tokens, token)
	}

	return &GetFCMTokensResponse{Tokens: tokens}, nil
}

// CheckEmailAvailabilityParams defines the parameters for checking email availability.
type CheckEmailAvailabilityParams struct {
	Email string `json:"email"`
}

// CheckEmailAvailabilityResponse defines the response for email availability check.
type CheckEmailAvailabilityResponse struct {
	Available bool   `json:"available"`
	Message   string `json:"message"`
}

// CheckEmailAvailability checks if an email address is available for registration.
//encore:api public method=POST path=/users/check-email
func CheckEmailAvailability(ctx context.Context, params *CheckEmailAvailabilityParams) (*CheckEmailAvailabilityResponse, error) {
	if params.Email == "" {
		return &CheckEmailAvailabilityResponse{
			Available: false,
			Message:   "Email is required",
		}, nil
	}

	// Check if email exists in database
	var exists bool
	err := userDB.QueryRow(ctx, `
		SELECT EXISTS(SELECT 1 FROM users WHERE email = $1)
	`, params.Email).Scan(&exists)
	if err != nil {
		return nil, fmt.Errorf("failed to check email availability: %w", err)
	}

	if exists {
		return &CheckEmailAvailabilityResponse{
			Available: false,
			Message:   "This email address is already registered",
		}, nil
	}

	return &CheckEmailAvailabilityResponse{
		Available: true,
		Message:   "Email address is available",
	}, nil
}

// CheckUsernameAvailabilityParams defines the parameters for checking username availability.
type CheckUsernameAvailabilityParams struct {
	Username string `json:"username"`
}

// CheckUsernameAvailabilityResponse defines the response for username availability check.
type CheckUsernameAvailabilityResponse struct {
	Available bool   `json:"available"`
	Message   string `json:"message"`
}

// CheckUsernameAvailability checks if a username is available for registration.
//encore:api public method=POST path=/users/check-username
func CheckUsernameAvailability(ctx context.Context, params *CheckUsernameAvailabilityParams) (*CheckUsernameAvailabilityResponse, error) {
	if params.Username == "" {
		return &CheckUsernameAvailabilityResponse{
			Available: false,
			Message:   "Username is required",
		}, nil
	}

	// Check if username exists in database
	var exists bool
	err := userDB.QueryRow(ctx, `
		SELECT EXISTS(SELECT 1 FROM users WHERE username = $1)
	`, params.Username).Scan(&exists)
	if err != nil {
		return nil, fmt.Errorf("failed to check username availability: %w", err)
	}

	if exists {
		return &CheckUsernameAvailabilityResponse{
			Available: false,
			Message:   "This username is already taken",
		}, nil
	}

	return &CheckUsernameAvailabilityResponse{
		Available: true,
		Message:   "Username is available",
	}, nil
} 