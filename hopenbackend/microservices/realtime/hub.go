// Package realtime provides real-time WebSocket communication hub
// with room management, user presence, and MQTT integration.
package realtime

import (
	"context"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// <PERSON><PERSON> manages WebSocket connections and message routing
type Hub struct {
	config      *Config
	clients     map[string]*Client
	rooms       map[string]*Room
	broadcast   chan *Message
	register    chan *Client
	unregister  chan *Client
	metrics     *WebSocketMetrics
	upgrader    websocket.Upgrader
	middleware  []Middleware
	handlers    map[string]MessageHandler
	presence    *PresenceManager
	mutex       sync.RWMutex
}

// Config holds WebSocket hub configuration
type Config struct {
	ReadBufferSize    int
	WriteBufferSize   int
	HandshakeTimeout  time.Duration
	ReadTimeout       time.Duration
	WriteTimeout      time.Duration
	PongTimeout       time.Duration
	PingPeriod        time.Duration
	MaxMessageSize    int64
	AllowedOrigins    []string
	EnableCompression bool
	EnableMetrics     bool
}

// Client represents a WebSocket client connection
type Client struct {
	ID       string
	UserID   string
	Conn     *websocket.Conn
	Hub      *Hub
	Send     chan *Message
	Rooms    map[string]bool
	Metadata map[string]interface{}
	LastSeen time.Time
	mutex    sync.RWMutex
}

// Room represents a WebSocket room for grouped communication
type Room struct {
	ID          string
	Name        string
	Clients     map[string]*Client
	Metadata    map[string]interface{}
	CreatedAt   time.Time
	LastActive  time.Time
	MessageCount int64
	mutex       sync.RWMutex
}

// Message represents a WebSocket message
type Message struct {
	Type      string                 `json:"type"`
	Room      string                 `json:"room,omitempty"`
	From      string                 `json:"from,omitempty"`
	To        string                 `json:"to,omitempty"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
	ID        string                 `json:"id,omitempty"`
}

// PresenceManager manages user presence information
type PresenceManager struct {
	users     map[string]*UserPresence
	rooms     map[string]map[string]*UserPresence
	mutex     sync.RWMutex
	hub       *Hub
}

// UserPresence represents user presence information
type UserPresence struct {
	UserID    string                 `json:"user_id"`
	Status    string                 `json:"status"` // online, away, busy, offline
	LastSeen  time.Time              `json:"last_seen"`
	Metadata  map[string]interface{} `json:"metadata"`
	Rooms     []string               `json:"rooms"`
}

// WebSocketMetrics holds Prometheus metrics
type WebSocketMetrics struct {
	connectionsTotal    *prometheus.CounterVec
	activeConnections   prometheus.Gauge
	messagesTotal       *prometheus.CounterVec
	messageSize         *prometheus.HistogramVec
	roomsActive         prometheus.Gauge
	presenceUpdates     prometheus.Counter
	errors              *prometheus.CounterVec
	connectionDuration  *prometheus.HistogramVec
}

// MessageHandler defines a function to handle specific message types
type MessageHandler func(*Client, *Message) error

// Middleware defines WebSocket middleware
type Middleware func(*Client, *Message) error

// NewHub creates a new WebSocket hub
func NewHub(config *Config) *Hub {
	if config == nil {
		config = DefaultConfig()
	}

	hub := &Hub{
		config:     config,
		clients:    make(map[string]*Client),
		rooms:      make(map[string]*Room),
		broadcast:  make(chan *Message, 256),
		register:   make(chan *Client),
		unregister: make(chan *Client),
		metrics:    newWebSocketMetrics(),
		handlers:   make(map[string]MessageHandler),
		presence:   NewPresenceManager(),
	}

	// Set up the upgrader after hub is created
	hub.upgrader = websocket.Upgrader{
		ReadBufferSize:    config.ReadBufferSize,
		WriteBufferSize:   config.WriteBufferSize,
		HandshakeTimeout:  config.HandshakeTimeout,
		EnableCompression: config.EnableCompression,
		CheckOrigin:       hub.checkOrigin,
	}

	hub.presence.hub = hub

	// Register default message handlers
	hub.RegisterHandler("join_room", hub.handleJoinRoom)
	hub.RegisterHandler("leave_room", hub.handleLeaveRoom)
	hub.RegisterHandler("typing", hub.handleTyping)
	hub.RegisterHandler("presence", hub.handlePresence)
	hub.RegisterHandler("ping", hub.handlePing)

	return hub
}

// Run starts the WebSocket hub
func (h *Hub) Run(ctx context.Context) {
	go h.presence.Run(ctx)

	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)

		case <-ctx.Done():
			h.shutdown()
			return
		}
	}
}

// ServeWS handles WebSocket upgrade requests
func (h *Hub) ServeWS(w http.ResponseWriter, r *http.Request) {
	// Extract user ID from request (implement your auth logic)
	userID := h.extractUserID(r)
	if userID == "" {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	// Upgrade connection
	conn, err := h.upgrader.Upgrade(w, r, nil)
	if err != nil {
		h.metrics.errors.WithLabelValues("upgrade_failed").Inc()
		return
	}

	// Create client
	client := &Client{
		ID:       generateClientID(),
		UserID:   userID,
		Conn:     conn,
		Hub:      h,
		Send:     make(chan *Message, 256),
		Rooms:    make(map[string]bool),
		Metadata: make(map[string]interface{}),
		LastSeen: time.Now(),
	}

	// Register client
	h.register <- client

	// Start client goroutines
	go client.writePump()
	go client.readPump()
}

// RegisterHandler registers a message handler for a specific message type
func (h *Hub) RegisterHandler(messageType string, handler MessageHandler) {
	h.handlers[messageType] = handler
}

// RegisterMiddleware registers middleware for message processing
func (h *Hub) RegisterMiddleware(middleware Middleware) {
	h.middleware = append(h.middleware, middleware)
}

// BroadcastToRoom sends a message to all clients in a room
func (h *Hub) BroadcastToRoom(roomID string, message *Message) {
	message.Room = roomID
	message.Timestamp = time.Now()
	h.broadcast <- message
}

// SendToUser sends a message to a specific user
func (h *Hub) SendToUser(userID string, message *Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, client := range h.clients {
		if client.UserID == userID {
			select {
			case client.Send <- message:
			default:
				close(client.Send)
				delete(h.clients, client.ID)
			}
		}
	}
}

// Client methods
func (c *Client) readPump() {
	defer func() {
		c.Hub.unregister <- c
		c.Conn.Close()
	}()

	c.Conn.SetReadLimit(c.Hub.config.MaxMessageSize)
	c.Conn.SetReadDeadline(time.Now().Add(c.Hub.config.ReadTimeout))
	c.Conn.SetPongHandler(func(string) error {
		c.Conn.SetReadDeadline(time.Now().Add(c.Hub.config.ReadTimeout))
		return nil
	})

	for {
		var message Message
		err := c.Conn.ReadJSON(&message)
		if err != nil {
			if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
				c.Hub.metrics.errors.WithLabelValues("read_error").Inc()
			}
			break
		}

		c.LastSeen = time.Now()
		message.From = c.UserID
		message.Timestamp = time.Now()

		// Apply middleware
		if err := c.applyMiddleware(&message); err != nil {
			continue
		}

		// Handle message
		if err := c.handleMessage(&message); err != nil {
			c.Hub.metrics.errors.WithLabelValues("handle_error").Inc()
		}

		c.Hub.metrics.messagesTotal.WithLabelValues("received", message.Type).Inc()
	}
}

func (c *Client) writePump() {
	ticker := time.NewTicker(c.Hub.config.PingPeriod)
	defer func() {
		ticker.Stop()
		c.Conn.Close()
	}()

	for {
		select {
		case message, ok := <-c.Send:
			c.Conn.SetWriteDeadline(time.Now().Add(c.Hub.config.WriteTimeout))
			if !ok {
				c.Conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			if err := c.Conn.WriteJSON(message); err != nil {
				c.Hub.metrics.errors.WithLabelValues("write_error").Inc()
				return
			}

			c.Hub.metrics.messagesTotal.WithLabelValues("sent", message.Type).Inc()

		case <-ticker.C:
			c.Conn.SetWriteDeadline(time.Now().Add(c.Hub.config.WriteTimeout))
			if err := c.Conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

func (c *Client) applyMiddleware(message *Message) error {
	for _, middleware := range c.Hub.middleware {
		if err := middleware(c, message); err != nil {
			return err
		}
	}
	return nil
}

func (c *Client) handleMessage(message *Message) error {
	if handler, exists := c.Hub.handlers[message.Type]; exists {
		return handler(c, message)
	}
	return fmt.Errorf("unknown message type: %s", message.Type)
}

// Hub internal methods
func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client.ID] = client
	h.metrics.connectionsTotal.WithLabelValues("connected").Inc()
	h.metrics.activeConnections.Inc()

	// Update presence
	h.presence.UpdateUserPresence(client.UserID, "online", nil)

	// Send welcome message
	welcome := &Message{
		Type: "welcome",
		Data: map[string]interface{}{
			"client_id": client.ID,
			"user_id":   client.UserID,
		},
		Timestamp: time.Now(),
	}
	client.Send <- welcome
}

func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, exists := h.clients[client.ID]; exists {
		delete(h.clients, client.ID)
		close(client.Send)

		// Remove from all rooms
		for roomID := range client.Rooms {
			h.leaveRoom(client, roomID)
		}

		h.metrics.connectionsTotal.WithLabelValues("disconnected").Inc()
		h.metrics.activeConnections.Dec()

		// Update presence
		h.presence.UpdateUserPresence(client.UserID, "offline", nil)
	}
}

func (h *Hub) broadcastMessage(message *Message) {
	if message.Room != "" {
		h.broadcastToRoom(message)
	} else {
		h.broadcastToAll(message)
	}
}

func (h *Hub) broadcastToRoom(message *Message) {
	h.mutex.RLock()
	room, exists := h.rooms[message.Room]
	h.mutex.RUnlock()

	if !exists {
		return
	}

	room.mutex.RLock()
	defer room.mutex.RUnlock()

	for _, client := range room.Clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.clients, client.ID)
		}
	}

	room.MessageCount++
	room.LastActive = time.Now()
}

func (h *Hub) broadcastToAll(message *Message) {
	h.mutex.RLock()
	defer h.mutex.RUnlock()

	for _, client := range h.clients {
		select {
		case client.Send <- message:
		default:
			close(client.Send)
			delete(h.clients, client.ID)
		}
	}
}

// Message handlers
func (h *Hub) handleJoinRoom(client *Client, message *Message) error {
	roomID, ok := message.Data["room_id"].(string)
	if !ok {
		return fmt.Errorf("invalid room_id")
	}

	h.joinRoom(client, roomID)
	return nil
}

func (h *Hub) handleLeaveRoom(client *Client, message *Message) error {
	roomID, ok := message.Data["room_id"].(string)
	if !ok {
		return fmt.Errorf("invalid room_id")
	}

	h.leaveRoom(client, roomID)
	return nil
}

func (h *Hub) handleTyping(client *Client, message *Message) error {
	roomID, ok := message.Data["room_id"].(string)
	if !ok {
		return fmt.Errorf("invalid room_id")
	}

	typing := &Message{
		Type: "typing",
		Room: roomID,
		From: client.UserID,
		Data: map[string]interface{}{
			"user_id": client.UserID,
			"typing":  message.Data["typing"],
		},
		Timestamp: time.Now(),
	}

	h.BroadcastToRoom(roomID, typing)
	return nil
}

func (h *Hub) handlePresence(client *Client, message *Message) error {
	status, ok := message.Data["status"].(string)
	if !ok {
		return fmt.Errorf("invalid status")
	}

	h.presence.UpdateUserPresence(client.UserID, status, message.Data["metadata"])
	return nil
}

func (h *Hub) handlePing(client *Client, message *Message) error {
	pong := &Message{
		Type:      "pong",
		Timestamp: time.Now(),
	}
	client.Send <- pong
	return nil
}

// Room management
func (h *Hub) joinRoom(client *Client, roomID string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	// Create room if it doesn't exist
	room, exists := h.rooms[roomID]
	if !exists {
		room = &Room{
			ID:        roomID,
			Clients:   make(map[string]*Client),
			Metadata:  make(map[string]interface{}),
			CreatedAt: time.Now(),
		}
		h.rooms[roomID] = room
		h.metrics.roomsActive.Inc()
	}

	// Add client to room
	room.mutex.Lock()
	room.Clients[client.ID] = client
	room.LastActive = time.Now()
	room.mutex.Unlock()

	// Add room to client
	client.mutex.Lock()
	client.Rooms[roomID] = true
	client.mutex.Unlock()

	// Notify room of new member
	notification := &Message{
		Type: "user_joined",
		Room: roomID,
		Data: map[string]interface{}{
			"user_id": client.UserID,
		},
		Timestamp: time.Now(),
	}
	h.BroadcastToRoom(roomID, notification)
}

func (h *Hub) leaveRoom(client *Client, roomID string) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	room, exists := h.rooms[roomID]
	if !exists {
		return
	}

	// Remove client from room
	room.mutex.Lock()
	delete(room.Clients, client.ID)
	isEmpty := len(room.Clients) == 0
	room.mutex.Unlock()

	// Remove room from client
	client.mutex.Lock()
	delete(client.Rooms, roomID)
	client.mutex.Unlock()

	// Clean up empty room
	if isEmpty {
		delete(h.rooms, roomID)
		h.metrics.roomsActive.Dec()
	} else {
		// Notify room of member leaving
		notification := &Message{
			Type: "user_left",
			Room: roomID,
			Data: map[string]interface{}{
				"user_id": client.UserID,
			},
			Timestamp: time.Now(),
		}
		h.BroadcastToRoom(roomID, notification)
	}
}

// Helper functions
func (h *Hub) checkOrigin(r *http.Request) bool {
	if len(h.config.AllowedOrigins) == 0 {
		return true // Allow all origins if none specified
	}

	origin := r.Header.Get("Origin")
	for _, allowed := range h.config.AllowedOrigins {
		if origin == allowed {
			return true
		}
	}
	return false
}

func (h *Hub) extractUserID(r *http.Request) string {
	// Extract from JWT token, session, or query parameter
	// This is a simplified implementation
	return r.URL.Query().Get("user_id")
}

func (h *Hub) shutdown() {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	for _, client := range h.clients {
		close(client.Send)
		client.Conn.Close()
	}
}

func generateClientID() string {
	return fmt.Sprintf("client_%d", time.Now().UnixNano())
}

// Presence Manager implementation
func NewPresenceManager() *PresenceManager {
	return &PresenceManager{
		users: make(map[string]*UserPresence),
		rooms: make(map[string]map[string]*UserPresence),
	}
}

func (pm *PresenceManager) Run(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.cleanupStalePresence()
		case <-ctx.Done():
			return
		}
	}
}

func (pm *PresenceManager) UpdateUserPresence(userID, status string, metadata interface{}) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	presence, exists := pm.users[userID]
	if !exists {
		presence = &UserPresence{
			UserID:   userID,
			Metadata: make(map[string]interface{}),
		}
		pm.users[userID] = presence
	}

	presence.Status = status
	presence.LastSeen = time.Now()
	if metadata != nil {
		if meta, ok := metadata.(map[string]interface{}); ok {
			for k, v := range meta {
				presence.Metadata[k] = v
			}
		}
	}

	pm.hub.metrics.presenceUpdates.Inc()

	// Broadcast presence update
	pm.broadcastPresenceUpdate(presence)
}

func (pm *PresenceManager) broadcastPresenceUpdate(presence *UserPresence) {
	message := &Message{
		Type: "presence_update",
		Data: map[string]interface{}{
			"user_id":   presence.UserID,
			"status":    presence.Status,
			"last_seen": presence.LastSeen,
			"metadata":  presence.Metadata,
		},
		Timestamp: time.Now(),
	}

	// Broadcast to all rooms where user is present
	for _, roomID := range presence.Rooms {
		pm.hub.BroadcastToRoom(roomID, message)
	}
}

func (pm *PresenceManager) cleanupStalePresence() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	cutoff := time.Now().Add(-5 * time.Minute)
	for _, presence := range pm.users {
		if presence.LastSeen.Before(cutoff) && presence.Status != "offline" {
			presence.Status = "offline"
			pm.broadcastPresenceUpdate(presence)
		}
	}
}

// newWebSocketMetrics initializes Prometheus metrics
func newWebSocketMetrics() *WebSocketMetrics {
	return &WebSocketMetrics{
		connectionsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_websocket_connections_total",
			Help: "Total number of WebSocket connections",
		}, []string{"status"}),
		activeConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_websocket_active_connections",
			Help: "Number of active WebSocket connections",
		}),
		messagesTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_websocket_messages_total",
			Help: "Total number of WebSocket messages",
		}, []string{"direction", "type"}),
		messageSize: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_websocket_message_size_bytes",
			Help:    "WebSocket message size in bytes",
			Buckets: []float64{64, 256, 1024, 4096, 16384, 65536},
		}, []string{"type"}),
		roomsActive: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_websocket_rooms_active",
			Help: "Number of active WebSocket rooms",
		}),
		presenceUpdates: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_websocket_presence_updates_total",
			Help: "Total number of presence updates",
		}),
		errors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_websocket_errors_total",
			Help: "Total number of WebSocket errors",
		}, []string{"type"}),
		connectionDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_websocket_connection_duration_seconds",
			Help:    "WebSocket connection duration",
			Buckets: []float64{1, 10, 60, 300, 1800, 3600},
		}, []string{"reason"}),
	}
}

// DefaultConfig returns default WebSocket configuration
func DefaultConfig() *Config {
	return &Config{
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		HandshakeTimeout:  10 * time.Second,
		ReadTimeout:       60 * time.Second,
		WriteTimeout:      10 * time.Second,
		PongTimeout:       60 * time.Second,
		PingPeriod:        54 * time.Second,
		MaxMessageSize:    512 * 1024, // 512KB
		AllowedOrigins:    []string{},  // Allow all origins
		EnableCompression: true,
		EnableMetrics:     true,
	}
} 