package realtime

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	mqtt "github.com/eclipse/paho.mqtt.golang"
)

// Service definition for unified realtime microservice
//encore:service
type Service struct {
	mqttClient mqtt.Client
	hub        *Hub
	mu         sync.RWMutex
}

// Initialize the service with MQTT and WebSocket hub
func initService() (*Service, error) {
	// Initialize MQTT client
	opts := mqtt.NewClientOptions().
		AddBroker("tcp://localhost:1883").
		SetClientID("encore-realtime-client").
		SetUsername("encore").
		SetPassword("encore").
		SetCleanSession(true).
		SetAutoReconnect(true).
		SetOnConnectHandler(func(client mqtt.Client) {
			rlog.Info("connected to MQTT broker")
		}).
		SetConnectionLostHandler(func(client mqtt.Client, err error) {
			rlog.Error("lost connection to MQTT broker", "error", err)
		})

	mqttClient := mqtt.NewClient(opts)
	if token := mqttClient.Connect(); token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to connect to MQTT broker: %v", token.Error())
	}

	// Initialize WebSocket hub
	hub := NewHub(DefaultConfig())

	service := &Service{
		mqttClient: mqttClient,
		hub:        hub,
		mu:         sync.RWMutex{},
	}

	// Start the hub in a goroutine
	go func() {
		ctx := context.Background()
		hub.Run(ctx)
	}()

	return service, nil
}

// MQTT Message Types

// MQTT publish parameters
type PublishParams struct {
	Topic   string `json:"topic"`
	Payload string `json:"payload"`
	QoS     byte   `json:"qos"`
}

// MQTT publish response
type PublishResponse struct {
	Success bool `json:"success"`
}

// MQTT subscribe parameters
type SubscribeParams struct {
	Topic string `json:"topic"`
	QoS   byte   `json:"qos"`
}

// MQTT subscribe response
type SubscribeResponse struct {
	Success bool `json:"success"`
}

// MQTT unsubscribe parameters
type UnsubscribeParams struct {
	Topic string `json:"topic"`
}

// MQTT unsubscribe response
type UnsubscribeResponse struct {
	Success bool `json:"success"`
}

// WebSocket Types

// WebSocket connection request parameters
type ConnectParams struct {
	UserID string `json:"user_id"`
	RoomID string `json:"room_id,omitempty"`
}

// WebSocket connection response
type ConnectResponse struct {
	Success      bool   `json:"success"`
	ConnectionID string `json:"connection_id"`
	Message      string `json:"message"`
}

// Room join parameters
type JoinRoomParams struct {
	RoomID string `json:"room_id"`
	UserID string `json:"user_id"`
}

// Room join response
type JoinRoomResponse struct {
	Success bool   `json:"success"`
	RoomID  string `json:"room_id"`
	Message string `json:"message"`
}

// User presence metadata
type PresenceMetadata struct {
	LastActivity string `json:"last_activity,omitempty"`
	DeviceType   string `json:"device_type,omitempty"`
	AppVersion   string `json:"app_version,omitempty"`
	IsTyping     bool   `json:"is_typing,omitempty"`
}

// User presence update parameters
type UpdatePresenceParams struct {
	UserID   string           `json:"user_id"`
	Status   string           `json:"status"` // online, away, busy, offline
	Metadata PresenceMetadata `json:"metadata,omitempty"`
}

// User presence response
type PresenceResponse struct {
	Success bool   `json:"success"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// Health response type
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}

// MQTT API Endpoints

// Publish message to MQTT topic
//encore:api auth method=POST path=/realtime/mqtt/publish
func (s *Service) PublishMQTT(ctx context.Context, params *PublishParams) (*PublishResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, &errs.Error{
			Code:    errs.Unauthenticated,
			Message: "user not authenticated",
		}
	}

	s.mu.RLock()
	client := s.mqttClient
	s.mu.RUnlock()

	if client == nil {
		return nil, &errs.Error{
			Code:    errs.Unavailable,
			Message: "MQTT client not initialized",
		}
	}

	rlog.Info("Publishing MQTT message", "user_id", uid, "topic", params.Topic)

	token := client.Publish(params.Topic, params.QoS, false, params.Payload)
	if token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to publish message: %v", token.Error())
	}

	return &PublishResponse{Success: true}, nil
}

// Subscribe to MQTT topic
//encore:api auth method=POST path=/realtime/mqtt/subscribe
func (s *Service) SubscribeMQTT(ctx context.Context, params *SubscribeParams) (*SubscribeResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, &errs.Error{
			Code:    errs.Unauthenticated,
			Message: "user not authenticated",
		}
	}

	s.mu.RLock()
	client := s.mqttClient
	s.mu.RUnlock()

	if client == nil {
		return nil, &errs.Error{
			Code:    errs.Unavailable,
			Message: "MQTT client not initialized",
		}
	}

	token := client.Subscribe(params.Topic, params.QoS, func(client mqtt.Client, msg mqtt.Message) {
		rlog.Info("received MQTT message",
			"topic", msg.Topic(),
			"payload", string(msg.Payload()),
			"user_id", uid,
		)
		
		// Bridge MQTT messages to WebSocket if needed
		s.bridgeToWebSocket(msg.Topic(), string(msg.Payload()))
	})
	
	if token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to subscribe: %v", token.Error())
	}

	return &SubscribeResponse{Success: true}, nil
}

// Unsubscribe from MQTT topic
//encore:api auth method=POST path=/realtime/mqtt/unsubscribe
func (s *Service) UnsubscribeMQTT(ctx context.Context, params *UnsubscribeParams) (*UnsubscribeResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, &errs.Error{
			Code:    errs.Unauthenticated,
			Message: "user not authenticated",
		}
	}

	s.mu.RLock()
	client := s.mqttClient
	s.mu.RUnlock()

	if client == nil {
		return nil, &errs.Error{
			Code:    errs.Unavailable,
			Message: "MQTT client not initialized",
		}
	}

	rlog.Info("Unsubscribing from MQTT topic", "user_id", uid, "topic", params.Topic)

	token := client.Unsubscribe(params.Topic)
	if token.Wait() && token.Error() != nil {
		return nil, fmt.Errorf("failed to unsubscribe: %v", token.Error())
	}

	return &UnsubscribeResponse{Success: true}, nil
}

// WebSocket API Endpoints

// WebSocket connection endpoint
//encore:api public method=POST path=/realtime/connect
func (s *Service) Connect(ctx context.Context, params *ConnectParams) (*ConnectResponse, error) {
	rlog.Info("WebSocket connection request", "user_id", params.UserID, "room_id", params.RoomID)

	// TODO: Implement actual WebSocket connection logic with hub
	connectionID := fmt.Sprintf("conn_%s", params.UserID)

	return &ConnectResponse{
		Success:      true,
		ConnectionID: connectionID,
		Message:      "WebSocket connection established",
	}, nil
}

// Room management endpoint
//encore:api auth method=POST path=/realtime/rooms/join
func (s *Service) JoinRoom(ctx context.Context, params *JoinRoomParams) (*JoinRoomResponse, error) {
	rlog.Info("User joining room", "user_id", params.UserID, "room_id", params.RoomID)

	// TODO: Implement actual room joining logic with hub

	return &JoinRoomResponse{
		Success: true,
		RoomID:  params.RoomID,
		Message: "Successfully joined room",
	}, nil
}

// User presence management endpoint
//encore:api auth method=POST path=/realtime/presence/update
func (s *Service) UpdatePresence(ctx context.Context, params *UpdatePresenceParams) (*PresenceResponse, error) {
	rlog.Info("Updating user presence", "user_id", params.UserID, "status", params.Status)

	// TODO: Implement actual presence update logic with hub

	return &PresenceResponse{
		Success: true,
		Status:  params.Status,
		Message: "Presence updated successfully",
	}, nil
}

// Health check endpoint
//encore:api public method=GET path=/realtime/health
func (s *Service) Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:  "healthy",
		Service: "realtime",
		Version: "2.0.0", // Updated version after merge
	}, nil
}

// Helper Methods

// Bridge MQTT messages to WebSocket connections
func (s *Service) bridgeToWebSocket(topic, payload string) {
	if s.hub == nil {
		return
	}

	// Convert MQTT topic to room ID (e.g., "bubbles/123" -> "bubble_123")
	roomID := convertTopicToRoom(topic)
	
	message := &Message{
		Type:      "mqtt_bridge",
		Room:      roomID,
		Data:      map[string]interface{}{"payload": payload, "topic": topic},
		Timestamp: getCurrentTime(),
	}

	s.hub.BroadcastToRoom(roomID, message)
}

// Convert MQTT topic to WebSocket room ID
func convertTopicToRoom(topic string) string {
	// Simple conversion: replace "/" with "_"
	// e.g., "bubbles/123" -> "bubbles_123"
	return strings.ReplaceAll(topic, "/", "_")
}

// Get current time helper
func getCurrentTime() time.Time {
	return time.Now()
} 