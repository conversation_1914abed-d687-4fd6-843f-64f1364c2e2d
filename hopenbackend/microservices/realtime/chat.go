package realtime

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"github.com/gocql/gocql"
	"github.com/google/uuid"
)

// Cassandra session for chat history
var cassandraSession *gocql.Session

func initCassandra() error {
	cluster := gocql.NewCluster("127.0.0.1")
	cluster.Keyspace = "hopen_chat"
	cluster.Consistency = gocql.Quorum
	cluster.ProtoVersion = 4
	cluster.ConnectTimeout = time.Second * 10

	var err error
	cassandraSession, err = cluster.CreateSession()
	if err != nil {
		rlog.Error("Failed to connect to <PERSON>", "error", err)
		return err
	}

	// Create keyspace and tables if they don't exist
	err = createCassandraSchema()
	if err != nil {
		rlog.Error("Failed to create Cassandra schema", "error", err)
		return err
	}

	rlog.Info("Connected to <PERSON> successfully")
	return nil
}

func createCassandraSchema() error {
	// Create keyspace
	err := cassandraSession.Query(`
		CREATE KEYSPACE IF NOT EXISTS hopen_chat 
		WITH REPLICATION = {
			'class': 'SimpleStrategy',
			'replication_factor': 1
		}
	`).Exec()
	if err != nil {
		return err
	}

	// Create messages table
	err = cassandraSession.Query(`
		CREATE TABLE IF NOT EXISTS hopen_chat.messages (
			conversation_id UUID,
			message_id UUID,
			sender_id TEXT,
			recipient_id TEXT,
			content TEXT,
			message_type TEXT,
			media_url TEXT,
			media_type TEXT,
			reply_to_id UUID,
			is_edited BOOLEAN,
			is_deleted BOOLEAN,
			created_at TIMESTAMP,
			updated_at TIMESTAMP,
			PRIMARY KEY (conversation_id, created_at, message_id)
		) WITH CLUSTERING ORDER BY (created_at DESC)
	`).Exec()
	if err != nil {
		return err
	}

	// Create conversations table
	err = cassandraSession.Query(`
		CREATE TABLE IF NOT EXISTS hopen_chat.conversations (
			conversation_id UUID PRIMARY KEY,
			conversation_type TEXT,
			participants SET<TEXT>,
			bubble_id TEXT,
			last_message_id UUID,
			last_message_content TEXT,
			last_message_time TIMESTAMP,
			created_at TIMESTAMP,
			updated_at TIMESTAMP
		)
	`).Exec()
	if err != nil {
		return err
	}

	// Create user conversations index
	err = cassandraSession.Query(`
		CREATE TABLE IF NOT EXISTS hopen_chat.user_conversations (
			user_id TEXT,
			conversation_id UUID,
			last_read_time TIMESTAMP,
			is_muted BOOLEAN,
			is_archived BOOLEAN,
			created_at TIMESTAMP,
			PRIMARY KEY (user_id, conversation_id)
		)
	`).Exec()
	if err != nil {
		return err
	}

	return nil
}

// Chat message structures
type ChatMessage struct {
	MessageID      string    `json:"message_id"`
	ConversationID string    `json:"conversation_id"`
	SenderID       string    `json:"sender_id"`
	RecipientID    string    `json:"recipient_id,omitempty"`
	Content        string    `json:"content"`
	MessageType    string    `json:"message_type"` // "text", "image", "file", "audio", "video"
	MediaURL       string    `json:"media_url,omitempty"`
	MediaType      string    `json:"media_type,omitempty"`
	ReplyToID      string    `json:"reply_to_id,omitempty"`
	IsEdited       bool      `json:"is_edited"`
	IsDeleted      bool      `json:"is_deleted"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type Conversation struct {
	ConversationID      string    `json:"conversation_id"`
	ConversationType    string    `json:"conversation_type"` // "direct", "group", "bubble"
	Participants        []string  `json:"participants"`
	BubbleID            string    `json:"bubble_id,omitempty"`
	LastMessageID       string    `json:"last_message_id,omitempty"`
	LastMessageContent  string    `json:"last_message_content,omitempty"`
	LastMessageTime     time.Time `json:"last_message_time,omitempty"`
	CreatedAt           time.Time `json:"created_at"`
	UpdatedAt           time.Time `json:"updated_at"`
}

type UserConversation struct {
	UserID         string    `json:"user_id"`
	ConversationID string    `json:"conversation_id"`
	LastReadTime   time.Time `json:"last_read_time"`
	IsMuted        bool      `json:"is_muted"`
	IsArchived     bool      `json:"is_archived"`
	CreatedAt      time.Time `json:"created_at"`
}

// API Request/Response Types
type SendMessageParams struct {
	ConversationID string `json:"conversation_id,omitempty"`
	RecipientID    string `json:"recipient_id,omitempty"`
	Content        string `json:"content"`
	MessageType    string `json:"message_type"`
	MediaURL       string `json:"media_url,omitempty"`
	MediaType      string `json:"media_type,omitempty"`
	ReplyToID      string `json:"reply_to_id,omitempty"`
}

type SendMessageResponse struct {
	Success bool        `json:"success"`
	Message ChatMessage `json:"message"`
}

type GetMessagesParams struct {
	ConversationID string    `json:"conversation_id"`
	Limit          int       `json:"limit,omitempty"`
	Before         time.Time `json:"before,omitempty"`
}

type GetMessagesResponse struct {
	Messages []ChatMessage `json:"messages"`
	Total    int           `json:"total"`
}

type GetConversationsResponse struct {
	Conversations []Conversation `json:"conversations"`
	Total         int            `json:"total"`
}

type CreateConversationParams struct {
	ConversationType string   `json:"conversation_type"`
	Participants     []string `json:"participants"`
	BubbleID         string   `json:"bubble_id,omitempty"`
}

type CreateConversationResponse struct {
	Success      bool         `json:"success"`
	Conversation Conversation `json:"conversation"`
}

// Chat API Endpoints

// Send a message
//encore:api auth method=POST path=/chat/messages
func (s *Service) SendMessage(ctx context.Context, params *SendMessageParams) (*SendMessageResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	senderID := string(uid)

	var conversationID string
	var recipientID string

	// If conversation ID is provided, use it
	if params.ConversationID != "" {
		conversationID = params.ConversationID
		recipientID = params.RecipientID
	} else if params.RecipientID != "" {
		// Create or find direct conversation
		recipientID = params.RecipientID
		conversationID = s.getOrCreateDirectConversation(ctx, senderID, recipientID)
	} else {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("either conversation_id or recipient_id must be provided").Err()
	}

	// Create message
	messageID := uuid.New().String()
	now := time.Now()

	message := ChatMessage{
		MessageID:      messageID,
		ConversationID: conversationID,
		SenderID:       senderID,
		RecipientID:    recipientID,
		Content:        params.Content,
		MessageType:    params.MessageType,
		MediaURL:       params.MediaURL,
		MediaType:      params.MediaType,
		ReplyToID:      params.ReplyToID,
		IsEdited:       false,
		IsDeleted:      false,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// Store message in PostgreSQL for now (can be migrated to Cassandra later)
	_, err := realtimeDB.Exec(ctx, `
		INSERT INTO chat_messages (message_id, conversation_id, sender_id, recipient_id, content, message_type, media_url, media_type, reply_to_id, is_edited, is_deleted, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
	`, messageID, conversationID, senderID, recipientID, params.Content, params.MessageType, params.MediaURL, params.MediaType, params.ReplyToID, false, false, now, now)

	if err != nil {
		rlog.Error("Failed to save message", "error", err)
		return nil, errs.B().Code(errs.Internal).Msg("failed to save message").Err()
	}

	// Send message via MQTT for real-time delivery
	mqttMessage := map[string]interface{}{
		"type":    "chat_message",
		"message": message,
	}

	mqttPayload, _ := json.Marshal(mqttMessage)
	
	// Send to recipient's personal topic
	if recipientID != "" {
		recipientTopic := fmt.Sprintf("user/%s/messages", recipientID)
		s.mqttClient.Publish(recipientTopic, 1, false, mqttPayload)
	}

	// Send to conversation topic
	conversationTopic := fmt.Sprintf("conversation/%s", conversationID)
	s.mqttClient.Publish(conversationTopic, 1, false, mqttPayload)

	return &SendMessageResponse{
		Success: true,
		Message: message,
	}, nil
}

// Get messages from a conversation
//encore:api auth method=GET path=/chat/conversations/:conversationID/messages
func (s *Service) GetMessages(ctx context.Context, conversationID string, params *GetMessagesParams) (*GetMessagesResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Build query with pagination
	query := `
		SELECT message_id, conversation_id, sender_id, recipient_id, content, message_type, media_url, media_type, reply_to_id, is_edited, is_deleted, created_at, updated_at
		FROM chat_messages 
		WHERE conversation_id = $1 AND is_deleted = false
	`
	args := []interface{}{conversationID}
	argCount := 1

	if !params.Before.IsZero() {
		argCount++
		query += fmt.Sprintf(" AND created_at < $%d", argCount)
		args = append(args, params.Before)
	}

	query += " ORDER BY created_at DESC"

	if params.Limit > 0 {
		argCount++
		query += fmt.Sprintf(" LIMIT $%d", argCount)
		args = append(args, params.Limit)
	} else {
		query += " LIMIT 50" // Default limit
	}

	rows, err := realtimeDB.Query(ctx, query, args...)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to retrieve messages").Err()
	}
	defer rows.Close()

	var messages []ChatMessage
	for rows.Next() {
		var message ChatMessage
		err := rows.Scan(&message.MessageID, &message.ConversationID, &message.SenderID, &message.RecipientID, &message.Content, &message.MessageType, &message.MediaURL, &message.MediaType, &message.ReplyToID, &message.IsEdited, &message.IsDeleted, &message.CreatedAt, &message.UpdatedAt)
		if err != nil {
			continue
		}
		messages = append(messages, message)
	}

	return &GetMessagesResponse{
		Messages: messages,
		Total:    len(messages),
	}, nil
}

// Get user conversations
//encore:api auth method=GET path=/chat/conversations
func (s *Service) GetConversations(ctx context.Context) (*GetConversationsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Get conversations where user is a participant
	rows, err := realtimeDB.Query(ctx, `
		SELECT DISTINCT c.conversation_id, c.conversation_type, c.participants, c.bubble_id, c.last_message_id, c.last_message_content, c.last_message_time, c.created_at, c.updated_at
		FROM conversations c
		WHERE $1 = ANY(c.participants)
		ORDER BY c.last_message_time DESC NULLS LAST, c.created_at DESC
	`, userID)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to retrieve conversations").Err()
	}
	defer rows.Close()

	var conversations []Conversation
	for rows.Next() {
		var conv Conversation
		var participants []string
		err := rows.Scan(&conv.ConversationID, &conv.ConversationType, &participants, &conv.BubbleID, &conv.LastMessageID, &conv.LastMessageContent, &conv.LastMessageTime, &conv.CreatedAt, &conv.UpdatedAt)
		if err != nil {
			continue
		}
		conv.Participants = participants
		conversations = append(conversations, conv)
	}

	return &GetConversationsResponse{
		Conversations: conversations,
		Total:         len(conversations),
	}, nil
}

// Create a new conversation
//encore:api auth method=POST path=/chat/conversations
func (s *Service) CreateConversation(ctx context.Context, params *CreateConversationParams) (*CreateConversationResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	conversationID := uuid.New().String()
	now := time.Now()

	// Add current user to participants if not already included
	participants := params.Participants
	userIncluded := false
	for _, participant := range participants {
		if participant == userID {
			userIncluded = true
			break
		}
	}
	if !userIncluded {
		participants = append(participants, userID)
	}

	conversation := Conversation{
		ConversationID:   conversationID,
		ConversationType: params.ConversationType,
		Participants:     participants,
		BubbleID:         params.BubbleID,
		CreatedAt:        now,
		UpdatedAt:        now,
	}

	// Save conversation
	_, err := realtimeDB.Exec(ctx, `
		INSERT INTO conversations (conversation_id, conversation_type, participants, bubble_id, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`, conversationID, params.ConversationType, participants, params.BubbleID, now, now)

	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to create conversation").Err()
	}

	return &CreateConversationResponse{
		Success:      true,
		Conversation: conversation,
	}, nil
}

// Helper function to get or create direct conversation
func (s *Service) getOrCreateDirectConversation(ctx context.Context, userID1, userID2 string) string {
	// Try to find existing direct conversation
	var conversationID string
	err := realtimeDB.QueryRow(ctx, `
		SELECT conversation_id FROM conversations 
		WHERE conversation_type = 'direct' 
		AND $1 = ANY(participants) 
		AND $2 = ANY(participants)
		AND array_length(participants, 1) = 2
		LIMIT 1
	`, userID1, userID2).Scan(&conversationID)

	if err == nil {
		return conversationID
	}

	// Create new direct conversation
	newConversationID := uuid.New().String()
	now := time.Now()
	participants := []string{userID1, userID2}

	_, err = realtimeDB.Exec(ctx, `
		INSERT INTO conversations (conversation_id, conversation_type, participants, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5)
	`, newConversationID, "direct", participants, now, now)

	if err != nil {
		rlog.Error("Failed to create direct conversation", "error", err)
		return ""
	}

	return newConversationID
} 