-- Create contacts table
CREATE TABLE IF NOT EXISTS contacts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    contact_user_id VARCHAR(255),
    name VA<PERSON>HAR(255) NOT NULL,
    email VARCHAR(255),
    phone_number VA<PERSON>HAR(50),
    image_url TEXT,
    relationship_type VARCHAR(50) NOT NULL DEFAULT 'contact',
    bubble_status VARCHAR(50) DEFAULT 'noBubble',
    is_online BOOLEAN DEFAULT false,
    source VARCHAR(50) NOT NULL DEFAULT 'manual',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create contact requests table
CREATE TABLE IF NOT EXISTS contact_requests (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sender_id VARCHAR(255) NOT NULL,
    recipient_id VARCHAR(255) NOT NULL,
    message TEXT,
    status VARCHAR(50) NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contacts_user_id ON contacts(user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_contact_user_id ON contacts(contact_user_id);
CREATE INDEX IF NOT EXISTS idx_contacts_relationship_type ON contacts(relationship_type);
CREATE INDEX IF NOT EXISTS idx_contacts_bubble_status ON contacts(bubble_status);
CREATE INDEX IF NOT EXISTS idx_contacts_email ON contacts(email);
CREATE INDEX IF NOT EXISTS idx_contacts_phone_number ON contacts(phone_number);

CREATE INDEX IF NOT EXISTS idx_contact_requests_sender_id ON contact_requests(sender_id);
CREATE INDEX IF NOT EXISTS idx_contact_requests_recipient_id ON contact_requests(recipient_id);
CREATE INDEX IF NOT EXISTS idx_contact_requests_status ON contact_requests(status);

-- Create unique constraint to prevent duplicate contacts
CREATE UNIQUE INDEX IF NOT EXISTS idx_contacts_unique_user_contact 
ON contacts(user_id, contact_user_id) 
WHERE contact_user_id IS NOT NULL;

-- Create unique constraint for email/phone combinations
CREATE UNIQUE INDEX IF NOT EXISTS idx_contacts_unique_user_email_phone 
ON contacts(user_id, COALESCE(email, ''), COALESCE(phone_number, ''));

-- Create unique constraint to prevent duplicate contact requests
CREATE UNIQUE INDEX IF NOT EXISTS idx_contact_requests_unique_sender_recipient 
ON contact_requests(sender_id, recipient_id) 
WHERE status = 'pending'; 