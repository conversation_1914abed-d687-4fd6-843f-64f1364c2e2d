package contact

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"encore.app/microservices/notification"
	"encore.app/microservices/user"
)

// Database for contact service
var contactDB = sqldb.NewDatabase("contact", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

//encore:service
type Service struct{}

// Contact represents a contact entry
type Contact struct {
	ID               string    `json:"id"`
	UserID           string    `json:"user_id"`
	ContactUserID    string    `json:"contact_user_id"`
	Name             string    `json:"name"`
	Email            string    `json:"email,omitempty"`
	PhoneNumber      string    `json:"phone_number,omitempty"`
	ImageURL         string    `json:"image_url,omitempty"`
	RelationshipType string    `json:"relationship_type"` // "contact", "none", "blocked"
	BubbleStatus     string    `json:"bubble_status"`     // "noBubble", "notFullBubble", "fullBubble"
	IsOnline         bool      `json:"is_online"`
	Source           string    `json:"source"`     // "phone", "email", "manual", "platform"
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// ContactRequest represents a contact request
type ContactRequest struct {
	ID          string    `json:"id"`
	SenderID    string    `json:"sender_id"`
	RecipientID string    `json:"recipient_id"`
	Message     string    `json:"message,omitempty"`
	Status      string    `json:"status"` // "pending", "accepted", "declined"
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// API Request/Response Types

// SyncContactsParams for syncing phone/email contacts
type SyncContactsParams struct {
	Contacts []ContactSyncData `json:"contacts"`
	Source   string            `json:"source"` // "phone" or "email"
}

type ContactSyncData struct {
	Name        string `json:"name"`
	Email       string `json:"email,omitempty"`
	PhoneNumber string `json:"phone_number,omitempty"`
}

// SyncContactsResponse returns sync results
type SyncContactsResponse struct {
	Success      bool   `json:"success"`
	SyncedCount  int    `json:"synced_count"`
	MatchedCount int    `json:"matched_count"`
	Message      string `json:"message"`
}

// GetContactsParams for retrieving contacts
type GetContactsParams struct {
	Search           string `json:"search,omitempty"`
	RelationshipType string `json:"relationship_type,omitempty"`
	BubbleStatus     string `json:"bubble_status,omitempty"`
	Limit            int    `json:"limit,omitempty"`
	Offset           int    `json:"offset,omitempty"`
}

// GetContactsResponse returns contact list
type GetContactsResponse struct {
	Contacts []Contact `json:"contacts"`
	Total    int       `json:"total"`
}

// SendContactRequestParams for sending contact requests
type SendContactRequestParams struct {
	ContactUserID string `json:"contact_user_id"`
	Message       string `json:"message,omitempty"`
}

// SendContactRequestResponse confirms request sent
type SendContactRequestResponse struct {
	Success   bool   `json:"success"`
	RequestID string `json:"request_id"`
	Message   string `json:"message"`
}

// ContactRequestActionParams for accepting/declining requests
type ContactRequestActionParams struct {
	Action string `json:"action"` // "accept" or "decline"
}

// ContactRequestActionResponse confirms action
type ContactRequestActionResponse struct {
	Success bool   `json:"success"`
	Status  string `json:"status"`
	Message string `json:"message"`
}

// GetContactRequestsResponse returns pending requests
type GetContactRequestsResponse struct {
	Requests []ContactRequest `json:"requests"`
	Total    int              `json:"total"`
}

// UpdateContactParams for updating contact info
type UpdateContactParams struct {
	Name             string `json:"name,omitempty"`
	RelationshipType string `json:"relationship_type,omitempty"`
}

// Health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}

// API Endpoints

// Health check endpoint
//encore:api public method=GET path=/contact/health
func (s *Service) Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:  "healthy",
		Service: "contact",
		Version: "1.0.0",
	}, nil
}

// Sync contacts from phone or email
//encore:api auth method=POST path=/contacts/sync
func (s *Service) SyncContacts(ctx context.Context, params *SyncContactsParams) (*SyncContactsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	rlog.Info("Syncing contacts", "user_id", userID, "source", params.Source, "count", len(params.Contacts))

	syncedCount := 0
	matchedCount := 0

	for _, contactData := range params.Contacts {
		// Store contact (whether matched or not)
		_, err := contactDB.Exec(ctx, `
			INSERT INTO contacts (user_id, contact_user_id, name, email, phone_number, relationship_type, source, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
			ON CONFLICT (user_id, COALESCE(email, ''), COALESCE(phone_number, '')) 
			DO UPDATE SET 
				name = EXCLUDED.name,
				contact_user_id = EXCLUDED.contact_user_id,
				updated_at = NOW()
		`, userID, "", contactData.Name, contactData.Email, contactData.PhoneNumber, "contact", params.Source)

		if err != nil {
			rlog.Error("failed to sync contact", "error", err, "contact", contactData.Name)
			continue
		}
		syncedCount++
	}

	return &SyncContactsResponse{
		Success:      true,
		SyncedCount:  syncedCount,
		MatchedCount: matchedCount,
		Message:      fmt.Sprintf("Synced %d contacts, %d matched with platform users", syncedCount, matchedCount),
	}, nil
}

// Get user contacts with filtering
//encore:api auth method=GET path=/contacts
func (s *Service) GetContacts(ctx context.Context, params *GetContactsParams) (*GetContactsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Build query with filters
	query := `
		SELECT c.id, c.user_id, c.contact_user_id, c.name, c.email, c.phone_number, 
		       c.image_url, c.relationship_type, c.bubble_status, c.is_online, c.source, c.created_at, c.updated_at
		FROM contacts c
		WHERE c.user_id = $1
	`
	args := []interface{}{userID}
	argCount := 1

	// Add search filter
	if params.Search != "" {
		argCount++
		query += fmt.Sprintf(" AND LOWER(c.name) LIKE LOWER($%d)", argCount)
		args = append(args, "%"+params.Search+"%")
	}

	// Add relationship type filter
	if params.RelationshipType != "" {
		argCount++
		query += fmt.Sprintf(" AND c.relationship_type = $%d", argCount)
		args = append(args, params.RelationshipType)
	}

	// Add bubble status filter
	if params.BubbleStatus != "" {
		argCount++
		query += fmt.Sprintf(" AND c.bubble_status = $%d", argCount)
		args = append(args, params.BubbleStatus)
	}

	query += " ORDER BY c.name ASC"

	// Add pagination
	if params.Limit > 0 {
		argCount++
		query += fmt.Sprintf(" LIMIT $%d", argCount)
		args = append(args, params.Limit)

		if params.Offset > 0 {
			argCount++
			query += fmt.Sprintf(" OFFSET $%d", argCount)
			args = append(args, params.Offset)
		}
	}

	rows, err := contactDB.Query(ctx, query, args...)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to get contacts").Err()
	}
	defer rows.Close()

	var contacts []Contact
	for rows.Next() {
		var c Contact
		err := rows.Scan(&c.ID, &c.UserID, &c.ContactUserID, &c.Name, &c.Email, &c.PhoneNumber,
			&c.ImageURL, &c.RelationshipType, &c.BubbleStatus, &c.IsOnline, &c.Source, &c.CreatedAt, &c.UpdatedAt)
		if err != nil {
			continue
		}
		contacts = append(contacts, c)
	}

	// Get total count for pagination
	countQuery := `SELECT COUNT(*) FROM contacts WHERE user_id = $1`
	var total int
	err = contactDB.QueryRow(ctx, countQuery, userID).Scan(&total)
	if err != nil {
		total = len(contacts)
	}

	return &GetContactsResponse{
		Contacts: contacts,
		Total:    total,
	}, nil
}

// Send contact request
//encore:api auth method=POST path=/contacts/request
func (s *Service) SendContactRequest(ctx context.Context, params *SendContactRequestParams) (*SendContactRequestResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	senderID := string(uid)

	// Check if request already exists
	var existingID string
	err := contactDB.QueryRow(ctx, `
		SELECT id FROM contact_requests 
		WHERE sender_id = $1 AND recipient_id = $2 AND status = 'pending'
	`, senderID, params.ContactUserID).Scan(&existingID)

	if err == nil {
		return &SendContactRequestResponse{
			Success:   false,
			RequestID: existingID,
			Message:   "Contact request already sent",
		}, nil
	}

	// Create new contact request
	var requestID string
	err = contactDB.QueryRow(ctx, `
		INSERT INTO contact_requests (sender_id, recipient_id, message, status, created_at, updated_at)
		VALUES ($1, $2, $3, 'pending', NOW(), NOW())
		RETURNING id
	`, senderID, params.ContactUserID, params.Message).Scan(&requestID)

	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to send contact request").Err()
	}

	// Send notification to recipient
	go func() {
		senderInfo, err := user.GetByID(ctx, &user.GetByIDParams{ID: senderID})
		if err == nil {
			notification.SendNotification(ctx, &notification.SendNotificationRequest{
				UserID:    params.ContactUserID,
				Type:      "contact_request",
				Title:     "New Contact Request",
				Message:   fmt.Sprintf("%s wants to add you as a contact", senderInfo.FirstName+" "+senderInfo.LastName),
				SendInApp: true,
				SendPush:  true,
			})
		}
	}()

	return &SendContactRequestResponse{
		Success:   true,
		RequestID: requestID,
		Message:   "Contact request sent successfully",
	}, nil
}

// Get pending contact requests
//encore:api auth method=GET path=/contacts/requests/pending
func (s *Service) GetPendingRequests(ctx context.Context) (*GetContactRequestsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	rows, err := contactDB.Query(ctx, `
		SELECT id, sender_id, recipient_id, message, status, created_at, updated_at
		FROM contact_requests
		WHERE recipient_id = $1 AND status = 'pending'
		ORDER BY created_at DESC
	`, userID)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to get pending requests").Err()
	}
	defer rows.Close()

	var requests []ContactRequest
	for rows.Next() {
		var r ContactRequest
		err := rows.Scan(&r.ID, &r.SenderID, &r.RecipientID, &r.Message, &r.Status, &r.CreatedAt, &r.UpdatedAt)
		if err != nil {
			continue
		}
		requests = append(requests, r)
	}

	return &GetContactRequestsResponse{
		Requests: requests,
		Total:    len(requests),
	}, nil
}

// Accept or decline contact request
//encore:api auth method=POST path=/contacts/requests/:requestID/action
func (s *Service) HandleContactRequest(ctx context.Context, requestID string, params *ContactRequestActionParams) (*ContactRequestActionResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	if params.Action != "accept" && params.Action != "decline" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("action must be 'accept' or 'decline'").Err()
	}

	// Update request status
	var senderID string
	err := contactDB.QueryRow(ctx, `
		UPDATE contact_requests 
		SET status = $1, updated_at = NOW()
		WHERE id = $2 AND recipient_id = $3 AND status = 'pending'
		RETURNING sender_id
	`, params.Action+"ed", requestID, userID).Scan(&senderID)

	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("contact request not found").Err()
	}

	// If accepted, create mutual contact relationship
	if params.Action == "accept" {
		// Add sender to recipient's contacts
		_, err = contactDB.Exec(ctx, `
			INSERT INTO contacts (user_id, contact_user_id, name, relationship_type, source, created_at, updated_at)
			SELECT $1, $2, u.first_name || ' ' || u.last_name, 'contact', 'platform', NOW(), NOW()
			FROM users u WHERE u.id = $2
			ON CONFLICT (user_id, contact_user_id) DO UPDATE SET relationship_type = 'contact'
		`, userID, senderID)

		// Add recipient to sender's contacts
		_, err = contactDB.Exec(ctx, `
			INSERT INTO contacts (user_id, contact_user_id, name, relationship_type, source, created_at, updated_at)
			SELECT $1, $2, u.first_name || ' ' || u.last_name, 'contact', 'platform', NOW(), NOW()
			FROM users u WHERE u.id = $2
			ON CONFLICT (user_id, contact_user_id) DO UPDATE SET relationship_type = 'contact'
		`, senderID, userID)
	}

	status := params.Action + "ed"
	message := fmt.Sprintf("Contact request %s", status)

	return &ContactRequestActionResponse{
		Success: true,
		Status:  status,
		Message: message,
	}, nil
}

// Update contact information
//encore:api auth method=PUT path=/contacts/:contactID
func (s *Service) UpdateContact(ctx context.Context, contactID string, params *UpdateContactParams) (*Contact, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Build update query dynamically
	setParts := []string{}
	args := []interface{}{contactID, userID}
	argCount := 2

	if params.Name != "" {
		argCount++
		setParts = append(setParts, fmt.Sprintf("name = $%d", argCount))
		args = append(args, params.Name)
	}

	if params.RelationshipType != "" {
		argCount++
		setParts = append(setParts, fmt.Sprintf("relationship_type = $%d", argCount))
		args = append(args, params.RelationshipType)
	}

	if len(setParts) == 0 {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("no fields to update").Err()
	}

	setParts = append(setParts, "updated_at = NOW()")
	setClause := strings.Join(setParts, ", ")

	query := fmt.Sprintf(`
		UPDATE contacts 
		SET %s
		WHERE id = $1 AND user_id = $2
		RETURNING id, user_id, contact_user_id, name, email, phone_number, image_url, relationship_type, bubble_status, is_online, source, created_at, updated_at
	`, setClause)

	var contact Contact
	err := contactDB.QueryRow(ctx, query, args...).Scan(
		&contact.ID, &contact.UserID, &contact.ContactUserID, &contact.Name, &contact.Email, &contact.PhoneNumber,
		&contact.ImageURL, &contact.RelationshipType, &contact.BubbleStatus, &contact.IsOnline, &contact.Source, &contact.CreatedAt, &contact.UpdatedAt)

	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("contact not found").Err()
	}

	return &contact, nil
}

// Remove contact
//encore:api auth method=DELETE path=/contacts/:contactID
func (s *Service) RemoveContact(ctx context.Context, contactID string) error {
	uid, ok := auth.UserID()
	if !ok {
		return errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	_, err := contactDB.Exec(ctx, `
		DELETE FROM contacts 
		WHERE id = $1 AND user_id = $2
	`, contactID, userID)

	if err != nil {
		return errs.B().Code(errs.Internal).Msg("failed to remove contact").Err()
	}

	return nil
}

// Block contact
//encore:api auth method=POST path=/contacts/:contactID/block
func (s *Service) BlockContact(ctx context.Context, contactID string) error {
	uid, ok := auth.UserID()
	if !ok {
		return errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	_, err := contactDB.Exec(ctx, `
		UPDATE contacts 
		SET relationship_type = 'blocked', updated_at = NOW()
		WHERE id = $1 AND user_id = $2
	`, contactID, userID)

	if err != nil {
		return errs.B().Code(errs.Internal).Msg("failed to block contact").Err()
	}

	return nil
} 