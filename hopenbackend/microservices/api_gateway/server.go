// Package api_gateway provides GraphQL API implementation with schema stitching
// and real-time subscriptions for flexible data querying.
package api_gateway

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	// "encore.dev/api" // Commented out for compilation
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Server manages GraphQL operations and schema
type Server struct {
	config      *Config
	schema      *Schema
	resolvers   map[string]Resolver
	middleware  []Middleware
	metrics     *GraphQLMetrics
	subscriptions *SubscriptionManager
	cache       map[string]*CacheEntry
	cacheMutex  sync.RWMutex
}

// Config holds GraphQL server configuration
type Config struct {
	EnableIntrospection bool
	EnablePlayground    bool
	MaxQueryDepth       int
	MaxQueryComplexity  int
	EnableCaching       bool
	CacheTTL           time.Duration
	EnableSubscriptions bool
	EnableTracing      bool
	EnableMetrics      bool
}

// Schema represents the GraphQL schema
type Schema struct {
	Types         map[string]*TypeDefinition
	Queries       map[string]*FieldDefinition
	Mutations     map[string]*FieldDefinition
	Subscriptions map[string]*FieldDefinition
	Directives    map[string]*DirectiveDefinition
}

// TypeDefinition represents a GraphQL type
type TypeDefinition struct {
	Name        string
	Kind        string // OBJECT, INTERFACE, UNION, ENUM, INPUT_OBJECT, SCALAR
	Description string
	Fields      map[string]*FieldDefinition
	Interfaces  []string
	EnumValues  []string
}

// FieldDefinition represents a GraphQL field
type FieldDefinition struct {
	Name        string
	Type        string
	Description string
	Args        map[string]*ArgumentDefinition
	Resolver    Resolver
}

// ArgumentDefinition represents a GraphQL argument
type ArgumentDefinition struct {
	Name         string
	Type         string
	Description  string
	DefaultValue interface{}
}

// DirectiveDefinition represents a GraphQL directive
type DirectiveDefinition struct {
	Name        string
	Description string
	Locations   []string
	Args        map[string]*ArgumentDefinition
}

// Resolver defines a GraphQL resolver function
type Resolver func(ctx context.Context, args map[string]interface{}) (interface{}, error)

// Middleware defines GraphQL middleware
type Middleware func(ctx context.Context, query string, variables map[string]interface{}) error

// Request represents a GraphQL request
type Request struct {
	Query         string                 `json:"query"`
	Variables     map[string]interface{} `json:"variables,omitempty"`
	OperationName string                 `json:"operationName,omitempty"`
}

// Response represents a GraphQL response
type Response struct {
	Data       interface{}            `json:"data,omitempty"`
	Errors     []GraphQLError         `json:"errors,omitempty"`
	Extensions map[string]interface{} `json:"extensions,omitempty"`
}



// Location represents an error location
type Location struct {
	Line   int `json:"line"`
	Column int `json:"column"`
}

// SubscriptionManager manages GraphQL subscriptions
type SubscriptionManager struct {
	subscriptions map[string]*Subscription
	clients       map[string]chan *SubscriptionEvent
	mutex         sync.RWMutex
}

// Subscription represents an active GraphQL subscription
type Subscription struct {
	ID        string
	Query     string
	Variables map[string]interface{}
	ClientID  string
	CreatedAt time.Time
}

// SubscriptionEvent represents a subscription event
type SubscriptionEvent struct {
	ID   string      `json:"id"`
	Type string      `json:"type"`
	Data interface{} `json:"data"`
}

// CacheEntry represents a cached GraphQL result
type CacheEntry struct {
	Data      interface{}
	ExpiresAt time.Time
}

// GraphQLMetrics holds Prometheus metrics
type GraphQLMetrics struct {
	requestsTotal       *prometheus.CounterVec
	requestDuration     *prometheus.HistogramVec
	queryComplexity     *prometheus.HistogramVec
	resolverDuration    *prometheus.HistogramVec
	cacheHits           prometheus.Counter
	cacheMisses         prometheus.Counter
	subscriptionsActive prometheus.Gauge
	errors              *prometheus.CounterVec
}

// Operation represents a parsed GraphQL operation
type Operation struct {
	Type  string
	Query string
}

// NewServer creates a new GraphQL server
func NewServer(config *Config) *Server {
	if config == nil {
		config = DefaultConfig()
	}

	server := &Server{
		config:        config,
		schema:        NewSchema(),
		resolvers:     make(map[string]Resolver),
		middleware:    []Middleware{},
		metrics:       newGraphQLMetrics(),
		subscriptions: NewSubscriptionManager(),
		cache:         make(map[string]*CacheEntry),
	}

	// Register default schema
	server.registerDefaultSchema()

	return server
}

// GraphQL API endpoints - Commented out for compilation
/*
var GraphQLEndpoint = api.Raw(
	api.RawConfig{
		Path:   "/graphql",
		Method: "POST",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		server := GetDefaultServer()
		server.ServeHTTP(w, r)
	},
)

// GraphQL Playground endpoint
var PlaygroundEndpoint = api.Raw(
	api.RawConfig{
		Path:   "/graphql/playground",
		Method: "GET",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		server := GetDefaultServer()
		if server.config.EnablePlayground {
			server.ServePlayground(w, r)
		} else {
			http.Error(w, "Playground disabled", http.StatusNotFound)
		}
	},
)
*/

// ServeHTTP handles GraphQL HTTP requests
func (s *Server) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	
	// Parse request
	var req Request
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		s.writeError(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// Apply middleware
	ctx := r.Context()
	for _, middleware := range s.middleware {
		if err := middleware(ctx, req.Query, req.Variables); err != nil {
			s.writeError(w, err.Error(), http.StatusBadRequest)
			return
		}
	}

	// Execute query
	response := s.ExecuteQuery(ctx, &req)

	// Update metrics
	s.metrics.requestsTotal.WithLabelValues(s.getOperationType(req.Query)).Inc()
	s.metrics.requestDuration.WithLabelValues(s.getOperationType(req.Query)).Observe(time.Since(start).Seconds())

	// Write response
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// ExecuteQuery executes a GraphQL query
func (s *Server) ExecuteQuery(ctx context.Context, req *Request) *Response {
	// Check cache first
	if s.config.EnableCaching && s.getOperationType(req.Query) == "query" {
		if cached := s.getCachedResult(req); cached != nil {
			s.metrics.cacheHits.Inc()
			return &Response{Data: cached}
		}
		s.metrics.cacheMisses.Inc()
	}

	// Parse and validate query
	operation, err := s.parseQuery(req.Query)
	if err != nil {
		return &Response{
			Errors: []GraphQLError{{Message: err.Error()}},
		}
	}

	// Execute operation
	result, err := s.executeOperation(ctx, operation, req.Variables)
	if err != nil {
		s.metrics.errors.WithLabelValues("execution").Inc()
		return &Response{
			Errors: []GraphQLError{{Message: err.Error()}},
		}
	}

	// Cache result if applicable
	if s.config.EnableCaching && operation.Type == "query" {
		s.cacheResult(req, result)
	}

	return &Response{Data: result}
}

// RegisterType registers a new GraphQL type
func (s *Server) RegisterType(typeDef *TypeDefinition) {
	s.schema.Types[typeDef.Name] = typeDef
}

// RegisterQuery registers a new GraphQL query
func (s *Server) RegisterQuery(name string, field *FieldDefinition) {
	s.schema.Queries[name] = field
	s.resolvers[fmt.Sprintf("Query.%s", name)] = field.Resolver
}

// RegisterMutation registers a new GraphQL mutation
func (s *Server) RegisterMutation(name string, field *FieldDefinition) {
	s.schema.Mutations[name] = field
	s.resolvers[fmt.Sprintf("Mutation.%s", name)] = field.Resolver
}

// RegisterSubscription registers a new GraphQL subscription
func (s *Server) RegisterSubscription(name string, field *FieldDefinition) {
	s.schema.Subscriptions[name] = field
	s.resolvers[fmt.Sprintf("Subscription.%s", name)] = field.Resolver
}

// RegisterMiddleware registers GraphQL middleware
func (s *Server) RegisterMiddleware(middleware Middleware) {
	s.middleware = append(s.middleware, middleware)
}

// Default schema registration
func (s *Server) registerDefaultSchema() {
	// User type
	s.RegisterType(&TypeDefinition{
		Name: "User",
		Kind: "OBJECT",
		Fields: map[string]*FieldDefinition{
			"id":       {Name: "id", Type: "ID!"},
			"email":    {Name: "email", Type: "String!"},
			"username": {Name: "username", Type: "String"},
			"createdAt": {Name: "createdAt", Type: "DateTime!"},
		},
	})

	// Bubble type
	s.RegisterType(&TypeDefinition{
		Name: "Bubble",
		Kind: "OBJECT",
		Fields: map[string]*FieldDefinition{
			"id":          {Name: "id", Type: "ID!"},
			"name":        {Name: "name", Type: "String!"},
			"description": {Name: "description", Type: "String"},
			"createdBy":   {Name: "createdBy", Type: "User!"},
			"members":     {Name: "members", Type: "[User!]!"},
			"createdAt":   {Name: "createdAt", Type: "DateTime!"},
		},
	})

	// Message type
	s.RegisterType(&TypeDefinition{
		Name: "Message",
		Kind: "OBJECT",
		Fields: map[string]*FieldDefinition{
			"id":        {Name: "id", Type: "ID!"},
			"content":   {Name: "content", Type: "String!"},
			"sender":    {Name: "sender", Type: "User!"},
			"bubble":    {Name: "bubble", Type: "Bubble!"},
			"createdAt": {Name: "createdAt", Type: "DateTime!"},
		},
	})

	// Queries
	s.RegisterQuery("user", &FieldDefinition{
		Name: "user",
		Type: "User",
		Args: map[string]*ArgumentDefinition{
			"id": {Name: "id", Type: "ID!"},
		},
		Resolver: s.resolveUser,
	})

	s.RegisterQuery("users", &FieldDefinition{
		Name: "users",
		Type: "[User!]!",
		Args: map[string]*ArgumentDefinition{
			"limit":  {Name: "limit", Type: "Int", DefaultValue: 10},
			"offset": {Name: "offset", Type: "Int", DefaultValue: 0},
		},
		Resolver: s.resolveUsers,
	})

	s.RegisterQuery("bubble", &FieldDefinition{
		Name: "bubble",
		Type: "Bubble",
		Args: map[string]*ArgumentDefinition{
			"id": {Name: "id", Type: "ID!"},
		},
		Resolver: s.resolveBubble,
	})

	s.RegisterQuery("bubbles", &FieldDefinition{
		Name: "bubbles",
		Type: "[Bubble!]!",
		Args: map[string]*ArgumentDefinition{
			"limit":  {Name: "limit", Type: "Int", DefaultValue: 10},
			"offset": {Name: "offset", Type: "Int", DefaultValue: 0},
		},
		Resolver: s.resolveBubbles,
	})

	// Mutations
	s.RegisterMutation("createBubble", &FieldDefinition{
		Name: "createBubble",
		Type: "Bubble!",
		Args: map[string]*ArgumentDefinition{
			"name":        {Name: "name", Type: "String!"},
			"description": {Name: "description", Type: "String"},
		},
		Resolver: s.resolveCreateBubble,
	})

	s.RegisterMutation("sendMessage", &FieldDefinition{
		Name: "sendMessage",
		Type: "Message!",
		Args: map[string]*ArgumentDefinition{
			"bubbleId": {Name: "bubbleId", Type: "ID!"},
			"content":  {Name: "content", Type: "String!"},
		},
		Resolver: s.resolveSendMessage,
	})

	// Subscriptions
	s.RegisterSubscription("messageAdded", &FieldDefinition{
		Name: "messageAdded",
		Type: "Message!",
		Args: map[string]*ArgumentDefinition{
			"bubbleId": {Name: "bubbleId", Type: "ID!"},
		},
		Resolver: s.resolveMessageSubscription,
	})
}

// Resolver implementations (simplified - would connect to actual data sources)
func (s *Server) resolveUser(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	userID := args["id"].(string)
	// In production, this would query the database
	return map[string]interface{}{
		"id":       userID,
		"email":    "<EMAIL>",
		"username": "testuser",
		"createdAt": time.Now(),
	}, nil
}

func (s *Server) resolveUsers(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// In production, this would query the database with pagination
	return []map[string]interface{}{
		{
			"id":       "1",
			"email":    "<EMAIL>",
			"username": "user1",
			"createdAt": time.Now(),
		},
		{
			"id":       "2",
			"email":    "<EMAIL>",
			"username": "user2",
			"createdAt": time.Now(),
		},
	}, nil
}

func (s *Server) resolveBubble(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	bubbleID := args["id"].(string)
	// In production, this would query the database
	return map[string]interface{}{
		"id":          bubbleID,
		"name":        "Test Bubble",
		"description": "A test bubble",
		"createdBy":   map[string]interface{}{"id": "1", "username": "creator"},
		"members":     []interface{}{},
		"createdAt":   time.Now(),
	}, nil
}

func (s *Server) resolveBubbles(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// In production, this would query the database with pagination
	return []map[string]interface{}{
		{
			"id":          "1",
			"name":        "Bubble 1",
			"description": "First bubble",
			"createdAt":   time.Now(),
		},
		{
			"id":          "2",
			"name":        "Bubble 2",
			"description": "Second bubble",
			"createdAt":   time.Now(),
		},
	}, nil
}

func (s *Server) resolveCreateBubble(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	name := args["name"].(string)
	description, _ := args["description"].(string)
	
	// In production, this would create in database
	return map[string]interface{}{
		"id":          fmt.Sprintf("bubble_%d", time.Now().Unix()),
		"name":        name,
		"description": description,
		"createdAt":   time.Now(),
	}, nil
}

func (s *Server) resolveSendMessage(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	bubbleID := args["bubbleId"].(string)
	content := args["content"].(string)
	
	message := map[string]interface{}{
		"id":        fmt.Sprintf("msg_%d", time.Now().Unix()),
		"content":   content,
		"bubbleId":  bubbleID,
		"createdAt": time.Now(),
	}
	
	// Trigger subscription
	s.subscriptions.Publish("messageAdded", bubbleID, message)
	
	return message, nil
}

func (s *Server) resolveMessageSubscription(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	bubbleID := args["bubbleId"].(string)
	
	// Create subscription channel
	ch := make(chan *SubscriptionEvent, 100)
	s.subscriptions.Subscribe("messageAdded", bubbleID, ch)
	
	return ch, nil
}

// Helper methods
func (s *Server) parseQuery(query string) (*Operation, error) {
	// Simplified query parsing - in production would use proper GraphQL parser
	operationType := "query"
	if strings.Contains(strings.ToLower(query), "mutation") {
		operationType = "mutation"
	} else if strings.Contains(strings.ToLower(query), "subscription") {
		operationType = "subscription"
	}
	
	return &Operation{
		Type:  operationType,
		Query: query,
	}, nil
}

func (s *Server) executeOperation(ctx context.Context, operation *Operation, variables map[string]interface{}) (interface{}, error) {
	// Simplified execution - in production would use proper GraphQL execution engine
	switch operation.Type {
	case "query":
		return s.executeQuery(ctx, operation, variables)
	case "mutation":
		return s.executeMutation(ctx, operation, variables)
	case "subscription":
		return s.executeSubscription(ctx, operation, variables)
	default:
		return nil, fmt.Errorf("unknown operation type: %s", operation.Type)
	}
}

func (s *Server) executeQuery(ctx context.Context, operation *Operation, variables map[string]interface{}) (interface{}, error) {
	// Simplified query execution
	return map[string]interface{}{
		"users": []map[string]interface{}{
			{"id": "1", "username": "user1"},
			{"id": "2", "username": "user2"},
		},
	}, nil
}

func (s *Server) executeMutation(ctx context.Context, operation *Operation, variables map[string]interface{}) (interface{}, error) {
	// Simplified mutation execution
	return map[string]interface{}{
		"createBubble": map[string]interface{}{
			"id":   "new_bubble",
			"name": "New Bubble",
		},
	}, nil
}

func (s *Server) executeSubscription(ctx context.Context, operation *Operation, variables map[string]interface{}) (interface{}, error) {
	// Simplified subscription execution
	return map[string]interface{}{
		"messageAdded": "subscription_channel",
	}, nil
}

func (s *Server) getOperationType(query string) string {
	query = strings.ToLower(strings.TrimSpace(query))
	if strings.HasPrefix(query, "mutation") {
		return "mutation"
	} else if strings.HasPrefix(query, "subscription") {
		return "subscription"
	}
	return "query"
}

func (s *Server) getCachedResult(req *Request) interface{} {
	s.cacheMutex.RLock()
	defer s.cacheMutex.RUnlock()
	
	key := s.getCacheKey(req)
	if entry, exists := s.cache[key]; exists && time.Now().Before(entry.ExpiresAt) {
		return entry.Data
	}
	return nil
}

func (s *Server) cacheResult(req *Request, result interface{}) {
	s.cacheMutex.Lock()
	defer s.cacheMutex.Unlock()
	
	key := s.getCacheKey(req)
	s.cache[key] = &CacheEntry{
		Data:      result,
		ExpiresAt: time.Now().Add(s.config.CacheTTL),
	}
}

func (s *Server) getCacheKey(req *Request) string {
	// Simple cache key generation - in production would be more sophisticated
	return fmt.Sprintf("%s_%v", req.Query, req.Variables)
}

func (s *Server) writeError(w http.ResponseWriter, message string, status int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(Response{
		Errors: []GraphQLError{{Message: message}},
	})
}

func (s *Server) ServePlayground(w http.ResponseWriter, r *http.Request) {
	playground := `
<!DOCTYPE html>
<html>
<head>
    <title>GraphQL Playground</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/css/index.css" />
    <link rel="shortcut icon" href="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/favicon.png" />
    <script src="https://cdn.jsdelivr.net/npm/graphql-playground-react/build/static/js/middleware.js"></script>
</head>
<body>
    <div id="root">
        <style>
            body { margin: 0; font-family: Open Sans, sans-serif; overflow: hidden; }
            #root { height: 100vh; }
        </style>
    </div>
    <script>
        window.addEventListener('load', function (event) {
            GraphQLPlayground.init(document.getElementById('root'), {
                endpoint: '/graphql'
            })
        })
    </script>
</body>
</html>`
	
	w.Header().Set("Content-Type", "text/html")
	w.Write([]byte(playground))
}

// Schema constructor
func NewSchema() *Schema {
	return &Schema{
		Types:         make(map[string]*TypeDefinition),
		Queries:       make(map[string]*FieldDefinition),
		Mutations:     make(map[string]*FieldDefinition),
		Subscriptions: make(map[string]*FieldDefinition),
		Directives:    make(map[string]*DirectiveDefinition),
	}
}

// Subscription Manager
func NewSubscriptionManager() *SubscriptionManager {
	return &SubscriptionManager{
		subscriptions: make(map[string]*Subscription),
		clients:       make(map[string]chan *SubscriptionEvent),
	}
}

func (sm *SubscriptionManager) Subscribe(eventType, filter string, ch chan *SubscriptionEvent) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()
	
	key := fmt.Sprintf("%s:%s", eventType, filter)
	sm.clients[key] = ch
}

func (sm *SubscriptionManager) Publish(eventType, filter string, data interface{}) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()
	
	key := fmt.Sprintf("%s:%s", eventType, filter)
	if ch, exists := sm.clients[key]; exists {
		event := &SubscriptionEvent{
			ID:   fmt.Sprintf("event_%d", time.Now().UnixNano()),
			Type: eventType,
			Data: data,
		}
		
		select {
		case ch <- event:
		default:
			// Channel full, skip
		}
	}
}

// Global server instance
var defaultServer *Server
var serverOnce sync.Once

func GetDefaultServer() *Server {
	serverOnce.Do(func() {
		defaultServer = NewServer(nil)
	})
	return defaultServer
}

// newGraphQLMetrics initializes Prometheus metrics
func newGraphQLMetrics() *GraphQLMetrics {
	return &GraphQLMetrics{
		requestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_graphql_requests_total",
			Help: "Total number of GraphQL requests",
		}, []string{"operation_type"}),
		requestDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_graphql_request_duration_seconds",
			Help:    "GraphQL request duration",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0},
		}, []string{"operation_type"}),
		queryComplexity: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_graphql_query_complexity",
			Help:    "GraphQL query complexity",
			Buckets: []float64{1, 5, 10, 25, 50, 100, 250, 500, 1000},
		}, []string{"operation_type"}),
		resolverDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_graphql_resolver_duration_seconds",
			Help:    "GraphQL resolver duration",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"resolver"}),
		cacheHits: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_graphql_cache_hits_total",
			Help: "Total number of GraphQL cache hits",
		}),
		cacheMisses: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_graphql_cache_misses_total",
			Help: "Total number of GraphQL cache misses",
		}),
		subscriptionsActive: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_graphql_subscriptions_active",
			Help: "Number of active GraphQL subscriptions",
		}),
		errors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_graphql_errors_total",
			Help: "Total number of GraphQL errors",
		}, []string{"type"}),
	}
}

// DefaultConfig returns default GraphQL configuration
func DefaultConfig() *Config {
	return &Config{
		EnableIntrospection: true,
		EnablePlayground:    true,
		MaxQueryDepth:       15,
		MaxQueryComplexity:  1000,
		EnableCaching:       true,
		CacheTTL:           5 * time.Minute,
		EnableSubscriptions: true,
		EnableTracing:      true,
		EnableMetrics:      true,
	}
} 