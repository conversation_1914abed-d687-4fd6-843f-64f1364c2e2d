package auth

import (
	"context"
	"fmt"
	"time"
	"encore.dev/storage/cache"
)

// Cache TTLs
const (
	jwtCacheTTL = 5 * time.Minute  // JWT cache duration
	aclCacheTTL = 30 * time.Minute // ACL cache duration
)

// Initialize Valkey cache cluster
var authCache = cache.NewCluster("auth-cache", cache.ClusterConfig{
	EvictionPolicy: cache.AllKeysLRU,
})

// JWT cache keyspace
var jwtKeyspace = cache.NewStructKeyspace[string, CachedJWTClaims](authCache, cache.KeyspaceConfig{
	KeyPattern:    "jwt/:key",
	DefaultExpiry: cache.ExpireIn(jwtCacheTTL),
})

// ACL cache keyspace
var aclKeyspace = cache.NewStructKeyspace[string, CachedACL](authCache, cache.KeyspaceConfig{
	KeyPattern:    "acl/:key",
	DefaultExpiry: cache.ExpireIn(aclCacheTTL),
})

// CachedJWTClaims represents cached JWT validation results
type CachedJWTClaims struct {
	UserID    string    `json:"user_id"`
	ExpiresAt time.Time `json:"expires_at"`
}

// CachedACL represents cached ACL permissions
type CachedACL struct {
	UserID string   `json:"user_id"`
	Pub    []string `json:"pub"`
	Sub    []string `json:"sub"`
}

// getJWTFromCache attempts to get JWT validation results from cache
func getJWTFromCache(ctx context.Context, token string) (*CachedJWTClaims, error) {
	claims, err := jwtKeyspace.Get(ctx, token)
	if err == cache.Miss {
		return nil, nil
	} else if err != nil {
		return nil, fmt.Errorf("cache error: %w", err)
	}

	// Check if cached claims are still valid
	if time.Now().After(claims.ExpiresAt) {
		_, _ = jwtKeyspace.Delete(ctx, token)
		return nil, nil
	}

	return &claims, nil
}

// setJWTInCache stores JWT validation results in cache
func setJWTInCache(ctx context.Context, token string, claims *CachedJWTClaims) error {
	return jwtKeyspace.Set(ctx, token, *claims)
}

// getACLFromCache attempts to get ACL permissions from cache
func getACLFromCache(ctx context.Context, userID string) (*CachedACL, error) {
	acl, err := aclKeyspace.Get(ctx, userID)
	if err == cache.Miss {
		return nil, nil
	} else if err != nil {
		return nil, fmt.Errorf("cache error: %w", err)
	}

	return &acl, nil
}

// setACLInCache stores ACL permissions in cache
func setACLInCache(ctx context.Context, userID string, acl *CachedACL) error {
	return aclKeyspace.Set(ctx, userID, *acl)
}

// invalidateJWTCache removes a JWT from cache
func invalidateJWTCache(ctx context.Context, token string) error {
	_, err := jwtKeyspace.Delete(ctx, token)
	return err
}

// invalidateACLCache removes an ACL from cache
func invalidateACLCache(ctx context.Context, userID string) error {
	_, err := aclKeyspace.Delete(ctx, userID)
	return err
}

// generateUserACL creates a new ACL for a user
func generateUserACL(userID string) *CachedACL {
	return &CachedACL{
		UserID: userID,
		Sub: []string{
			fmt.Sprintf("user/%s/#", userID),     // User's personal topics
			fmt.Sprintf("bubbles/+/members/%s/#", userID), // Topics for bubbles the user is a member of
			"public/#",                           // Public topics
		},
		Pub: []string{
			fmt.Sprintf("user/%s/#", userID),     // User's personal topics
			fmt.Sprintf("bubbles/+/members/%s/#", userID), // Topics for bubbles the user is a member of
		},
	}
} 