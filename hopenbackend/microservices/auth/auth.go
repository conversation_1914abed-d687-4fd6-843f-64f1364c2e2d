// Service auth implements authentication using <PERSON><PERSON> (Kratos + Hydra)
package auth

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.app/microservices/user"
	"github.com/golang-jwt/jwt/v5"
)

// Configuration secrets for <PERSON><PERSON> Stack
var secrets struct {
	// Ory K<PERSON>s configuration
	KratosPublicURL string // KRATOS_PUBLIC_URL
	KratosAdminURL  string // KRATOS_ADMIN_URL
	
	// Ory Hydra configuration
	HydraPublicURL string // HYDRA_PUBLIC_URL
	HydraAdminURL  string // HYDRA_ADMIN_URL
	
	// JWT validation keys
	HydraJWKSURL string // HYDRA_JWKS_URL
	
	// OAuth2 configuration
	GoogleClientID     string // GOOGLE_CLIENT_ID
	GoogleClientSecret string // GOOGLE_CLIENT_SECRET
	AppleClientID      string // APPLE_CLIENT_ID
	AppleClientSecret  string // APPLE_CLIENT_SECRET
}

// AuthData represents the authenticated user data
type AuthData struct {
	UserID   string          `json:"sub"`
	Email    string          `json:"email"`
	Name     string          `json:"name"`
	Username string          `json:"username"`
	Claims   json.RawMessage `json:"claims"`
}

// Session represents a user session from Ory Kratos
type Session struct {
	ID       string          `json:"id"`
	Active   bool            `json:"active"`
	Identity Identity        `json:"identity"`
	Devices  []Device        `json:"devices"`
}

type Identity struct {
	ID     string          `json:"id"`
	Traits json.RawMessage `json:"traits"`
	State  string          `json:"state"`
}

type Device struct {
	ID        string `json:"id"`
	IPAddress string `json:"ip_address"`
	UserAgent string `json:"user_agent"`
	Location  string `json:"location"`
}

// ==================================================================
// Email/Password Authentication Endpoints
// ==================================================================

// RegisterParams defines the registration request structure
type RegisterParams struct {
	Email     string `json:"email"`
	Password  string `json:"password"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
}

// RegisterResponse defines the registration response structure
type RegisterResponse struct {
	Success   bool   `json:"success"`
	SessionID string `json:"session_id,omitempty"`
	UserID    string `json:"user_id,omitempty"`
	Message   string `json:"message"`
}

//encore:api public method=POST path=/auth/register
func Register(ctx context.Context, params *RegisterParams) (*RegisterResponse, error) {
	// Create registration flow with Ory Kratos
	flowResp, err := createRegistrationFlow(ctx)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to create registration flow").Err()
	}

	// Submit registration data
	sessionResp, err := submitRegistration(ctx, flowResp.ID, params)
	if err != nil {
		return &RegisterResponse{
			Success: false,
			Message: fmt.Sprintf("Registration failed: %v", err),
		}, nil
	}

	// Create user in our system
	_, err = user.Create(ctx, &user.CreateParams{
		ID:        sessionResp.Identity.ID,
		Email:     params.Email,
		FirstName: params.FirstName,
		LastName:  params.LastName,
		Username:  params.Username,
	})
	if err != nil {
		rlog.Error("failed to create user in system", "error", err)
	}

	return &RegisterResponse{
		Success:   true,
		SessionID: sessionResp.ID,
		UserID:    sessionResp.Identity.ID,
		Message:   "Registration successful",
	}, nil
}

// LoginParams defines the login request structure
type LoginParams struct {
	Email    string `json:"email"`
	Password string `json:"password"`
}

// LoginResponse defines the login response structure
type LoginResponse struct {
	Success      bool   `json:"success"`
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	SessionID    string `json:"session_id,omitempty"`
	UserID       string `json:"user_id,omitempty"`
	Message      string `json:"message"`
}

//encore:api public method=POST path=/auth/login
func Login(ctx context.Context, params *LoginParams) (*LoginResponse, error) {
	// Create login flow with Ory Kratos
	flowResp, err := createLoginFlow(ctx)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to create login flow").Err()
	}

	// Submit login credentials
	sessionResp, err := submitLogin(ctx, flowResp.ID, params)
	if err != nil {
		return &LoginResponse{
			Success: false,
			Message: fmt.Sprintf("Login failed: %v", err),
		}, nil
	}

	// Generate JWT access token via Ory Hydra
	accessToken, refreshToken, err := generateTokensViaHydra(ctx, sessionResp.Identity.ID)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to generate tokens").Err()
	}

	return &LoginResponse{
		Success:      true,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		SessionID:    sessionResp.ID,
		UserID:       sessionResp.Identity.ID,
		Message:      "Login successful",
	}, nil
}

// ==================================================================
// OAuth2 Social Login Endpoints (Google/Apple)
// ==================================================================

// OAuthInitParams defines OAuth initialization parameters
type OAuthInitParams struct {
	Provider string `json:"provider"` // "google" or "apple"
}

// OAuthInitResponse defines OAuth initialization response
type OAuthInitResponse struct {
	AuthURL string `json:"auth_url"`
	State   string `json:"state"`
}

//encore:api public method=POST path=/auth/oauth/init
func OAuthInit(ctx context.Context, params *OAuthInitParams) (*OAuthInitResponse, error) {
	if params.Provider != "google" && params.Provider != "apple" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("unsupported provider").Err()
	}

	// Generate OAuth authorization URL via Ory Hydra
	authURL, state, err := createOAuthAuthorizationURL(ctx, params.Provider)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to create OAuth URL").Err()
	}

	return &OAuthInitResponse{
		AuthURL: authURL,
		State:   state,
	}, nil
}

// OAuthCallbackParams defines OAuth callback parameters
type OAuthCallbackParams struct {
	Code     string `json:"code"`
	State    string `json:"state"`
	Provider string `json:"provider"`
}

// OAuthCallbackResponse defines OAuth callback response
type OAuthCallbackResponse struct {
	Success      bool   `json:"success"`
	AccessToken  string `json:"access_token,omitempty"`
	RefreshToken string `json:"refresh_token,omitempty"`
	UserID       string `json:"user_id,omitempty"`
	Message      string `json:"message"`
}

//encore:api public method=POST path=/auth/oauth/callback
func OAuthCallback(ctx context.Context, params *OAuthCallbackParams) (*OAuthCallbackResponse, error) {
	// Exchange authorization code for tokens via Ory Hydra
	tokens, err := exchangeOAuthCode(ctx, params.Code, params.State, params.Provider)
	if err != nil {
		return &OAuthCallbackResponse{
			Success: false,
			Message: fmt.Sprintf("OAuth callback failed: %v", err),
		}, nil
	}

	// Extract user info from ID token
	userInfo, err := extractUserInfoFromIDToken(tokens.IDToken)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to extract user info").Err()
	}

	// Create or update user in our system
	_, err = user.CreateOrUpdate(ctx, &user.CreateOrUpdateParams{
		ID:        userInfo.Subject,
		Email:     userInfo.Email,
		FirstName: userInfo.GivenName,
		LastName:  userInfo.FamilyName,
		Username:  userInfo.PreferredUsername,
		AvatarURL: userInfo.Picture,
	})
	if err != nil {
		rlog.Error("failed to create/update user", "error", err)
	}

	return &OAuthCallbackResponse{
		Success:      true,
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
		UserID:       userInfo.Subject,
		Message:      "OAuth login successful",
	}, nil
}

// ==================================================================
// Token Validation and User Info
// ==================================================================

// ValidateTokenParams defines token validation parameters
type ValidateTokenParams struct {
	Token string `json:"token"`
}

// ValidateTokenResponse defines token validation response
type ValidateTokenResponse struct {
	Valid  bool      `json:"valid"`
	UserID string    `json:"user_id,omitempty"`
	Email  string    `json:"email,omitempty"`
	Claims *AuthData `json:"claims,omitempty"`
}

//encore:api public method=POST path=/auth/validate
func ValidateToken(ctx context.Context, params *ValidateTokenParams) (*ValidateTokenResponse, error) {
	// Remove Bearer prefix if present
	token := strings.TrimPrefix(params.Token, "Bearer ")
	
	// Validate JWT token against Ory Hydra
	claims, err := validateHydraJWT(ctx, token)
	if err != nil {
		return &ValidateTokenResponse{
			Valid: false,
		}, nil
	}

	return &ValidateTokenResponse{
		Valid:  true,
		UserID: claims.UserID,
		Email:  claims.Email,
		Claims: claims,
	}, nil
}

// ProfileResponse defines user profile response
type ProfileResponse struct {
	UserID    string `json:"user_id"`
	Email     string `json:"email"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
	AvatarURL string `json:"avatar_url"`
}

//encore:api auth method=GET path=/auth/profile
func GetProfile(ctx context.Context) (*ProfileResponse, error) {
	// Get user ID from authenticated context
	userID, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("not authenticated").Err()
	}

	// Fetch user profile from our system
	profile, err := user.GetByID(ctx, string(userID))
	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("user not found").Err()
	}

	// Handle nullable string fields
	firstName := ""
	if profile.FirstName != nil {
		firstName = *profile.FirstName
	}
	lastName := ""
	if profile.LastName != nil {
		lastName = *profile.LastName
	}
	username := ""
	if profile.Username != nil {
		username = *profile.Username
	}
	avatarURL := ""
	if profile.AvatarURL != nil {
		avatarURL = *profile.AvatarURL
	}

	return &ProfileResponse{
		UserID:    profile.ID,
		Email:     profile.Email,
		FirstName: firstName,
		LastName:  lastName,
		Username:  username,
		AvatarURL: avatarURL,
	}, nil
}

// ==================================================================
// Update Onboarding Status
// ==================================================================

// UpdateOnboardingStatusParams defines parameters for updating onboarding status
type UpdateOnboardingStatusParams struct {
	HasCompletedOnboarding bool `json:"hasCompletedOnboarding"`
}

// UpdateOnboardingStatusResponse defines response for updating onboarding status
type UpdateOnboardingStatusResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

//encore:api auth method=POST path=/auth/onboarding/complete
func UpdateOnboardingStatus(ctx context.Context, params *UpdateOnboardingStatusParams) (*UpdateOnboardingStatusResponse, error) {
	// Get user ID from authenticated context
	userID, ok := auth.UserID()
	if !ok {
		return &UpdateOnboardingStatusResponse{
			Success: false,
			Message: "not authenticated",
		}, nil
	}

	// Update onboarding status in Ory Kratos
	err := updateKratosUserTraits(ctx, string(userID), map[string]interface{}{
		"hasCompletedOnboarding": params.HasCompletedOnboarding,
	})
	if err != nil {
		rlog.Error("failed to update onboarding status in Kratos", "error", err, "userID", userID)
		return &UpdateOnboardingStatusResponse{
			Success: false,
			Message: "Failed to update onboarding status",
		}, nil
	}

	return &UpdateOnboardingStatusResponse{
		Success: true,
		Message: "Onboarding status updated successfully",
	}, nil
}

// ==================================================================
// Logout
// ==================================================================

// LogoutParams defines logout parameters
type LogoutParams struct {
	SessionToken string `json:"session_token,omitempty"`
}

// LogoutResponse defines logout response
type LogoutResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

//encore:api public method=POST path=/auth/logout
func Logout(ctx context.Context, params *LogoutParams) (*LogoutResponse, error) {
	// Invalidate session via Ory Kratos
	err := invalidateKratosSession(ctx, params.SessionToken)
	if err != nil {
		rlog.Error("failed to invalidate Kratos session", "error", err)
	}

	// Revoke tokens via Ory Hydra
	err = revokeHydraTokens(ctx, params.SessionToken)
	if err != nil {
		rlog.Error("failed to revoke Hydra tokens", "error", err)
	}

	return &LogoutResponse{
		Success: true,
		Message: "Logout successful",
	}, nil
}

// ==================================================================
// Auth Handler for Encore's built-in auth system
// ==================================================================

//encore:authhandler
func AuthHandler(ctx context.Context, token string) (auth.UID, *AuthData, error) {
	// Validate JWT token
	claims, err := validateHydraJWT(ctx, token)
	if err != nil {
		return "", nil, errs.B().Code(errs.Unauthenticated).Msg("invalid token").Err()
	}

	return auth.UID(claims.UserID), claims, nil
}

// ==================================================================
// Helper Functions - Ory Kratos Integration
// ==================================================================

type KratosFlow struct {
	ID string `json:"id"`
}

func createRegistrationFlow(ctx context.Context) (*KratosFlow, error) {
	url := fmt.Sprintf("%s/self-service/registration/api", secrets.KratosPublicURL)
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var flow KratosFlow
	if err := json.NewDecoder(resp.Body).Decode(&flow); err != nil {
		return nil, err
	}

	return &flow, nil
}

func submitRegistration(ctx context.Context, flowID string, params *RegisterParams) (*Session, error) {
	// Implementation for submitting registration to Kratos
	// This would make HTTP calls to Ory Kratos API
	traits, _ := json.Marshal(map[string]interface{}{
		"email":                   params.Email,
		"first_name":              params.FirstName,
		"last_name":               params.LastName,
		"username":                params.Username,
		"hasCompletedOnboarding":  false, // New users haven't completed onboarding
	})
	
	return &Session{
		ID: "mock-session-id",
		Identity: Identity{
			ID:     "mock-user-id",
			Traits: traits,
		},
	}, nil
}

func createLoginFlow(ctx context.Context) (*KratosFlow, error) {
	url := fmt.Sprintf("%s/self-service/login/api", secrets.KratosPublicURL)
	resp, err := http.Post(url, "application/json", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	var flow KratosFlow
	if err := json.NewDecoder(resp.Body).Decode(&flow); err != nil {
		return nil, err
	}

	return &flow, nil
}

func submitLogin(ctx context.Context, flowID string, params *LoginParams) (*Session, error) {
	// Implementation for submitting login to Kratos
	traits, _ := json.Marshal(map[string]interface{}{
		"email": params.Email,
	})
	
	return &Session{
		ID: "mock-session-id",
		Identity: Identity{
			ID:     "mock-user-id",
			Traits: traits,
		},
	}, nil
}

// ==================================================================
// Helper Functions - Ory Hydra Integration
// ==================================================================

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	IDToken      string `json:"id_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
}

type UserInfo struct {
	Subject           string `json:"sub"`
	Email             string `json:"email"`
	GivenName         string `json:"given_name"`
	FamilyName        string `json:"family_name"`
	PreferredUsername string `json:"preferred_username"`
	Picture           string `json:"picture"`
}

func generateTokensViaHydra(ctx context.Context, userID string) (accessToken, refreshToken string, err error) {
	// Implementation for generating tokens via Ory Hydra
	// This is a mock implementation
	return "mock-access-token", "mock-refresh-token", nil
}

func createOAuthAuthorizationURL(ctx context.Context, provider string) (authURL, state string, err error) {
	// Generate random state
	stateBytes := make([]byte, 32)
	if _, err := rand.Read(stateBytes); err != nil {
		return "", "", err
	}
	state = base64.URLEncoding.EncodeToString(stateBytes)

	// Create OAuth URL via Ory Hydra
	authURL = fmt.Sprintf("%s/oauth2/auth?provider=%s&state=%s", secrets.HydraPublicURL, provider, state)
	
	return authURL, state, nil
}

func exchangeOAuthCode(ctx context.Context, code, state, provider string) (*TokenResponse, error) {
	// Implementation for exchanging OAuth code via Ory Hydra
	return &TokenResponse{
		AccessToken:  "mock-access-token",
		RefreshToken: "mock-refresh-token",
		IDToken:      "mock-id-token",
		TokenType:    "Bearer",
		ExpiresIn:    3600,
	}, nil
}

func extractUserInfoFromIDToken(idToken string) (*UserInfo, error) {
	// Parse JWT ID token to extract user information
	return &UserInfo{
		Subject:           "mock-user-id",
		Email:             "<EMAIL>",
		GivenName:         "John",
		FamilyName:        "Doe",
		PreferredUsername: "johndoe",
		Picture:           "https://example.com/avatar.jpg",
	}, nil
}

func validateHydraJWT(ctx context.Context, tokenString string) (*AuthData, error) {
	// Parse JWT token
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		// Fetch public key from Ory Hydra JWKS endpoint
		// This is a simplified implementation
		return []byte("mock-secret"), nil
	})

	if err != nil {
		return nil, err
	}

	if !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Extract claims
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("invalid token claims")
	}

	// Marshal claims to JSON for storage
	claimsJSON, err := json.Marshal(claims)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal claims: %w", err)
	}

	return &AuthData{
		UserID:   claims["sub"].(string),
		Email:    claims["email"].(string),
		Name:     claims["name"].(string),
		Username: claims["preferred_username"].(string),
		Claims:   claimsJSON,
	}, nil
}

func invalidateKratosSession(ctx context.Context, sessionToken string) error {
	// Implementation for invalidating Kratos session
	return nil
}

func revokeHydraTokens(ctx context.Context, token string) error {
	// Implementation for revoking Hydra tokens
	return nil
}

func updateKratosUserTraits(ctx context.Context, userID string, traits map[string]interface{}) error {
	// Implementation for updating user traits in Ory Kratos
	// This would make HTTP calls to Ory Kratos Admin API to update user traits
	
	// For now, this is a mock implementation
	// In a real implementation, you would:
	// 1. Get the current user identity from Kratos Admin API
	// 2. Merge the new traits with existing traits
	// 3. Update the identity via Kratos Admin API
	
	rlog.Info("updating user traits in Kratos", "userID", userID, "traits", traits)
	return nil
}

// ==================================================================
// Health Check
// ==================================================================

type HealthResponse struct {
	Status   string `json:"status"`
	Service  string `json:"service"`
	Provider string `json:"provider"`
	Kratos   string `json:"kratos"`
	Hydra    string `json:"hydra"`
}

//encore:api public method=GET path=/auth/health
func Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:   "healthy",
		Service:  "auth",
		Provider: "ory-stack",
		Kratos:   secrets.KratosPublicURL,
		Hydra:    secrets.HydraPublicURL,
	}, nil
}

// ==================================================================
// Availability Checking Endpoints
// ==================================================================

// CheckEmailAvailabilityParams defines the email availability check parameters
type CheckEmailAvailabilityParams struct {
	Email string `json:"email"`
}

// CheckEmailAvailabilityResponse defines the email availability check response
type CheckEmailAvailabilityResponse struct {
	Available bool   `json:"available"`
	Message   string `json:"message"`
}

//encore:api public method=POST path=/auth/check-email
func CheckEmailAvailability(ctx context.Context, params *CheckEmailAvailabilityParams) (*CheckEmailAvailabilityResponse, error) {
	if params.Email == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("email is required").Err()
	}

	// Check if email exists by trying to get user by email
	// We'll need to add a GetByEmail function to the user service
	// For now, we'll use a simple approach
	exists, err := checkEmailExists(ctx, params.Email)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to check email availability").Err()
	}

	if exists {
		return &CheckEmailAvailabilityResponse{
			Available: false,
			Message:   "This email address is already registered",
		}, nil
	}

	return &CheckEmailAvailabilityResponse{
		Available: true,
		Message:   "Email address is available",
	}, nil
}

// CheckUsernameAvailabilityParams defines the username availability check parameters
type CheckUsernameAvailabilityParams struct {
	Username string `json:"username"`
}

// CheckUsernameAvailabilityResponse defines the username availability check response
type CheckUsernameAvailabilityResponse struct {
	Available bool   `json:"available"`
	Message   string `json:"message"`
}

//encore:api public method=POST path=/auth/check-username
func CheckUsernameAvailability(ctx context.Context, params *CheckUsernameAvailabilityParams) (*CheckUsernameAvailabilityResponse, error) {
	if params.Username == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("username is required").Err()
	}

	// Check if username exists by trying to get user by username
	exists, err := checkUsernameExists(ctx, params.Username)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to check username availability").Err()
	}

	if exists {
		return &CheckUsernameAvailabilityResponse{
			Available: false,
			Message:   "This username is already taken",
		}, nil
	}

	return &CheckUsernameAvailabilityResponse{
		Available: true,
		Message:   "Username is available",
	}, nil
}

// Helper functions to check existence
func checkEmailExists(ctx context.Context, email string) (bool, error) {
	// Call the user service to check email availability
	response, err := user.CheckEmailAvailability(ctx, &user.CheckEmailAvailabilityParams{
		Email: email,
	})
	if err != nil {
		return false, err
	}
	return !response.Available, nil
}

func checkUsernameExists(ctx context.Context, username string) (bool, error) {
	// Call the user service to check username availability
	response, err := user.CheckUsernameAvailability(ctx, &user.CheckUsernameAvailabilityParams{
		Username: username,
	})
	if err != nil {
		return false, err
	}
	return !response.Available, nil
}
