package auth

import (
	"context"
	"fmt"
	"strings"
	"time"

	"encore.dev/beta/errs"
)

// MQTTAuthRequest defines the request structure for MQTT authentication.
type MQTTAuthRequest struct {
	ClientID    string `json:"clientid"`
	Username    string `json:"username"`
	Password    string `json:"password"`
	IPAddress   string `json:"ipaddress"`
	Protocol    string `json:"protocol"`
	PeerCert    string `json:"peercert,omitempty"`
	Mountpoint  string `json:"mountpoint,omitempty"`
	IsSuperuser bool   `json:"is_superuser"`
}

// MQTTAuthResponse defines the response structure for MQTT authentication.
type MQTTAuthResponse struct {
	IsAuthenticated bool   `json:"is_authenticated"`
	UserID          string `json:"user_id,omitempty"`
	IsSuperuser     bool   `json:"is_superuser"`
	ACL             *ACL   `json:"acl,omitempty"`
}

// ACL defines the access control list for a user.
type ACL struct {
	Pub []string `json:"pub,omitempty"`
	Sub []string `json:"sub,omitempty"`
}

// MQTTACLRequest defines the request structure for MQTT ACL checking.
type MQTTACLRequest struct {
	ClientID   string `json:"clientid"`
	Username   string `json:"username"`
	IPAddress  string `json:"ipaddress"`
	Topic      string `json:"topic"`
	Action     string `json:"action"` // "publish" or "subscribe"
	QoS        int    `json:"qos"`
	Retain     bool   `json:"retain,omitempty"`
	Mountpoint string `json:"mountpoint,omitempty"`
}

// MQTTACLResponse defines the response structure for MQTT ACL checking.
type MQTTACLResponse struct {
	IsAllowed bool `json:"is_allowed"`
}

// AuthenticateMQTT authenticates an MQTT client using JWT.
//encore:api public method=POST path=/mqtt/auth
func AuthenticateMQTT(ctx context.Context, req *MQTTAuthRequest) (*MQTTAuthResponse, error) {
	// Validate request
	if req.ClientID == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("clientid is required").Err()
	}
	if req.Username == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("username is required").Err()
	}
	if req.Password == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("password is required").Err()
	}

	// The password is expected to be the JWT token
	token := req.Password

	// First, try to get JWT validation result from cache
	cachedClaims, err := getJWTFromCache(ctx, token)
	if err != nil {
		// Log cache error but continue with validation
		fmt.Printf("JWT cache error: %v\n", err)
	}

	var userID string
	if cachedClaims != nil {
		// Use cached claims
		userID = cachedClaims.UserID
	} else {
		// Validate the JWT token using Ory Stack
		claims, err := validateHydraJWT(ctx, token)
		if err != nil {
			return &MQTTAuthResponse{
				IsAuthenticated: false,
			}, nil
		}

		// Extract user ID from claims
		userID = claims.UserID

		// Cache the validation result
		err = setJWTInCache(ctx, token, &CachedJWTClaims{
			UserID:    userID,
			ExpiresAt: time.Now().Add(jwtCacheTTL),
		})
		if err != nil {
			// Log cache error but continue
			fmt.Printf("Failed to cache JWT: %v\n", err)
		}
	}

	// Try to get ACL from cache
	cachedACL, err := getACLFromCache(ctx, userID)
	if err != nil {
		// Log cache error but continue
		fmt.Printf("ACL cache error: %v\n", err)
	}

	var acl *ACL
	if cachedACL != nil {
		// Use cached ACL
		acl = &ACL{
			Pub: cachedACL.Pub,
			Sub: cachedACL.Sub,
		}
	} else {
		// Generate new ACL
		newACL := generateUserACL(userID)
		acl = &ACL{
			Pub: newACL.Pub,
			Sub: newACL.Sub,
		}

		// Cache the ACL
		err = setACLInCache(ctx, userID, newACL)
		if err != nil {
			// Log cache error but continue
			fmt.Printf("Failed to cache ACL: %v\n", err)
		}
	}

	return &MQTTAuthResponse{
		IsAuthenticated: true,
		UserID:         userID,
		IsSuperuser:    false, // Regular users are never superusers
		ACL:            acl,
	}, nil
}

// CheckMQTTACL checks if an MQTT client has permission to access a topic.
//encore:api public method=POST path=/mqtt/acl
func CheckMQTTACL(ctx context.Context, req *MQTTACLRequest) (*MQTTACLResponse, error) {
	// Validate request
	if req.ClientID == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("clientid is required").Err()
	}
	if req.Username == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("username is required").Err()
	}
	if req.Topic == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("topic is required").Err()
	}
	if req.Action == "" {
		return nil, errs.B().Code(errs.InvalidArgument).Msg("action is required").Err()
	}

	// If it's a system client (e.g., backend services), allow all access
	if strings.HasPrefix(req.ClientID, "system_") {
		return &MQTTACLResponse{IsAllowed: true}, nil
	}

	// Try to get ACL from cache
	cachedACL, err := getACLFromCache(ctx, req.Username)
	if err != nil {
		// Log cache error but continue
		fmt.Printf("ACL cache error: %v\n", err)
	}

	var isAllowed bool
	if cachedACL != nil {
		// Use cached ACL
		switch req.Action {
		case "publish":
			isAllowed = isTopicAllowed(req.Topic, cachedACL.Pub)
		case "subscribe":
			isAllowed = isTopicAllowed(req.Topic, cachedACL.Sub)
		}
	} else {
		// Generate new ACL
		newACL := generateUserACL(req.Username)
		
		// Cache the ACL
		err = setACLInCache(ctx, req.Username, newACL)
		if err != nil {
			// Log cache error but continue
			fmt.Printf("Failed to cache ACL: %v\n", err)
		}

		// Check permissions
		switch req.Action {
		case "publish":
			isAllowed = isTopicAllowed(req.Topic, newACL.Pub)
		case "subscribe":
			isAllowed = isTopicAllowed(req.Topic, newACL.Sub)
		}
	}

	return &MQTTACLResponse{
		IsAllowed: isAllowed,
	}, nil
}

// Helper function to check if a topic matches any of the allowed patterns
func isTopicAllowed(topic string, allowedPatterns []string) bool {
	for _, pattern := range allowedPatterns {
		if topicMatches(topic, pattern) {
			return true
		}
	}
	return false
}

// Helper function to check if a topic matches a pattern
func topicMatches(topic, pattern string) bool {
	// Split topic and pattern into segments
	topicParts := strings.Split(topic, "/")
	patternParts := strings.Split(pattern, "/")

	// If pattern ends with #, it matches all remaining segments
	if len(patternParts) > 0 && patternParts[len(patternParts)-1] == "#" {
		// Remove # from pattern for comparison
		patternParts = patternParts[:len(patternParts)-1]
		// Topic must be at least as long as pattern (excluding #)
		if len(topicParts) < len(patternParts) {
			return false
		}
		// Only compare up to pattern length
		topicParts = topicParts[:len(patternParts)]
	} else if len(topicParts) != len(patternParts) {
		// If no #, lengths must match
		return false
	}

	// Compare each segment
	for i := range patternParts {
		if patternParts[i] != "+" && patternParts[i] != topicParts[i] {
			return false
		}
	}

	return true
} 