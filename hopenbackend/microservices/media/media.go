package media

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"github.com/google/uuid"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

// Database for media service
var mediaDB = sqldb.NewDatabase("media", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

//encore:service
type Service struct{}

// MinIO client configuration
var minioClient *minio.Client

func init() {
	// Initialize MinIO client
	var err error
	minioClient, err = minio.New("localhost:9000", &minio.Options{
		Creds:  credentials.NewStaticV4("minioadmin", "minioadmin", ""),
		Secure: false,
	})
	if err != nil {
		rlog.Error("Failed to initialize MinIO client", "error", err)
	}
}

// MediaFile represents a media file record
type MediaFile struct {
	ID          string    `json:"id"`
	UserID      string    `json:"user_id"`
	FileName    string    `json:"file_name"`
	OriginalName string   `json:"original_name"`
	ContentType string    `json:"content_type"`
	Size        int64     `json:"size"`
	BucketName  string    `json:"bucket_name"`
	ObjectKey   string    `json:"object_key"`
	URL         string    `json:"url"`
	Category    string    `json:"category"` // "avatar", "chat", "bubble", "document"
	IsPublic    bool      `json:"is_public"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// API Request/Response Types

// UploadParams for file upload
type UploadParams struct {
	Category string `json:"category"`
	IsPublic bool   `json:"is_public"`
}

// UploadResponse returns upload result
type UploadResponse struct {
	Success bool      `json:"success"`
	File    MediaFile `json:"file"`
	Message string    `json:"message"`
}

// GetFileParams for retrieving file info
type GetFileParams struct {
	FileID string `json:"file_id"`
}

// GetFileResponse returns file info
type GetFileResponse struct {
	File MediaFile `json:"file"`
}

// GetFilesParams for listing files
type GetFilesParams struct {
	Category string `json:"category,omitempty"`
	Limit    int    `json:"limit,omitempty"`
	Offset   int    `json:"offset,omitempty"`
}

// GetFilesResponse returns file list
type GetFilesResponse struct {
	Files []MediaFile `json:"files"`
	Total int         `json:"total"`
}

// DeleteFileResponse confirms deletion
type DeleteFileResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// Health check response
type HealthResponse struct {
	Status  string `json:"status"`
	Service string `json:"service"`
	Version string `json:"version"`
}

// API Endpoints

// Health check endpoint
//encore:api public method=GET path=/media/health
func (s *Service) Health(ctx context.Context) (*HealthResponse, error) {
	return &HealthResponse{
		Status:  "healthy",
		Service: "media",
		Version: "1.0.0",
	}, nil
}

// Upload file to MinIO
//encore:api auth method=POST path=/media/upload
func (s *Service) UploadFile(ctx context.Context, params *UploadParams) (*UploadResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Note: In a real implementation, you would get the file from the request
	// For now, we'll create a placeholder response
	fileID := uuid.New().String()
	fileName := fmt.Sprintf("%s_%s", userID, fileID)
	bucketName := "hopen-media"
	
	// Ensure bucket exists
	err := s.ensureBucket(ctx, bucketName)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to ensure bucket exists").Err()
	}

	// Create media file record
	mediaFile := MediaFile{
		ID:          fileID,
		UserID:      userID,
		FileName:    fileName,
		OriginalName: "uploaded_file",
		ContentType: "application/octet-stream",
		Size:        0,
		BucketName:  bucketName,
		ObjectKey:   fileName,
		URL:         fmt.Sprintf("http://localhost:9000/%s/%s", bucketName, fileName),
		Category:    params.Category,
		IsPublic:    params.IsPublic,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Save to database
	_, err = mediaDB.Exec(ctx, `
		INSERT INTO media_files (id, user_id, file_name, original_name, content_type, size, bucket_name, object_key, url, category, is_public, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
	`, mediaFile.ID, mediaFile.UserID, mediaFile.FileName, mediaFile.OriginalName, mediaFile.ContentType,
		mediaFile.Size, mediaFile.BucketName, mediaFile.ObjectKey, mediaFile.URL, mediaFile.Category,
		mediaFile.IsPublic, mediaFile.CreatedAt, mediaFile.UpdatedAt)

	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to save media file record").Err()
	}

	return &UploadResponse{
		Success: true,
		File:    mediaFile,
		Message: "File uploaded successfully",
	}, nil
}

// Get file information
//encore:api auth method=GET path=/media/:fileID
func (s *Service) GetFile(ctx context.Context, fileID string) (*GetFileResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	var file MediaFile
	err := mediaDB.QueryRow(ctx, `
		SELECT id, user_id, file_name, original_name, content_type, size, bucket_name, object_key, url, category, is_public, created_at, updated_at
		FROM media_files
		WHERE id = $1 AND (user_id = $2 OR is_public = true)
	`, fileID, userID).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.OriginalName, &file.ContentType,
		&file.Size, &file.BucketName, &file.ObjectKey, &file.URL, &file.Category,
		&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)

	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("file not found").Err()
	}

	return &GetFileResponse{
		File: file,
	}, nil
}

// List user files
//encore:api auth method=GET path=/media
func (s *Service) GetFiles(ctx context.Context, params *GetFilesParams) (*GetFilesResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Build query with filters
	query := `
		SELECT id, user_id, file_name, original_name, content_type, size, bucket_name, object_key, url, category, is_public, created_at, updated_at
		FROM media_files
		WHERE user_id = $1
	`
	args := []interface{}{userID}
	argCount := 1

	// Add category filter
	if params.Category != "" {
		argCount++
		query += fmt.Sprintf(" AND category = $%d", argCount)
		args = append(args, params.Category)
	}

	query += " ORDER BY created_at DESC"

	// Add pagination
	if params.Limit > 0 {
		argCount++
		query += fmt.Sprintf(" LIMIT $%d", argCount)
		args = append(args, params.Limit)

		if params.Offset > 0 {
			argCount++
			query += fmt.Sprintf(" OFFSET $%d", argCount)
			args = append(args, params.Offset)
		}
	}

	rows, err := mediaDB.Query(ctx, query, args...)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to get files").Err()
	}
	defer rows.Close()

	var files []MediaFile
	for rows.Next() {
		var file MediaFile
		err := rows.Scan(&file.ID, &file.UserID, &file.FileName, &file.OriginalName, &file.ContentType,
			&file.Size, &file.BucketName, &file.ObjectKey, &file.URL, &file.Category,
			&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)
		if err != nil {
			continue
		}
		files = append(files, file)
	}

	// Get total count
	countQuery := `SELECT COUNT(*) FROM media_files WHERE user_id = $1`
	countArgs := []interface{}{userID}
	if params.Category != "" {
		countQuery += " AND category = $2"
		countArgs = append(countArgs, params.Category)
	}

	var total int
	err = mediaDB.QueryRow(ctx, countQuery, countArgs...).Scan(&total)
	if err != nil {
		total = len(files)
	}

	return &GetFilesResponse{
		Files: files,
		Total: total,
	}, nil
}

// Delete file
//encore:api auth method=DELETE path=/media/:fileID
func (s *Service) DeleteFile(ctx context.Context, fileID string) (*DeleteFileResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Get file info first
	var file MediaFile
	err := mediaDB.QueryRow(ctx, `
		SELECT id, user_id, file_name, original_name, content_type, size, bucket_name, object_key, url, category, is_public, created_at, updated_at
		FROM media_files
		WHERE id = $1 AND user_id = $2
	`, fileID, userID).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.OriginalName, &file.ContentType,
		&file.Size, &file.BucketName, &file.ObjectKey, &file.URL, &file.Category,
		&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)

	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("file not found").Err()
	}

	// Delete from MinIO
	err = minioClient.RemoveObject(ctx, file.BucketName, file.ObjectKey, minio.RemoveObjectOptions{})
	if err != nil {
		rlog.Error("Failed to delete file from MinIO", "error", err, "file_id", fileID)
		// Continue with database deletion even if MinIO deletion fails
	}

	// Delete from database
	_, err = mediaDB.Exec(ctx, `
		DELETE FROM media_files
		WHERE id = $1 AND user_id = $2
	`, fileID, userID)

	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to delete file record").Err()
	}

	return &DeleteFileResponse{
		Success: true,
		Message: "File deleted successfully",
	}, nil
}

// Generate presigned URL for direct upload
//encore:api auth method=POST path=/media/presigned-url
func (s *Service) GeneratePresignedURL(ctx context.Context, params *UploadParams) (*UploadResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	fileID := uuid.New().String()
	fileName := fmt.Sprintf("%s_%s", userID, fileID)
	bucketName := "hopen-media"

	// Ensure bucket exists
	err := s.ensureBucket(ctx, bucketName)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to ensure bucket exists").Err()
	}

	// Generate presigned URL for upload
	presignedURL, err := minioClient.PresignedPutObject(ctx, bucketName, fileName, time.Hour)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to generate presigned URL").Err()
	}

	// Create placeholder media file record
	mediaFile := MediaFile{
		ID:          fileID,
		UserID:      userID,
		FileName:    fileName,
		OriginalName: "pending_upload",
		ContentType: "application/octet-stream",
		Size:        0,
		BucketName:  bucketName,
		ObjectKey:   fileName,
		URL:         presignedURL.String(),
		Category:    params.Category,
		IsPublic:    params.IsPublic,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return &UploadResponse{
		Success: true,
		File:    mediaFile,
		Message: "Presigned URL generated successfully",
	}, nil
}

// Helper function to ensure bucket exists
func (s *Service) ensureBucket(ctx context.Context, bucketName string) error {
	exists, err := minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		return err
	}

	if !exists {
		err = minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{})
		if err != nil {
			return err
		}
		rlog.Info("Created bucket", "bucket", bucketName)
	}

	return nil
}

// Get file download URL
//encore:api auth method=GET path=/media/:fileID/download
func (s *Service) GetDownloadURL(ctx context.Context, fileID string) (*UploadResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, errs.B().Code(errs.Unauthenticated).Msg("user not authenticated").Err()
	}
	userID := string(uid)

	// Get file info
	var file MediaFile
	err := mediaDB.QueryRow(ctx, `
		SELECT id, user_id, file_name, original_name, content_type, size, bucket_name, object_key, url, category, is_public, created_at, updated_at
		FROM media_files
		WHERE id = $1 AND (user_id = $2 OR is_public = true)
	`, fileID, userID).Scan(
		&file.ID, &file.UserID, &file.FileName, &file.OriginalName, &file.ContentType,
		&file.Size, &file.BucketName, &file.ObjectKey, &file.URL, &file.Category,
		&file.IsPublic, &file.CreatedAt, &file.UpdatedAt)

	if err != nil {
		return nil, errs.B().Code(errs.NotFound).Msg("file not found").Err()
	}

	// Generate presigned URL for download
	presignedURL, err := minioClient.PresignedGetObject(ctx, file.BucketName, file.ObjectKey, time.Hour, nil)
	if err != nil {
		return nil, errs.B().Code(errs.Internal).Msg("failed to generate download URL").Err()
	}

	file.URL = presignedURL.String()

	return &UploadResponse{
		Success: true,
		File:    file,
		Message: "Download URL generated successfully",
	}, nil
} 