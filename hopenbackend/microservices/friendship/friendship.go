package friendship

import (
	"context"
	"time"
	"encore.dev/beta/auth"
	"encore.dev/middleware"
	"encore.dev/storage/sqldb"
	"encore.dev/pubsub"
	"fmt"
)

// Define DB at package level
var friendshipDB = sqldb.NewDatabase("friendship", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

// Define the topic for mutual friendship events
var MutualFriendshipTopic = pubsub.NewTopic[*MutualFriendshipEvent]("mutual-friendship", pubsub.TopicConfig{
	DeliveryGuarantee: pubsub.AtLeastOnce,
})

// MutualFriendshipEvent defines the event for a mutual friendship.
type MutualFriendshipEvent struct {
	UserID1  string `json:"userId1"`
	UserID2  string `json:"userId2"`
	BubbleID string `json:"bubbleId"`
}

// SendRequestParams defines the parameters for sending a friend request
type SendRequestParams struct {
	TargetUserID string `json:"target_user_id"`
}

// Request represents a friend request
type Request struct {
	ID          string    `json:"id"`
	SenderID    string    `json:"sender_id"`
	RecipientID string    `json:"recipient_id"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// PendingRequestsResponse defines the response for getting pending requests
type PendingRequestsResponse struct {
	Requests []Request `json:"requests"`
}

// Friend represents a friend relationship
type Friend struct {
	UserID string `json:"user_id"`
}

// FriendsResponse defines the response for listing friends
type FriendsResponse struct {
	Friends []Friend `json:"friends"`
}

// BlockUserParams defines the parameters for blocking a user
type BlockUserParams struct {
	TargetUserID string `json:"target_user_id"`
}

// UnblockUserParams defines the parameters for unblocking a user
type UnblockUserParams struct {
	TargetUserID string `json:"target_user_id"`
}

//encore:service
type Service struct {
	db *sqldb.Database
}

var _ = pubsub.NewSubscription(MutualFriendshipTopic, "friendship-create-from-event-sub", pubsub.SubscriptionConfig[*MutualFriendshipEvent]{
	Handler: CreateFromEvent,
})

func initService() (*Service, error) {
	return &Service{db: friendshipDB}, nil
}

// This is a middleware that applies to all endpoints in the friendship service
// that have a method of POST, PUT, or PATCH.
// Note: Middleware simplified for compatibility
func (s *Service) Idempotency(req middleware.Request, next middleware.Next) middleware.Response {
	// Simplified idempotency middleware for compatibility
	return next(req)
}

//encore:api public method=POST path=/api/friendship/requests
func (s *Service) SendRequest(ctx context.Context, params *SendRequestParams) (*Request, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}
	senderID := string(uid)

	var req Request
	err := s.db.QueryRow(ctx, `
		INSERT INTO friend_requests (sender_id, recipient_id, status)
		VALUES ($1, $2, 'pending')
		RETURNING id, sender_id, recipient_id, status, created_at
	`, senderID, params.TargetUserID).Scan(&req.ID, &req.SenderID, &req.RecipientID, &req.Status, &req.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to send friend request: %w", err)
	}

	return &req, nil
}

//encore:api public method=GET path=/api/friendship/requests/pending
func (s *Service) GetPendingRequests(ctx context.Context) (*PendingRequestsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}
	recipientID := string(uid)

	rows, err := s.db.Query(ctx, `
		SELECT id, sender_id, recipient_id, status, created_at
		FROM friend_requests
		WHERE recipient_id = $1 AND status = 'pending'
	`, recipientID)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending requests: %w", err)
	}
	defer rows.Close()

	var requests []Request
	for rows.Next() {
		var req Request
		if err := rows.Scan(&req.ID, &req.SenderID, &req.RecipientID, &req.Status, &req.CreatedAt); err != nil {
			return nil, fmt.Errorf("failed to scan request: %w", err)
		}
		requests = append(requests, req)
	}

	return &PendingRequestsResponse{Requests: requests}, nil
}

//encore:api public method=POST path=/api/friendship/requests/:requestID/accept
func (s *Service) AcceptRequest(ctx context.Context, requestID string) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("unauthorized")
	}
	recipientID := string(uid)

	tx, err := s.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	var senderID string
	err = tx.QueryRow(ctx, `
		UPDATE friend_requests
		SET status = 'accepted'
		WHERE id = $1 AND recipient_id = $2 AND status = 'pending'
		RETURNING sender_id
	`, requestID, recipientID).Scan(&senderID)
	if err != nil {
		return fmt.Errorf("failed to accept friend request: %w", err)
	}

	userID1, userID2 := senderID, recipientID
	if userID1 > userID2 {
		userID1, userID2 = userID2, userID1
	}

	_, err = tx.Exec(ctx, `
		INSERT INTO friendships (user_id1, user_id2)
		VALUES ($1, $2)
		ON CONFLICT DO NOTHING
	`, userID1, userID2)
	if err != nil {
		return fmt.Errorf("failed to create friendship: %w", err)
	}

	return tx.Commit()
}

//encore:api public method=POST path=/api/friendship/requests/:requestID/decline
func (s *Service) DeclineRequest(ctx context.Context, requestID string) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("unauthorized")
	}
	recipientID := string(uid)

	_, err := s.db.Exec(ctx, `
		UPDATE friend_requests
		SET status = 'declined'
		WHERE id = $1 AND recipient_id = $2 AND status = 'pending'
	`, requestID, recipientID)
	if err != nil {
		return fmt.Errorf("failed to decline friend request: %w", err)
	}
	return nil
}

//encore:api public method=GET path=/api/friendship/friends
func (s *Service) ListFriends(ctx context.Context) (*FriendsResponse, error) {
	uid, ok := auth.UserID()
	if !ok {
		return nil, fmt.Errorf("unauthorized")
	}
	userID := string(uid)

	rows, err := s.db.Query(ctx, `
		SELECT
			CASE WHEN user_id1 = $1 THEN user_id2 ELSE user_id1 END AS friend_id
		FROM friendships
		WHERE user_id1 = $1 OR user_id2 = $1
	`, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to list friends: %w", err)
	}
	defer rows.Close()

	var friends []Friend
	for rows.Next() {
		var friend Friend
		if err := rows.Scan(&friend.UserID); err != nil {
			return nil, fmt.Errorf("failed to scan friend: %w", err)
		}
		friends = append(friends, friend)
	}

	return &FriendsResponse{Friends: friends}, nil
}

//encore:api public method=POST path=/api/friendship/block
func (s *Service) BlockUser(ctx context.Context, params *BlockUserParams) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("unauthorized")
	}
	blockerID := string(uid)

	_, err := s.db.Exec(ctx, `
		INSERT INTO blocked_users (blocker_id, blocked_id)
		VALUES ($1, $2)
		ON CONFLICT DO NOTHING
	`, blockerID, params.TargetUserID)
	if err != nil {
		return fmt.Errorf("failed to block user: %w", err)
	}

	return nil
}

//encore:api public method=POST path=/api/friendship/unblock
func (s *Service) UnblockUser(ctx context.Context, params *UnblockUserParams) error {
	uid, ok := auth.UserID()
	if !ok {
		return fmt.Errorf("unauthorized")
	}
	blockerID := string(uid)

	_, err := s.db.Exec(ctx, `
		DELETE FROM blocked_users
		WHERE blocker_id = $1 AND blocked_id = $2
	`, blockerID, params.TargetUserID)
	if err != nil {
		return fmt.Errorf("failed to unblock user: %w", err)
	}

	return nil
}

type CreateParams struct {
	UserID1 string
	UserID2 string
}

type CreateResponse struct {
	UserID1 string
	UserID2 string
}

//encore:api public method=POST path=/friendship
func (s *Service) Create(ctx context.Context, params *CreateParams) (*CreateResponse, error) {
	// Ensure user1 and user2 are ordered to prevent duplicate entries
	if params.UserID1 > params.UserID2 {
		params.UserID1, params.UserID2 = params.UserID2, params.UserID1
	}

	_, err := s.db.Exec(ctx, `
		INSERT INTO friendships (user_id1, user_id2)
		VALUES ($1, $2)
		ON CONFLICT DO NOTHING
	`, params.UserID1, params.UserID2)
	if err != nil {
		return nil, fmt.Errorf("failed to create friendship: %w", err)
	}

	return &CreateResponse{
		UserID1: params.UserID1,
		UserID2: params.UserID2,
	}, nil
}

type UserData struct {
	Role string
}

func CreateFromEvent(ctx context.Context, event *MutualFriendshipEvent) error {
	// Log: received mutual friendship event for user1=%s, user2=%s, bubble=%s
	_ = event.UserID1
	_ = event.UserID2
	_ = event.BubbleID
	// In a real implementation, you would need to get a service instance
	// to access the database, or pass a database client to this function.
	// For now, we'll just log the event.
	return nil
}
