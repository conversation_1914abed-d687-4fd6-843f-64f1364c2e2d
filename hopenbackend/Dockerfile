# Build stage - Use older Go version compatible with Encore
FROM golang:1.23-bullseye AS builder

# Install required dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy go mod files first for better caching
COPY go.mod go.sum ./

# Download dependencies with compatible versions
RUN go mod download

# Copy source code
COPY . .

# Set environment variables for consistent builds
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# Build the binary using our custom main.go
RUN go build -a -installsuffix cgo -ldflags="-w -s" -o hopen-backend ./cmd/main.go

# Production stage - Use distroless image for security
FROM gcr.io/distroless/static-debian11:latest

# Add ca-certificates for HTTPS requests
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary
COPY --from=builder /app/hopen-backend /hopen-backend

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD ["/hopen-backend", "-check"] || exit 1

# Set user (security)
USER 65534:65534

# Run the application
ENTRYPOINT ["/hopen-backend"] 