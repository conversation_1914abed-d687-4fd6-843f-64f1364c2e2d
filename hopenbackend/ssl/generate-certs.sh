#!/bin/bash

# SSL Certificate Generation Script for Hopen Backend
# Generates self-signed certificates for development and prepares for Let's Encrypt in production

set -e

DOMAIN=${DOMAIN:-"hopenapp.com"}
CERT_DIR="/ssl"
DEV_CERT_DIR="./ssl"

echo "🔐 Setting up SSL certificates for Hopen Backend..."

# Create certificate directories
mkdir -p "$DEV_CERT_DIR"
mkdir -p "$DEV_CERT_DIR/dev"
mkdir -p "$DEV_CERT_DIR/prod"

# Generate development certificates (self-signed)
echo "📝 Generating development certificates..."

# Create OpenSSL config for development
cat > "$DEV_CERT_DIR/dev/openssl.conf" << EOF
[req]
distinguished_name = req_distinguished_name
req_extensions = v3_req
prompt = no

[req_distinguished_name]
C = FR
ST = France
L = Paris
O = Hopen
OU = Development
CN = localhost

[v3_req]
keyUsage = keyEncipherment, dataEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = *.localhost
DNS.3 = hopen-backend
DNS.4 = ory-kratos
DNS.5 = ory-hydra
IP.1 = 127.0.0.1
IP.2 = ::1
EOF

# Generate private key for development
openssl genrsa -out "$DEV_CERT_DIR/dev/server.key" 2048

# Generate certificate signing request
openssl req -new -key "$DEV_CERT_DIR/dev/server.key" -out "$DEV_CERT_DIR/dev/server.csr" -config "$DEV_CERT_DIR/dev/openssl.conf"

# Generate self-signed certificate
openssl x509 -req -in "$DEV_CERT_DIR/dev/server.csr" -signkey "$DEV_CERT_DIR/dev/server.key" -out "$DEV_CERT_DIR/dev/server.crt" -days 365 -extensions v3_req -extfile "$DEV_CERT_DIR/dev/openssl.conf"

echo "✅ Development certificates generated!"

# Create production certificate configuration
echo "📝 Creating production certificate configuration..."

cat > "$DEV_CERT_DIR/prod/certbot-setup.sh" << 'EOF'
#!/bin/bash

# Production SSL Certificate Setup using Let's Encrypt
# Run this script on your production server

set -e

DOMAIN=${DOMAIN:-"hopenapp.com"}
EMAIL=${SSL_EMAIL:-"<EMAIL>"}

echo "🔐 Setting up Let's Encrypt certificates for production..."

# Install certbot if not present
if ! command -v certbot &> /dev/null; then
    echo "📦 Installing certbot..."
    apt-get update
    apt-get install -y certbot python3-certbot-nginx
fi

# Generate certificates
echo "📝 Generating Let's Encrypt certificates..."
certbot certonly --standalone \
    --email "$EMAIL" \
    --agree-tos \
    --no-eff-email \
    --domains "$DOMAIN,api.$DOMAIN,auth.$DOMAIN"

# Set up auto-renewal
echo "⏰ Setting up certificate auto-renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo "✅ Production certificates configured!"
echo "📋 Certificates location: /etc/letsencrypt/live/$DOMAIN/"
EOF

chmod +x "$DEV_CERT_DIR/prod/certbot-setup.sh"

# Create nginx SSL configuration template
cat > "$DEV_CERT_DIR/prod/nginx-ssl.conf" << EOF
# Nginx SSL Configuration for Hopen Backend
# Place this in your nginx sites-available directory

server {
    listen 80;
    server_name hopenapp.com api.hopenapp.com auth.hopenapp.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name hopenapp.com;
    
    ssl_certificate /etc/letsencrypt/live/hopenapp.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/hopenapp.com/privkey.pem;
    
    # SSL Security Headers
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}

server {
    listen 443 ssl http2;
    server_name api.hopenapp.com;
    
    ssl_certificate /etc/letsencrypt/live/hopenapp.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/hopenapp.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:4000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}

server {
    listen 443 ssl http2;
    server_name auth.hopenapp.com;
    
    ssl_certificate /etc/letsencrypt/live/hopenapp.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/hopenapp.com/privkey.pem;
    
    location /kratos/ {
        proxy_pass http://localhost:4433/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    location /hydra/ {
        proxy_pass http://localhost:4444/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

echo "✅ SSL certificate setup complete!"
echo "📋 Development certificates: $DEV_CERT_DIR/dev/"
echo "📋 Production setup script: $DEV_CERT_DIR/prod/certbot-setup.sh"
echo "📋 Nginx configuration: $DEV_CERT_DIR/prod/nginx-ssl.conf" 