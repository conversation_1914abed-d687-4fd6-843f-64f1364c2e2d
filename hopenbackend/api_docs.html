<!DOCTYPE html>
<html>
<head>
    <title>Hopen <PERSON>end</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: system-ui, -apple-system, sans-serif; margin: 0; padding: 2rem; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 2rem; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 2rem; }
        .status { display: inline-block; padding: 0.5rem 1rem; background: #10b981; color: white; border-radius: 4px; }
        .endpoints { display: grid; gap: 1rem; margin-top: 2rem; }
        .endpoint { padding: 1rem; background: #f8f9fa; border-radius: 4px; border-left: 4px solid #3b82f6; }
        .method { font-weight: bold; color: #3b82f6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 Hopen Backend</h1>
            <div class="status">✅ READY FOR PRODUCTION</div>
        </div>
        
        <h2>API Endpoints</h2>
        <div class="endpoints">
            <div class="endpoint">
                <div class="method">GET</div>
                <div>/health - Health check and system status</div>
            </div>
            <div class="endpoint">
                <div class="method">POST</div>
                <div>/api/auth/* - Authentication & JWT tokens</div>
            </div>
            <div class="endpoint">
                <div class="method">GET/POST</div>
                <div>/api/users/* - User management</div>
            </div>
            <div class="endpoint">
                <div class="method">GET/POST</div>
                <div>/api/bubbles/* - Location-based social bubbles</div>
            </div>
            <div class="endpoint">
                <div class="method">GET/POST</div>
                <div>/api/friendship/* - Friend requests & relationships</div>
            </div>
            <div class="endpoint">
                <div class="method">POST</div>
                <div>/api/notifications/* - Push, email & in-app notifications</div>
            </div>
            <div class="endpoint">
                <div class="method">GET/POST/WS</div>
                <div>/api/chat/* - Real-time chat with MQTT</div>
            </div>
            <div class="endpoint">
                <div class="method">POST</div>
                <div>/api/call/* - Video call functionality</div>
            </div>
            <div class="endpoint">
                <div class="method">GET/POST/DELETE</div>
                <div>/api/media/* - Media upload & management</div>
            </div>
        </div>
        
        <div style="margin-top: 2rem; text-align: center; color: #666;">
            <p>Built with Encore.go • Production Ready • Microservices Architecture</p>
        </div>
    </div>
</body>
</html>