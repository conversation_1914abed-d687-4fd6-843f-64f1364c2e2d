# Hopen Backend Production Environment Configuration
# Copy this file to your production server and customize the values

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
APP_NAME=hopen-backend
APP_VERSION=1.0.0
PORT=4000

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN=hopenapp.com
API_URL=https://api.hopenapp.com
FRONTEND_URL=https://hopenapp.com
AUTH_URL=https://auth.hopenapp.com

# =============================================================================
# ORY STACK CONFIGURATION
# =============================================================================
# Ory Kratos (Identity Management)
KRATOS_PUBLIC_URL=https://auth.hopenapp.com/kratos
KRATOS_ADMIN_URL=http://localhost:4434
KRATOS_DATABASE_URL=postgres://ory:CHANGE_ORY_PASSWORD@localhost:54323/kratos?sslmode=require

# Ory Hydra (OAuth2/OIDC)
HYDRA_PUBLIC_URL=https://auth.hopenapp.com/hydra
HYDRA_ADMIN_URL=http://localhost:4445
HYDRA_DATABASE_URL=postgres://ory:CHANGE_ORY_PASSWORD@localhost:54323/hydra?sslmode=require
HYDRA_JWKS_URL=https://auth.hopenapp.com/hydra/.well-known/jwks.json

# OAuth2 System Key (Generate with: openssl rand -hex 32)
SYSTEM_SECRET=CHANGE_THIS_TO_RANDOM_32_BYTE_HEX_STRING

# =============================================================================
# OAUTH2 PROVIDER CONFIGURATION
# =============================================================================
# Google OAuth2
GOOGLE_CLIENT_ID=your-google-client-id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=CHANGE_GOOGLE_CLIENT_SECRET
GOOGLE_REDIRECT_URI=https://auth.hopenapp.com/auth/callback/google

# Apple OAuth2
APPLE_CLIENT_ID=com.hopen.app.signin
APPLE_CLIENT_SECRET=CHANGE_APPLE_CLIENT_SECRET
APPLE_REDIRECT_URI=https://auth.hopenapp.com/auth/callback/apple
APPLE_TEAM_ID=CHANGE_APPLE_TEAM_ID
APPLE_KEY_ID=CHANGE_APPLE_KEY_ID

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Main Application Database
DATABASE_URL=postgres://hopen:CHANGE_HOPEN_DB_PASSWORD@localhost:5432/hopen?sslmode=require
DATABASE_MAX_CONNECTIONS=20
DATABASE_IDLE_TIMEOUT=30s

# Ory Database
ORY_DATABASE_URL=postgres://ory:CHANGE_ORY_PASSWORD@localhost:54323/postgres?sslmode=require

# =============================================================================
# REDIS/VALKEY CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=CHANGE_REDIS_PASSWORD
REDIS_MAX_CONNECTIONS=10
REDIS_IDLE_TIMEOUT=300s

# =============================================================================
# MQTT CONFIGURATION (EMQX)
# =============================================================================
MQTT_URL=mqtt://localhost:1883
MQTT_USERNAME=hopen
MQTT_PASSWORD=CHANGE_MQTT_PASSWORD
MQTT_CLIENT_ID=hopen-backend
MQTT_KEEP_ALIVE=60

# EMQX Management
EMQX_DASHBOARD_URL=http://localhost:18083
EMQX_API_KEY=CHANGE_EMQX_API_KEY
EMQX_API_SECRET=CHANGE_EMQX_API_SECRET

# =============================================================================
# MINIO CONFIGURATION (Object Storage)
# =============================================================================
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=CHANGE_MINIO_ACCESS_KEY
MINIO_SECRET_KEY=CHANGE_MINIO_SECRET_KEY
MINIO_USE_SSL=true
MINIO_REGION=us-east-1

# MinIO Buckets
MINIO_BUCKET_PROFILES=hopen-profiles
MINIO_BUCKET_MEDIA=hopen-media
MINIO_BUCKET_DOCUMENTS=hopen-documents

# =============================================================================
# EMAIL CONFIGURATION (AWS SES)
# =============================================================================
AWS_REGION=eu-west-1
AWS_ACCESS_KEY_ID=CHANGE_AWS_ACCESS_KEY
AWS_SECRET_ACCESS_KEY=CHANGE_AWS_SECRET_KEY
SES_FROM_EMAIL=<EMAIL>
SES_FROM_NAME=Hopen

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Configuration
JWT_SECRET=CHANGE_THIS_TO_RANDOM_JWT_SECRET_AT_LEAST_32_CHARS
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# CORS Configuration
CORS_ORIGINS=https://hopenapp.com,https://api.hopenapp.com,https://auth.hopenapp.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW=15m
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_CERT_PATH=/etc/letsencrypt/live/hopenapp.com/fullchain.pem
SSL_KEY_PATH=/etc/letsencrypt/live/hopenapp.com/privkey.pem
SSL_CA_PATH=/etc/letsencrypt/live/hopenapp.com/chain.pem

# =============================================================================
# MONITORING & LOGGING
# =============================================================================
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=/var/log/hopen/backend.log

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Go Runtime
GOMAXPROCS=0  # Use all available CPUs
GOGC=100      # Default garbage collection target

# Connection Pools
MAX_IDLE_CONNS=10
MAX_OPEN_CONNS=100
CONN_MAX_LIFETIME=1h

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=hopen-backups
BACKUP_ENCRYPTION_KEY=CHANGE_BACKUP_ENCRYPTION_KEY

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_ANALYTICS=true
FEATURE_PUSH_NOTIFICATIONS=true
FEATURE_VIDEO_CALLS=true
FEATURE_FILE_SHARING=true
FEATURE_REAL_TIME_MESSAGING=true

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# PostHog Analytics
POSTHOG_API_KEY=CHANGE_POSTHOG_API_KEY
POSTHOG_HOST=https://app.posthog.com

# Firebase Cloud Messaging
FCM_SERVER_KEY=CHANGE_FCM_SERVER_KEY
FCM_PROJECT_ID=hopen-app

# WebRTC Configuration
TURN_SERVER_URL=turn:turn.hopenapp.com:3478
TURN_USERNAME=hopen
TURN_PASSWORD=CHANGE_TURN_PASSWORD
STUN_SERVER_URL=stun:stun.hopenapp.com:3478 