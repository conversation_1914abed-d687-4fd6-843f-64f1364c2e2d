#!/bin/bash

# Setup OAuth2 clients for Hopen backend
# This script registers OAuth2 clients with Ory Hydra

set -e

HYDRA_ADMIN_URL=${HYDRA_ADMIN_URL:-"http://localhost:4445"}
OAUTH_CLIENTS_FILE="${OAUTH_CLIENTS_FILE:-/ory/oauth-clients.json}"
if [ ! -f "$OAUTH_CLIENTS_FILE" ]; then
    OAUTH_CLIENTS_FILE="./ory/oauth-clients.json"
fi

echo "🔧 Setting up OAuth2 clients for Hopen..."

# Wait for Hydra to be ready
echo "⏳ Waiting for Hydra to be ready..."
until curl -s "${HYDRA_ADMIN_URL}/health/ready" > /dev/null 2>&1; do
    echo "Waiting for Hydra admin API..."
    sleep 2
done

echo "✅ Hydra is ready!"

# Register each OAuth2 client
jq -c '.[]' "$OAUTH_CLIENTS_FILE" | while read -r client; do
    CLIENT_ID=$(echo "$client" | jq -r '.client_id')
    echo "📝 Registering OAuth2 client: $CLIENT_ID"
    
    # Always create new client (will fail silently if exists)
    echo "➕ Creating client $CLIENT_ID..."
    curl -X POST \
        -H "Content-Type: application/json" \
        -d "$client" \
        "${HYDRA_ADMIN_URL}/admin/clients" \
        > /dev/null 2>&1
    
    echo "✅ Client $CLIENT_ID registered successfully"
done

echo "🎉 All OAuth2 clients registered successfully!"

# List registered clients for verification
echo "📋 Registered OAuth2 clients:"
curl -s "${HYDRA_ADMIN_URL}/admin/clients" | jq -r '.[] | "- " + .client_name + " (" + .client_id + ")"'

echo "🔐 OAuth2 setup complete!" 