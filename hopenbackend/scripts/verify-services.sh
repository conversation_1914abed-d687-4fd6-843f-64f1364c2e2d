#!/bin/bash

# 🔍 Hopen Backend Service Verification Script
# Quick health check for all services

set -e

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[✅ PASS]${NC} $1"
}

print_error() {
    echo -e "${RED}[❌ FAIL]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[⚠️  WARN]${NC} $1"
}

echo "🔍 Hopen Backend Service Verification"
echo "====================================="

# Check if containers are running
print_status "Checking container status..."
if ! docker-compose ps | grep -q "Up"; then
    print_error "No containers are running. Please start services first."
    echo "Run: ./scripts/docker-setup.sh"
    exit 1
fi

# Service health checks
services_passed=0
services_total=8

# 1. Main Backend
print_status "Testing Hopen Backend API..."
if response=$(curl -s -w "%{http_code}" http://localhost:4000/health); then
    http_code="${response: -3}"
    if [[ "$http_code" == "200" ]]; then
        print_success "Hopen Backend API (Port 4000)"
        ((services_passed++))
    else
        print_error "Hopen Backend API returned HTTP $http_code"
    fi
else
    print_error "Hopen Backend API not responding"
fi

# 2. PostgreSQL
print_status "Testing PostgreSQL..."
if docker-compose exec -T postgres pg_isready -U hopen >/dev/null 2>&1; then
    print_success "PostgreSQL Database (Port 5432)"
    ((services_passed++))
else
    print_error "PostgreSQL not ready"
fi

# 3. Redis
print_status "Testing Redis..."
if docker-compose exec -T redis redis-cli ping >/dev/null 2>&1; then
    print_success "Redis Cache (Port 6379)"
    ((services_passed++))
else
    print_error "Redis not responding"
fi

# 4. EMQX MQTT
print_status "Testing EMQX MQTT..."
if curl -s -f http://localhost:18083 >/dev/null; then
    print_success "EMQX MQTT Broker (Port 1883, Dashboard 18083)"
    ((services_passed++))
else
    print_error "EMQX not responding"
fi

# 5. MinIO
print_status "Testing MinIO..."
if curl -s -f http://localhost:9001 >/dev/null; then
    print_success "MinIO Object Storage (API 9000, Console 9001)"
    ((services_passed++))
else
    print_error "MinIO not responding"
fi

# 6. Ory Kratos
print_status "Testing Ory Kratos..."
if curl -s -f http://localhost:4434/health/ready >/dev/null; then
    print_success "Ory Kratos Identity (Public 4433, Admin 4434)"
    ((services_passed++))
else
    print_error "Ory Kratos not ready"
fi

# 7. Ory Hydra
print_status "Testing Ory Hydra..."
if curl -s -f http://localhost:4445/health/ready >/dev/null; then
    print_success "Ory Hydra OAuth2 (Public 4444, Admin 4445)"
    ((services_passed++))
else
    print_error "Ory Hydra not ready"
fi

# 8. API Endpoints
print_status "Testing API endpoints..."
endpoints_passed=0
endpoints_total=8

api_endpoints=("auth" "users" "bubbles" "friendship" "notifications" "chat" "call" "media")

for endpoint in "${api_endpoints[@]}"; do
    if curl -s -f http://localhost:4000/api/$endpoint >/dev/null; then
        ((endpoints_passed++))
    fi
done

if [[ $endpoints_passed -eq $endpoints_total ]]; then
    print_success "All API Endpoints (/api/*)"
    ((services_passed++))
else
    print_warning "API Endpoints ($endpoints_passed/$endpoints_total working)"
fi

echo ""
echo "====================================="
echo "🏆 VERIFICATION SUMMARY"
echo "====================================="

if [[ $services_passed -eq $services_total ]]; then
    echo -e "${GREEN}✅ ALL SERVICES HEALTHY ($services_passed/$services_total)${NC}"
    echo ""
    echo "🌐 Service URLs:"
    echo "  • Main API:      http://localhost:4000"
    echo "  • Health Check:  http://localhost:4000/health"
    echo "  • EMQX Dashboard: http://localhost:18083 (admin/public)"
    echo "  • MinIO Console:  http://localhost:9001 (minioadmin/minioadmin)"
    echo "  • Ory Kratos:    http://localhost:4434"
    echo "  • Ory Hydra:     http://localhost:4445"
    echo ""
    echo "🎉 Your Hopen backend is fully operational!"
    exit 0
else
    echo -e "${RED}❌ SOME SERVICES FAILED ($services_passed/$services_total healthy)${NC}"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "  • Check logs: docker-compose logs"
    echo "  • Restart services: docker-compose restart"
    echo "  • Full reset: ./scripts/docker-setup.sh --clean-volumes"
    exit 1
fi 