#!/bin/bash

# 🚀 Hopen Backend Docker Setup Script
# This script sets up and verifies the entire Hopen backend on Docker Desktop

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Function to check if Docker is running
check_docker() {
    print_header "🐳 CHECKING DOCKER SETUP"
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker Desktop first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    
    print_success "Docker is running"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available."
        exit 1
    fi
    
    print_success "Docker Compose is available"
}

# Function to clean up existing containers and volumes
cleanup() {
    print_header "🧹 CLEANING UP EXISTING CONTAINERS"
    
    print_status "Stopping existing containers..."
    docker-compose down --remove-orphans 2>/dev/null || true
    
    print_status "Removing existing volumes (if requested)..."
    if [[ "$1" == "--clean-volumes" ]]; then
        docker-compose down -v 2>/dev/null || true
        print_warning "All data volumes have been removed"
    fi
    
    print_status "Pruning unused Docker resources..."
    docker system prune -f
    
    print_success "Cleanup completed"
}

# Function to build the application
build_application() {
    print_header "🏗️ BUILDING HOPEN BACKEND"
    
    print_status "Building Docker images..."
    docker-compose build --no-cache
    
    print_success "Application built successfully"
}

# Function to start all services
start_services() {
    print_header "🚀 STARTING ALL SERVICES"
    
    print_status "Starting infrastructure services first..."
    docker-compose up -d postgres redis emqx minio ory-db
    
    print_status "Waiting for infrastructure to be ready..."
    sleep 30
    
    print_status "Starting Ory Stack..."
    docker-compose up -d ory-kratos-migrate ory-hydra-migrate
    sleep 10
    docker-compose up -d ory-kratos ory-hydra
    sleep 20
    
    print_status "Setting up OAuth clients..."
    docker-compose up oauth-setup
    
    print_status "Starting main application..."
    docker-compose up -d hopen-backend
    
    print_success "All services started"
}

# Function to start monitoring stack
start_monitoring() {
    print_header "📊 STARTING MONITORING STACK"
    
    if [[ "$1" == "--with-monitoring" ]]; then
        print_status "Creating monitoring network..."
        docker network create hopen-network 2>/dev/null || true
        
        print_status "Starting monitoring services..."
        docker-compose -f monitoring/docker-compose.monitoring.yml up -d
        
        print_success "Monitoring stack started"
        print_status "Grafana: http://localhost:3000 (admin/admin123)"
        print_status "Prometheus: http://localhost:9090"
    else
        print_status "Skipping monitoring stack (use --with-monitoring to enable)"
    fi
}

# Function to verify service health
verify_services() {
    print_header "🔍 VERIFYING SERVICE HEALTH"
    
    # Wait for services to be fully ready
    print_status "Waiting for services to initialize..."
    sleep 30
    
    # Check main application
    print_status "Checking Hopen Backend..."
    if curl -f -s http://localhost:4000/health > /dev/null; then
        print_success "✅ Hopen Backend is healthy"
    else
        print_error "❌ Hopen Backend is not responding"
        return 1
    fi
    
    # Check PostgreSQL
    print_status "Checking PostgreSQL..."
    if docker-compose exec -T postgres pg_isready -U hopen > /dev/null 2>&1; then
        print_success "✅ PostgreSQL is healthy"
    else
        print_error "❌ PostgreSQL is not ready"
        return 1
    fi
    
    # Check Redis
    print_status "Checking Redis..."
    if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_success "✅ Redis is healthy"
    else
        print_error "❌ Redis is not responding"
        return 1
    fi
    
    # Check EMQX
    print_status "Checking EMQX MQTT Broker..."
    if curl -f -s http://localhost:18083 > /dev/null; then
        print_success "✅ EMQX is healthy"
    else
        print_error "❌ EMQX is not responding"
        return 1
    fi
    
    # Check MinIO
    print_status "Checking MinIO..."
    if curl -f -s http://localhost:9001 > /dev/null; then
        print_success "✅ MinIO is healthy"
    else
        print_error "❌ MinIO is not responding"
        return 1
    fi
    
    # Check Ory Kratos
    print_status "Checking Ory Kratos..."
    if curl -f -s http://localhost:4434/health/ready > /dev/null; then
        print_success "✅ Ory Kratos is healthy"
    else
        print_error "❌ Ory Kratos is not ready"
        return 1
    fi
    
    # Check Ory Hydra
    print_status "Checking Ory Hydra..."
    if curl -f -s http://localhost:4445/health/ready > /dev/null; then
        print_success "✅ Ory Hydra is healthy"
    else
        print_error "❌ Ory Hydra is not ready"
        return 1
    fi
    
    print_success "All services are healthy! 🎉"
}

# Function to test API endpoints
test_endpoints() {
    print_header "🧪 TESTING API ENDPOINTS"
    
    # Test health endpoint
    print_status "Testing health endpoint..."
    if response=$(curl -s http://localhost:4000/health); then
        print_success "✅ Health endpoint working"
        echo "Response: $response" | jq '.' 2>/dev/null || echo "Response: $response"
    else
        print_error "❌ Health endpoint failed"
        return 1
    fi
    
    # Test auth endpoint
    print_status "Testing auth endpoint..."
    if response=$(curl -s http://localhost:4000/api/auth); then
        print_success "✅ Auth endpoint working"
    else
        print_error "❌ Auth endpoint failed"
        return 1
    fi
    
    # Test other service endpoints
    local endpoints=("users" "bubbles" "friendship" "notifications" "chat" "call" "media")
    
    for endpoint in "${endpoints[@]}"; do
        print_status "Testing $endpoint endpoint..."
        if curl -f -s http://localhost:4000/api/$endpoint > /dev/null; then
            print_success "✅ $endpoint endpoint working"
        else
            print_error "❌ $endpoint endpoint failed"
        fi
    done
    
    print_success "API endpoint testing completed! 🎉"
}

# Function to show service URLs
show_urls() {
    print_header "🌐 SERVICE URLS"
    
    echo -e "${CYAN}Main Application:${NC}"
    echo "  🚀 Hopen Backend:     http://localhost:4000"
    echo "  📋 Health Check:     http://localhost:4000/health"
    echo "  🔗 API Endpoints:    http://localhost:4000/api"
    echo ""
    
    echo -e "${CYAN}Infrastructure:${NC}"
    echo "  🗄️  PostgreSQL:       localhost:5432 (hopen/password)"
    echo "  ⚡ Redis:            localhost:6379"
    echo "  📡 EMQX Dashboard:   http://localhost:18083 (admin/public)"
    echo "  📁 MinIO Console:    http://localhost:9001 (minioadmin/minioadmin)"
    echo ""
    
    echo -e "${CYAN}Authentication (Ory Stack):${NC}"
    echo "  👤 Kratos Public:    http://localhost:4433"
    echo "  🔧 Kratos Admin:     http://localhost:4434"
    echo "  🔐 Hydra Public:     http://localhost:4444"
    echo "  ⚙️  Hydra Admin:      http://localhost:4445"
    echo ""
    
    if docker-compose -f monitoring/docker-compose.monitoring.yml ps | grep -q "Up"; then
        echo -e "${CYAN}Monitoring (if enabled):${NC}"
        echo "  📊 Grafana:          http://localhost:3000 (admin/admin123)"
        echo "  📈 Prometheus:       http://localhost:9090"
        echo "  🚨 AlertManager:     http://localhost:9093"
        echo ""
    fi
}

# Function to show logs
show_logs() {
    print_header "📋 SHOWING RECENT LOGS"
    
    print_status "Recent logs from all services:"
    docker-compose logs --tail=20
}

# Function to show container status
show_status() {
    print_header "📊 CONTAINER STATUS"
    
    print_status "Current container status:"
    docker-compose ps
    
    echo ""
    print_status "Resource usage:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# Main execution
main() {
    print_header "🏆 HOPEN BACKEND DOCKER SETUP"
    echo -e "${CYAN}Enterprise-Grade Production-Ready System${NC}"
    echo ""
    
    # Parse arguments
    CLEAN_VOLUMES=false
    WITH_MONITORING=false
    SHOW_LOGS=false
    SHOW_STATUS=false
    
    for arg in "$@"; do
        case $arg in
            --clean-volumes)
                CLEAN_VOLUMES=true
                ;;
            --with-monitoring)
                WITH_MONITORING=true
                ;;
            --logs)
                SHOW_LOGS=true
                ;;
            --status)
                SHOW_STATUS=true
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo ""
                echo "Options:"
                echo "  --clean-volumes    Remove all data volumes (fresh start)"
                echo "  --with-monitoring  Start monitoring stack (Grafana, Prometheus)"
                echo "  --logs            Show recent logs after setup"
                echo "  --status          Show container status after setup"
                echo "  --help            Show this help message"
                echo ""
                echo "Examples:"
                echo "  $0                           # Basic setup"
                echo "  $0 --clean-volumes          # Fresh start with clean data"
                echo "  $0 --with-monitoring         # Include monitoring stack"
                echo "  $0 --clean-volumes --with-monitoring --logs  # Full setup with logs"
                exit 0
                ;;
        esac
    done
    
    # Execute setup steps
    check_docker
    
    if [[ "$CLEAN_VOLUMES" == true ]]; then
        cleanup --clean-volumes
    else
        cleanup
    fi
    
    build_application
    start_services
    
    if [[ "$WITH_MONITORING" == true ]]; then
        start_monitoring --with-monitoring
    fi
    
    verify_services
    test_endpoints
    show_urls
    
    if [[ "$SHOW_LOGS" == true ]]; then
        show_logs
    fi
    
    if [[ "$SHOW_STATUS" == true ]]; then
        show_status
    fi
    
    print_header "🎉 SETUP COMPLETED SUCCESSFULLY!"
    echo -e "${GREEN}Your Hopen backend is now running on Docker Desktop!${NC}"
    echo -e "${CYAN}Visit http://localhost:4000/health to verify everything is working.${NC}"
    echo ""
    echo -e "${YELLOW}To stop all services: docker-compose down${NC}"
    echo -e "${YELLOW}To view logs: docker-compose logs -f${NC}"
    echo -e "${YELLOW}To restart: docker-compose restart${NC}"
}

# Run main function with all arguments
main "$@" 