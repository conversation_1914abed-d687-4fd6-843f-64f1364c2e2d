services:
  # Hopen Backend Application
  hopen-backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "4000:4000"
    environment:
      - PORT=4000
      - NODE_ENV=development
      - ENVIRONMENT=development
      - LOG_LEVEL=debug
      
      # Database Configuration
      - DATABASE_URL=***************************************/hopen?sslmode=disable
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_DB=hopen
      - POSTGRES_USER=hopen
      - POSTGRES_PASSWORD=hopen123
      
      # ArangoDB Configuration
      - ARANGODB_HOST=arangodb
      - ARANGODB_PORT=8529
      - ARANGODB_DATABASE=hopen_social
      - ARANGODB_USERNAME=root
      - ARANGODB_PASSWORD=hopen123
      
      # Cassandra Configuration
      - CASSANDRA_HOSTS=cassandra:9042
      - CASSANDRA_KEYSPACE=hopen_messages
      
      # Valkey Configuration
      - VALKEY_HOST=valkey
      - VALKEY_PORT=6379
      
      # MQTT Configuration
      - MQTT_URL=mqtt://emqx:1883
      - EMQX_HOST=emqx
      - EMQX_PORT=1883
      - EMQX_USERNAME=admin
      - EMQX_PASSWORD=public
      
      # NATS Configuration
      - NATS_URL=nats://nats:4222
      - NATS_CONNECTION_NAME=hopen-backend-prod
      - NATS_ENABLE_JETSTREAM=true
      
      # MinIO Configuration
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=hopen
      - MINIO_SECRET_KEY=hopen123
      - MINIO_USE_SSL=false
      
      # JWT Configuration
      - JWT_SECRET=hopen-production-jwt-secret-256-bit-key-2024-secure-token-generation
      - JWT_REFRESH_SECRET=hopen-production-refresh-secret-256-bit-key-2024-secure-refresh
      
      # OAuth Configuration
      - GOOGLE_CLIENT_ID=257996495540-8555p6h5grhi9qmqrts661sro96kfd7.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - APPLE_TEAM_ID=8623VTFKZF
      - APPLE_KEY_ID=CW89ABQD5U
      - APPLE_CLIENT_ID=com.hopenapp.hopen
      
      # Firebase Configuration
      - FIREBASE_PROJECT_ID=hopen-id
      - FIREBASE_PRIVATE_KEY_ID=257996495540
      
      # Ory Stack Configuration
      - KRATOS_PUBLIC_URL=http://kratos:4433
      - KRATOS_ADMIN_URL=http://kratos:4434
      - HYDRA_PUBLIC_URL=http://hydra:4444
      - HYDRA_ADMIN_URL=http://hydra:4445
      - HYDRA_JWKS_URL=http://hydra:4444/.well-known/jwks.json
      
      # WebRTC Configuration
      - WEBRTC_HOST=webrtc-server
      - WEBRTC_PORT=8080
      - WEBRTC_STUN_PORT=3478
      
      # FCM Configuration
      - FCMServerKey=development-fcm-key-placeholder-not-functional
      - FCMAPIKey=your-fcm-api-key
      
      # Monitoring
      - PROMETHEUS_ENABLED=true
      - PROMETHEUS_METRICS_PATH=/metrics
      - PROMETHEUS_PORT=9090
      
    depends_on:
      - postgres
      - arangodb
      - cassandra
      - valkey
      - emqx
      - minio
      - nats
      - webrtc-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: hopen
      POSTGRES_PASSWORD: hopen123
      POSTGRES_DB: hopen
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hopen"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ArangoDB Multi-Model Database
  arangodb:
    image: arangodb/arangodb:3.11
    environment:
      ARANGO_ROOT_PASSWORD: hopen123
    ports:
      - "8529:8529"
    volumes:
      - arangodb_data:/var/lib/arangodb3
      - arangodb_apps_data:/var/lib/arangodb3-apps
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8529/_api/version"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Cassandra Database for Messages
  cassandra:
    image: cassandra:4.1
    environment:
      CASSANDRA_CLUSTER_NAME: HopenCluster
      CASSANDRA_DC: datacenter1
      CASSANDRA_RACK: rack1
      CASSANDRA_ENDPOINT_SNITCH: GossipingPropertyFileSnitch
      CASSANDRA_NUM_TOKENS: 128
    ports:
      - "9042:9042"
    volumes:
      - cassandra_data:/var/lib/cassandra
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cqlsh", "-e", "describe keyspaces"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Valkey (Redis alternative) for Caching
  valkey:
    image: valkey/valkey:7.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - valkey_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "valkey-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # EMQX MQTT Broker
  emqx:
    image: emqx/emqx:5.4.0
    ports:
      - "1883:1883"      # MQTT
      - "8083:8083"      # WebSocket
      - "8084:8084"      # WSS
      - "18083:18083"    # Dashboard
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=127.0.0.1
      - EMQX_DASHBOARD__DEFAULT_USERNAME=admin
      - EMQX_DASHBOARD__DEFAULT_PASSWORD=public
    volumes:
      - emqx_data:/opt/emqx/data
      - emqx_logs:/opt/emqx/log
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx", "ctl", "status"]
      interval: 10s
      timeout: 5s
      retries: 5

  # NATS Message Queue
  nats:
    image: nats:2.10-alpine
    ports:
      - "4222:4222"      # Client port
      - "8222:8222"      # HTTP monitoring port
      - "6222:6222"      # Routing port for clustering
    command: ["-js", "-m", "8222"]
    volumes:
      - nats_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8222/"]
      interval: 10s
      timeout: 5s
      retries: 5

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"      # API
      - "9001:9001"      # Console
    environment:
      MINIO_ROOT_USER: hopen
      MINIO_ROOT_PASSWORD: hopen123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # WebRTC Server for Real-time Communication
  webrtc-server:
    image: kurento/kurento-media-server:6.18
    ports:
      - "8080:8080"      # WebRTC API
      - "3478:3478/udp"  # STUN server
    environment:
      - KMS_TURN_URL=kurento:kurento@localhost:3478
    volumes:
      - webrtc_logs:/var/log/kurento-media-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/kurento"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Ory Stack (Authentication & Authorization)
  ory-db:
    image: postgres:15-alpine
    environment:
      POSTGRES_PASSWORD: ory_password
      POSTGRES_USER: ory
      POSTGRES_DB: ory
    ports:
      - "54323:5432"
    volumes:
      - ory_db_data:/var/lib/postgresql/data
      - ./ory-init.sql:/docker-entrypoint-initdb.d/ory-init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ory"]
      interval: 10s
      timeout: 5s
      retries: 5

  ory-kratos-migrate:
    image: oryd/kratos:v1.0.0
    environment:
      DSN: ***************************************/kratos?sslmode=disable
    volumes:
      - ./ory:/etc/config/kratos
    command: -c /etc/config/kratos/kratos.yml migrate sql -e --yes
    depends_on:
      ory-db:
        condition: service_healthy
    restart: "no"

  ory-hydra-migrate:
    image: oryd/hydra:v2.2.0
    environment:
      DSN: ***************************************/hydra?sslmode=disable
    command: migrate sql -e --yes
    depends_on:
      ory-db:
        condition: service_healthy
    restart: "no"

  ory-kratos:
    image: oryd/kratos:v1.0.0
    ports:
      - "4433:4433" # public
      - "4434:4434" # admin
    restart: unless-stopped
    environment:
      DSN: ***************************************/kratos?sslmode=disable
      LOG_LEVEL: debug
    command: serve -c /etc/config/kratos/kratos.yml --dev --watch-courier
    volumes:
      - ./ory:/etc/config/kratos
    depends_on:
      - ory-kratos-migrate

  ory-hydra:
    image: oryd/hydra:v2.2.0
    ports:
      - "4444:4444" # public
      - "4445:4445" # admin
    command: serve all -c /etc/config/hydra/hydra.yml --dev
    volumes:
      - ./ory:/etc/config/hydra
    environment:
      DSN: ***************************************/hydra?sslmode=disable
    restart: unless-stopped
    depends_on:
      - ory-hydra-migrate
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:4444/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 5

  oauth-setup:
    image: curlimages/curl:latest
    volumes:
      - ./ory/oauth-clients.json:/ory/oauth-clients.json:ro
      - ./scripts/setup-oauth-clients.sh:/scripts/setup-oauth-clients.sh:ro
    command: ["/bin/sh", "/scripts/setup-oauth-clients.sh"]
    depends_on:
      ory-hydra:
        condition: service_healthy
    restart: "no"

volumes:
  postgres_data:
  arangodb_data:
  arangodb_apps_data:
  cassandra_data:
  valkey_data:
  emqx_data:
  emqx_logs:
  nats_data:
  minio_data:
  webrtc_logs:
  ory_db_data:

networks:
  default:
    name: hopen-network 