// Package resilience provides enterprise-grade resilience patterns
// including circuit breakers, retries, timeouts, and bulkheads.
package resilience

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// CircuitBreakerState represents the state of a circuit breaker
type CircuitBreakerState int32

// CircuitBreaker implements the Circuit Breaker pattern for resilience
type CircuitBreaker struct {
	name          string
	config        Config
	state         State
	failureCount  int
	successCount  int
	lastFailTime  time.Time
	nextAttempt   time.Time
	mutex         sync.RWMutex
	metrics       *CircuitBreakerMetrics
	onStateChange func(name string, from State, to State)
}

// State represents the circuit breaker state
type State int

const (
	StateClosed State = iota
	StateHalfOpen
	StateOpen
)

func (s State) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateHalfOpen:
		return "HALF_OPEN"
	case StateOpen:
		return "OPEN"
	default:
		return "UNKNOWN"
	}
}

// Config holds circuit breaker configuration
type Config struct {
	Name                string
	MaxRequests         uint32        // Max requests allowed in half-open state
	Interval            time.Duration // Statistical window for failure counting
	Timeout             time.Duration // How long circuit stays open
	ReadyToTrip         ReadyToTripFunc
	OnStateChange       func(name string, from State, to State)
	IsSuccessful        func(err error) bool
}

// ReadyToTripFunc determines if circuit should trip to open state
type ReadyToTripFunc func(counts Counts) bool

// Counts holds circuit breaker statistics
type Counts struct {
	Requests        uint32
	TotalSuccesses  uint32
	TotalFailures   uint32
	ConsecutiveSuccesses uint32
	ConsecutiveFailures  uint32
}

// CircuitBreakerMetrics holds Prometheus metrics
type CircuitBreakerMetrics struct {
	state         *prometheus.GaugeVec
	requests      *prometheus.CounterVec
	failures      *prometheus.CounterVec
	successes     *prometheus.CounterVec
	trips         *prometheus.CounterVec
	duration      *prometheus.HistogramVec
	rejections    *prometheus.CounterVec
}

// ExecuteFunc represents a function that can be protected by circuit breaker
type ExecuteFunc func() (interface{}, error)

// Manager manages multiple circuit breakers
type Manager struct {
	breakers map[string]*CircuitBreaker
	mutex    sync.RWMutex
	metrics  *CircuitBreakerMetrics
}

// NewCircuitBreaker creates a new circuit breaker instance
func NewCircuitBreaker(config Config) *CircuitBreaker {
	if config.ReadyToTrip == nil {
		config.ReadyToTrip = DefaultReadyToTrip
	}
	
	if config.IsSuccessful == nil {
		config.IsSuccessful = DefaultIsSuccessful
	}

	cb := &CircuitBreaker{
		name:          config.Name,
		config:        config,
		state:         StateClosed,
		metrics:       newCircuitBreakerMetrics(),
		onStateChange: config.OnStateChange,
	}

	// Initialize metrics
	cb.metrics.state.WithLabelValues(cb.name).Set(float64(StateClosed))

	return cb
}

// Execute executes the given function with circuit breaker protection
func (cb *CircuitBreaker) Execute(fn ExecuteFunc) (interface{}, error) {
	start := time.Now()
	defer func() {
		cb.metrics.duration.WithLabelValues(cb.name).Observe(time.Since(start).Seconds())
	}()

	generation, err := cb.beforeRequest()
	if err != nil {
		cb.metrics.rejections.WithLabelValues(cb.name, "circuit_open").Inc()
		return nil, err
	}

	defer func() {
		cb.afterRequest(generation, err)
	}()

	result, err := fn()
	cb.onResult(err)
	
	return result, err
}

// ExecuteWithContext executes function with context support
func (cb *CircuitBreaker) ExecuteWithContext(ctx context.Context, fn func(context.Context) (interface{}, error)) (interface{}, error) {
	return cb.Execute(func() (interface{}, error) {
		return fn(ctx)
	})
}

// beforeRequest checks if request should be allowed
func (cb *CircuitBreaker) beforeRequest() (uint64, error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)

	if state == StateOpen {
		return generation, ErrCircuitBreakerOpen
	} else if state == StateHalfOpen && cb.successCount >= int(cb.config.MaxRequests) {
		return generation, ErrTooManyRequests
	}

	cb.metrics.requests.WithLabelValues(cb.name, state.String()).Inc()
	return generation, nil
}

// afterRequest handles request completion
func (cb *CircuitBreaker) afterRequest(before uint64, err error) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	now := time.Now()
	state, generation := cb.currentState(now)
	
	if generation != before {
		return
	}

	if err != nil {
		cb.onFailure(state, now)
	} else {
		cb.onSuccess(state, now)
	}
}

// onResult records the result of the function execution
func (cb *CircuitBreaker) onResult(err error) {
	if err != nil {
		cb.metrics.failures.WithLabelValues(cb.name).Inc()
	} else {
		cb.metrics.successes.WithLabelValues(cb.name).Inc()
	}
}

// onSuccess handles successful request
func (cb *CircuitBreaker) onSuccess(state State, now time.Time) {
	switch state {
	case StateClosed:
		cb.failureCount = 0
	case StateHalfOpen:
		cb.successCount++
		if cb.successCount >= int(cb.config.MaxRequests) {
			cb.setState(StateClosed, now)
		}
	}
}

// onFailure handles failed request
func (cb *CircuitBreaker) onFailure(state State, now time.Time) {
	switch state {
	case StateClosed:
		cb.failureCount++
		if cb.config.ReadyToTrip(cb.getCounts()) {
			cb.setState(StateOpen, now)
		}
	case StateHalfOpen:
		cb.setState(StateOpen, now)
	}
}

// currentState returns current state and generation
func (cb *CircuitBreaker) currentState(now time.Time) (State, uint64) {
	switch cb.state {
	case StateClosed:
		if !cb.lastFailTime.IsZero() && cb.lastFailTime.Add(cb.config.Interval).Before(now) {
			cb.toNewGeneration(now)
		}
	case StateOpen:
		if cb.nextAttempt.Before(now) {
			cb.setState(StateHalfOpen, now)
		}
	}
	
	return cb.state, cb.getGeneration(now)
}

// setState changes circuit breaker state
func (cb *CircuitBreaker) setState(state State, now time.Time) {
	if cb.state == state {
		return
	}

	prev := cb.state
	cb.state = state

	cb.toNewGeneration(now)

	if state == StateOpen {
		cb.nextAttempt = now.Add(cb.config.Timeout)
		cb.metrics.trips.WithLabelValues(cb.name, prev.String()).Inc()
	}

	// Update metrics
	cb.metrics.state.WithLabelValues(cb.name).Set(float64(state))

	// Notify state change
	if cb.onStateChange != nil {
		cb.onStateChange(cb.name, prev, state)
	}
}

// toNewGeneration resets counters for new generation
func (cb *CircuitBreaker) toNewGeneration(now time.Time) {
	cb.failureCount = 0
	cb.successCount = 0
	cb.lastFailTime = now
}

// getGeneration gets current generation (for concurrency control)
func (cb *CircuitBreaker) getGeneration(now time.Time) uint64 {
	return uint64(now.UnixNano())
}

// getCounts returns current statistics
func (cb *CircuitBreaker) getCounts() Counts {
	return Counts{
		Requests:        uint32(cb.failureCount + cb.successCount),
		TotalFailures:   uint32(cb.failureCount),
		TotalSuccesses:  uint32(cb.successCount),
		ConsecutiveFailures:  uint32(cb.failureCount),
		ConsecutiveSuccesses: uint32(cb.successCount),
	}
}

// State returns current circuit breaker state
func (cb *CircuitBreaker) State() State {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	
	state, _ := cb.currentState(time.Now())
	return state
}

// Counts returns current circuit breaker statistics
func (cb *CircuitBreaker) Counts() Counts {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()
	
	return cb.getCounts()
}

// Manager implementation
func NewManager() *Manager {
	return &Manager{
		breakers: make(map[string]*CircuitBreaker),
		metrics:  newCircuitBreakerMetrics(),
	}
}

// GetCircuitBreaker gets or creates a circuit breaker
func (m *Manager) GetCircuitBreaker(name string, config Config) *CircuitBreaker {
	m.mutex.RLock()
	cb, exists := m.breakers[name]
	m.mutex.RUnlock()

	if exists {
		return cb
	}

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Double-check after acquiring write lock
	if cb, exists := m.breakers[name]; exists {
		return cb
	}

	config.Name = name
	cb = NewCircuitBreaker(config)
	m.breakers[name] = cb

	return cb
}

// ListCircuitBreakers returns all circuit breakers
func (m *Manager) ListCircuitBreakers() map[string]*CircuitBreaker {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]*CircuitBreaker)
	for name, cb := range m.breakers {
		result[name] = cb
	}

	return result
}

// GetStats returns statistics for all circuit breakers
func (m *Manager) GetStats() map[string]CircuitBreakerStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]CircuitBreakerStats)
	for name, cb := range m.breakers {
		counts := cb.Counts()
		stats[name] = CircuitBreakerStats{
			Name:             name,
			State:            cb.State().String(),
			Failures:         counts.TotalFailures,
			Successes:        counts.TotalSuccesses,
			Requests:         counts.Requests,
			ConsecutiveFailures: counts.ConsecutiveFailures,
		}
	}

	return stats
}

// CircuitBreakerStats holds statistics for monitoring
type CircuitBreakerStats struct {
	Name                string `json:"name"`
	State               string `json:"state"`
	Failures            uint32 `json:"failures"`
	Successes           uint32 `json:"successes"`
	Requests            uint32 `json:"requests"`
	ConsecutiveFailures uint32 `json:"consecutive_failures"`
}

// Predefined error types
var (
	ErrCircuitBreakerOpen = errors.New("circuit breaker is open")
	ErrTooManyRequests    = errors.New("too many requests")
	ErrTimeout            = errors.New("request timeout")
)

// Default functions
func DefaultReadyToTrip(counts Counts) bool {
	return counts.ConsecutiveFailures > 5
}

func DefaultIsSuccessful(err error) bool {
	return err == nil
}

// Helper functions for common patterns
func NewCircuitBreakerForService(serviceName string, maxFailures int, timeout time.Duration) *CircuitBreaker {
	return NewCircuitBreaker(Config{
		Name:        serviceName,
		MaxRequests: 10,
		Interval:    60 * time.Second,
		Timeout:     timeout,
		ReadyToTrip: func(counts Counts) bool {
			return counts.ConsecutiveFailures > uint32(maxFailures)
		},
	})
}

// Wrapper for HTTP clients
func WrapHTTPClient(cb *CircuitBreaker, client func() error) error {
	_, err := cb.Execute(func() (interface{}, error) {
		return nil, client()
	})
	return err
}

// Wrapper for database operations
func WrapDBOperation(cb *CircuitBreaker, operation func() (interface{}, error)) (interface{}, error) {
	return cb.Execute(operation)
}

// newCircuitBreakerMetrics initializes Prometheus metrics
func newCircuitBreakerMetrics() *CircuitBreakerMetrics {
	return &CircuitBreakerMetrics{
		state: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_circuit_breaker_state",
			Help: "Circuit breaker state (0=closed, 1=half-open, 2=open)",
		}, []string{"name"}),
		requests: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_circuit_breaker_requests_total",
			Help: "Total number of requests through circuit breaker",
		}, []string{"name", "state"}),
		failures: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_circuit_breaker_failures_total",
			Help: "Total number of failures",
		}, []string{"name"}),
		successes: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_circuit_breaker_successes_total",
			Help: "Total number of successes",
		}, []string{"name"}),
		trips: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_circuit_breaker_trips_total",
			Help: "Total number of circuit breaker trips",
		}, []string{"name", "from_state"}),
		duration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_circuit_breaker_request_duration_seconds",
			Help:    "Request duration through circuit breaker",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0},
		}, []string{"name"}),
		rejections: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_circuit_breaker_rejections_total",
			Help: "Total number of rejected requests",
		}, []string{"name", "reason"}),
	}
}

// Configuration helpers
type ServiceConfig struct {
	MaxFailures int
	Timeout     time.Duration
	Interval    time.Duration
	MaxRequests uint32
}

func DefaultServiceConfig() ServiceConfig {
	return ServiceConfig{
		MaxFailures: 5,
		Timeout:     60 * time.Second,
		Interval:    60 * time.Second,
		MaxRequests: 10,
	}
}

func (sc ServiceConfig) ToCircuitBreakerConfig(name string) Config {
	return Config{
		Name:        name,
		MaxRequests: sc.MaxRequests,
		Interval:    sc.Interval,
		Timeout:     sc.Timeout,
		ReadyToTrip: func(counts Counts) bool {
			return counts.ConsecutiveFailures > uint32(sc.MaxFailures)
		},
	}
} 