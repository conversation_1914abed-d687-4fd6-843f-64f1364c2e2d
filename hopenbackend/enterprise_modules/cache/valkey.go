// Package cache provides enterprise-grade caching with Valkey (Redis-compatible)
// implementing multi-level caching, session management, and rate limiting.
package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// ValkeyManager manages Valkey connections and operations
type ValkeyManager struct {
	client      *redis.ClusterClient
	localCache  *LocalCache
	metrics     *CacheMetrics
	config      *Config
}

// Config holds Valkey configuration
type Config struct {
	Addrs              []string
	Password           string
	MaxRetries         int
	MinRetryBackoff    time.Duration
	MaxRetryBackoff    time.Duration
	DialTimeout        time.Duration
	ReadTimeout        time.Duration
	WriteTimeout       time.Duration
	PoolSize           int
	MinIdleConns       int
	MaxConnAge         time.Duration
	PoolTimeout        time.Duration
	IdleTimeout        time.Duration
	IdleCheckFrequency time.Duration
	LocalCacheSize     int
	LocalCacheTTL      time.Duration
}

// CacheMetrics holds Prometheus metrics for cache operations
type CacheMetrics struct {
	hits           *prometheus.CounterVec
	misses         *prometheus.CounterVec
	sets           *prometheus.CounterVec
	errors         *prometheus.CounterVec
	latency        *prometheus.HistogramVec
	localCacheHits prometheus.Counter
	evictions      prometheus.Counter
	connections    prometheus.Gauge
}

// CacheItem represents a cached item with metadata
type CacheItem struct {
	Data      interface{} `json:"data"`
	CreatedAt time.Time   `json:"created_at"`
	TTL       time.Duration `json:"ttl"`
	Version   string      `json:"version"`
}

// Session represents a user session
type Session struct {
	UserID    string                 `json:"user_id"`
	Data      map[string]interface{} `json:"data"`
	CreatedAt time.Time              `json:"created_at"`
	ExpiresAt time.Time              `json:"expires_at"`
	IPAddress string                 `json:"ip_address"`
	UserAgent string                 `json:"user_agent"`
}

// LocalCache implements an in-memory LRU cache
type LocalCache struct {
	items    map[string]*localCacheItem
	order    []string
	capacity int
	mutex    sync.RWMutex
}

type localCacheItem struct {
	data      []byte
	expiresAt time.Time
}

// Helper types
var ErrCacheMiss = fmt.Errorf("cache miss")

// NewValkeyManager creates a new Valkey manager
func NewValkeyManager(config *Config) (*ValkeyManager, error) {
	if config == nil {
		config = DefaultConfig()
	}

	client := redis.NewClusterClient(&redis.ClusterOptions{
		Addrs:              config.Addrs,
		Password:           config.Password,
		MaxRetries:         config.MaxRetries,
		MinRetryBackoff:    config.MinRetryBackoff,
		MaxRetryBackoff:    config.MaxRetryBackoff,
		DialTimeout:        config.DialTimeout,
		ReadTimeout:        config.ReadTimeout,
		WriteTimeout:       config.WriteTimeout,
		PoolSize:           config.PoolSize,
		MinIdleConns:       config.MinIdleConns,
		MaxConnAge:         config.MaxConnAge,
		PoolTimeout:        config.PoolTimeout,
		IdleTimeout:        config.IdleTimeout,
		IdleCheckFrequency: config.IdleCheckFrequency,
	})

	vm := &ValkeyManager{
		client:     client,
		localCache: NewLocalCache(config.LocalCacheSize),
		metrics:    newCacheMetrics(),
		config:     config,
	}

	return vm, nil
}

// Get retrieves a value from cache
func (vm *ValkeyManager) Get(ctx context.Context, key string, dest interface{}) error {
	// L1 Cache (local memory) lookup
	if item, found := vm.localCache.Get(key); found {
		vm.metrics.hits.WithLabelValues("local", "get").Inc()
		return json.Unmarshal(item, dest)
	}

	// L2 Cache (Valkey/Redis) lookup
	data, err := vm.client.Get(ctx, key).Result()
	if err == redis.Nil {
		vm.metrics.misses.WithLabelValues("valkey", "get").Inc()
		return ErrCacheMiss
	} else if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "get").Inc()
		return fmt.Errorf("valkey get error: %w", err)
	}

	vm.metrics.hits.WithLabelValues("valkey", "get").Inc()
	vm.localCache.Set(key, []byte(data), vm.config.LocalCacheTTL)

	return json.Unmarshal([]byte(data), dest)
}

// Set stores a value in cache
func (vm *ValkeyManager) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return fmt.Errorf("failed to marshal value: %w", err)
	}

	err = vm.client.Set(ctx, key, data, ttl).Err()
	if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "set").Inc()
		return fmt.Errorf("valkey set error: %w", err)
	}

	localTTL := vm.config.LocalCacheTTL
	if ttl < localTTL {
		localTTL = ttl
	}
	vm.localCache.Set(key, data, localTTL)

	return nil
}

// Delete removes a value from cache
func (vm *ValkeyManager) Delete(ctx context.Context, key string) error {
	// Delete from Valkey
	err := vm.client.Del(ctx, key).Err()
	if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "delete").Inc()
		return fmt.Errorf("valkey delete error: %w", err)
	}

	// Delete from local cache
	vm.localCache.Delete(key)

	return nil
}

// GetUserProfile retrieves user profile with caching
func (vm *ValkeyManager) GetUserProfile(ctx context.Context, userID string) (*UserProfile, error) {
	key := fmt.Sprintf("user:profile:%s", userID)
	var profile UserProfile

	err := vm.Get(ctx, key, &profile)
	if err == ErrCacheMiss {
		// Cache miss - would typically fetch from database here
		// For now, return not found
		return nil, fmt.Errorf("user profile not found: %s", userID)
	}

	return &profile, err
}

// SetUserProfile caches user profile data
func (vm *ValkeyManager) SetUserProfile(ctx context.Context, userID string, profile *UserProfile) error {
	key := fmt.Sprintf("user:profile:%s", userID)
	return vm.Set(ctx, key, profile, time.Hour)
}

// InvalidateUserCache removes all cached data for a user
func (vm *ValkeyManager) InvalidateUserCache(ctx context.Context, userID string) error {
	patterns := []string{
		fmt.Sprintf("user:profile:%s", userID),
		fmt.Sprintf("user:bubbles:%s", userID),
		fmt.Sprintf("user:friends:%s", userID),
		fmt.Sprintf("user:sessions:%s*", userID),
	}

	for _, pattern := range patterns {
		keys, err := vm.client.Keys(ctx, pattern).Result()
		if err != nil {
			continue
		}

		if len(keys) > 0 {
			vm.client.Del(ctx, keys...)
		}

		// Also invalidate from local cache
		vm.localCache.Delete(pattern)
	}

	return nil
}

// StoreSession stores a user session
func (vm *ValkeyManager) StoreSession(ctx context.Context, sessionID string, session *Session) error {
	key := fmt.Sprintf("session:%s", sessionID)
	ttl := time.Until(session.ExpiresAt)
	return vm.Set(ctx, key, session, ttl)
}

// GetSession retrieves a user session
func (vm *ValkeyManager) GetSession(ctx context.Context, sessionID string) (*Session, error) {
	key := fmt.Sprintf("session:%s", sessionID)
	var session Session
	err := vm.Get(ctx, key, &session)
	if err != nil {
		return nil, err
	}

	// Check if session is expired
	if time.Now().After(session.ExpiresAt) {
		vm.Delete(ctx, key)
		return nil, fmt.Errorf("session expired")
	}

	return &session, nil
}

// DeleteSession removes a user session
func (vm *ValkeyManager) DeleteSession(ctx context.Context, sessionID string) error {
	key := fmt.Sprintf("session:%s", sessionID)
	return vm.Delete(ctx, key)
}

// CheckRateLimit implements sliding window rate limiting
func (vm *ValkeyManager) CheckRateLimit(ctx context.Context, key string, limit int, window time.Duration) (bool, error) {
	now := time.Now().Unix()
	windowStart := now - int64(window.Seconds())

	pipe := vm.client.Pipeline()
	pipe.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", windowStart))
	countCmd := pipe.ZCard(ctx, key)
	pipe.ZAdd(ctx, key, &redis.Z{Score: float64(now), Member: now})
	pipe.Expire(ctx, key, window)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return false, err
	}

	count := countCmd.Val()
	return count <= int64(limit), nil
}

// GetBubbleMembers retrieves cached bubble members
func (vm *ValkeyManager) GetBubbleMembers(ctx context.Context, bubbleID string) ([]string, error) {
	key := fmt.Sprintf("bubble:members:%s", bubbleID)
	members, err := vm.client.SMembers(ctx, key).Result()
	if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "set_members").Inc()
		return nil, err
	}

	vm.metrics.hits.WithLabelValues("valkey", "set_members").Inc()
	return members, nil
}

// AddBubbleMember adds a member to bubble cache
func (vm *ValkeyManager) AddBubbleMember(ctx context.Context, bubbleID, userID string) error {
	key := fmt.Sprintf("bubble:members:%s", bubbleID)
	err := vm.client.SAdd(ctx, key, userID).Err()
	if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "set_add").Inc()
		return err
	}

	// Set expiration
	vm.client.Expire(ctx, key, 24*time.Hour)
	return nil
}

// RemoveBubbleMember removes a member from bubble cache
func (vm *ValkeyManager) RemoveBubbleMember(ctx context.Context, bubbleID, userID string) error {
	key := fmt.Sprintf("bubble:members:%s", bubbleID)
	return vm.client.SRem(ctx, key, userID).Err()
}

// GetRecentMessages retrieves recent chat messages from cache
func (vm *ValkeyManager) GetRecentMessages(ctx context.Context, bubbleID string, limit int) ([]ChatMessage, error) {
	key := fmt.Sprintf("bubble:messages:%s", bubbleID)
	
	// Get recent messages using ZREVRANGE
	results, err := vm.client.ZRevRange(ctx, key, 0, int64(limit-1)).Result()
	if err != nil {
		vm.metrics.errors.WithLabelValues("valkey", "sorted_set").Inc()
		return nil, err
	}

	messages := make([]ChatMessage, 0, len(results))
	for _, result := range results {
		var msg ChatMessage
		if err := json.Unmarshal([]byte(result), &msg); err == nil {
			messages = append(messages, msg)
		}
	}

	vm.metrics.hits.WithLabelValues("valkey", "sorted_set").Inc()
	return messages, nil
}

// CacheMessage stores a chat message in cache
func (vm *ValkeyManager) CacheMessage(ctx context.Context, bubbleID string, message *ChatMessage) error {
	key := fmt.Sprintf("bubble:messages:%s", bubbleID)
	
	data, err := json.Marshal(message)
	if err != nil {
		return err
	}

	// Use timestamp as score for chronological ordering
	score := float64(message.CreatedAt.Unix())
	
	pipe := vm.client.Pipeline()
	
	// Add message to sorted set
	pipe.ZAdd(ctx, key, &redis.Z{Score: score, Member: string(data)})
	
	// Keep only recent messages (last 1000)
	pipe.ZRemRangeByRank(ctx, key, 0, -1001)
	
	// Set expiration
	pipe.Expire(ctx, key, 24*time.Hour)
	
	_, err = pipe.Exec(ctx)
	return err
}

// HealthCheck verifies cache connectivity
func (vm *ValkeyManager) HealthCheck(ctx context.Context) error {
	_, err := vm.client.Ping(ctx).Result()
	if err != nil {
		return fmt.Errorf("valkey health check failed: %w", err)
	}
	return nil
}

// GetStats returns cache statistics
func (vm *ValkeyManager) GetStats(ctx context.Context) (*CacheStats, error) {
	info, err := vm.client.Info(ctx, "stats").Result()
	if err != nil {
		return nil, err
	}

	return &CacheStats{
		Info:           info,
		LocalCacheSize: vm.localCache.Size(),
	}, nil
}

// NewLocalCache creates a new local cache instance
func NewLocalCache(capacity int) *LocalCache {
	return &LocalCache{
		items:    make(map[string]*localCacheItem),
		order:    make([]string, 0, capacity),
		capacity: capacity,
	}
}

func (lc *LocalCache) Get(key string) ([]byte, bool) {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()

	item, exists := lc.items[key]
	if !exists {
		return nil, false
	}

	if time.Now().After(item.expiresAt) {
		// Item expired, clean up
		delete(lc.items, key)
		return nil, false
	}

	return item.data, true
}

func (lc *LocalCache) Set(key string, data []byte, ttl time.Duration) {
	lc.mutex.Lock()
	defer lc.mutex.Unlock()

	// Remove if exists
	if _, exists := lc.items[key]; !exists {
		// Add to order if new item
		lc.order = append(lc.order, key)
	}

	// Evict oldest items if at capacity
	for len(lc.order) >= lc.capacity {
		oldest := lc.order[0]
		lc.order = lc.order[1:]
		delete(lc.items, oldest)
	}

	lc.items[key] = &localCacheItem{
		data:      data,
		expiresAt: time.Now().Add(ttl),
	}
}

func (lc *LocalCache) Delete(key string) {
	lc.mutex.Lock()
	defer lc.mutex.Unlock()

	delete(lc.items, key)
	
	// Remove from order
	for i, k := range lc.order {
		if k == key {
			lc.order = append(lc.order[:i], lc.order[i+1:]...)
			break
		}
	}
}

func (lc *LocalCache) Size() int {
	lc.mutex.RLock()
	defer lc.mutex.RUnlock()
	return len(lc.items)
}

// Helper types and constants
type UserProfile struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	// Add other profile fields
}

type ChatMessage struct {
	ID        string    `json:"id"`
	BubbleID  string    `json:"bubble_id"`
	SenderID  string    `json:"sender_id"`
	Content   string    `json:"content"`
	CreatedAt time.Time `json:"created_at"`
}

type CacheStats struct {
	Info           string `json:"info"`
	LocalCacheSize int    `json:"local_cache_size"`
}

// newCacheMetrics initializes Prometheus metrics
func newCacheMetrics() *CacheMetrics {
	return &CacheMetrics{
		hits: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cache_hits_total",
			Help: "Total number of cache hits by cache type",
		}, []string{"cache_type", "operation"}),
		misses: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cache_misses_total",
			Help: "Total number of cache misses by cache type",
		}, []string{"cache_type", "operation"}),
		sets: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cache_sets_total",
			Help: "Total number of cache sets by cache type",
		}, []string{"cache_type", "operation"}),
		errors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cache_errors_total",
			Help: "Total number of cache errors by type",
		}, []string{"cache_type", "error_type"}),
		latency: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cache_operation_duration_seconds",
			Help:    "Cache operation duration in seconds",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"cache_type", "operation"}),
		localCacheHits: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_cache_local_hits_total",
			Help: "Total number of local cache hits",
		}),
		evictions: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_cache_evictions_total",
			Help: "Total number of cache evictions",
		}),
		connections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_cache_connections_active",
			Help: "Number of active cache connections",
		}),
	}
}

// DefaultConfig returns default Valkey configuration
func DefaultConfig() *Config {
	return &Config{
		Addrs:              []string{"localhost:6379"},
		MaxRetries:         3,
		MinRetryBackoff:    8 * time.Millisecond,
		MaxRetryBackoff:    512 * time.Millisecond,
		DialTimeout:        5 * time.Second,
		ReadTimeout:        3 * time.Second,
		WriteTimeout:       3 * time.Second,
		PoolSize:           10,
		MinIdleConns:       5,
		MaxConnAge:         30 * time.Minute,
		PoolTimeout:        30 * time.Second,
		IdleTimeout:        5 * time.Minute,
		IdleCheckFrequency: time.Minute,
		LocalCacheSize:     1000,
		LocalCacheTTL:      5 * time.Minute,
	}
} 