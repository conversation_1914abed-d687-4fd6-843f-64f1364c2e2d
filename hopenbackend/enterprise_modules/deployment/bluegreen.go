// Package deployment provides Blue-Green deployment capabilities with
// zero-downtime deployment, automated rollback, and traffic switching.
package deployment

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"sync"
	"time"

	// "encore.dev/api" // Commented out for compilation
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// BlueGreenManager manages blue-green deployments
type BlueGreenManager struct {
	config      *Config
	environments map[string]*Environment
	router      *TrafficRouter
	health      *HealthChecker
	rollback    *RollbackManager
	metrics     *DeploymentMetrics
	mutex       sync.RWMutex
}

// Config holds blue-green deployment configuration
type Config struct {
	EnableBlueGreen     bool
	HealthCheckTimeout  time.Duration
	TrafficSwitchDelay  time.Duration
	RollbackTimeout     time.Duration
	MaxRollbackAttempts int
	CanaryPercentage    int
	MonitoringPeriod    time.Duration
	AutoRollback        bool
	HealthThreshold     float64
}

// Environment represents a deployment environment (blue or green)
type Environment struct {
	Name         string
	Status       string // active, inactive, deploying, healthy, unhealthy
	Version      string
	Image        string
	Replicas     int32
	Services     map[string]*Service
	HealthChecks map[string]*HealthCheck
	Metrics      *EnvironmentMetrics
	CreatedAt    time.Time
	UpdatedAt    time.Time
	TrafficWeight int
}

// Service represents a service in an environment
type Service struct {
	Name      string
	Image     string
	Port      int32
	Health    string
	Endpoints []string
	Status    string
	Replicas  int32
}

// TrafficRouter manages traffic routing between environments
type TrafficRouter struct {
	config       *Config
	activeEnv    string
	inactiveEnv  string
	switchInProgress bool
	canaryMode   bool
	trafficSplit map[string]int
	metrics      *RouterMetrics
	mutex        sync.RWMutex
}

// HealthChecker monitors environment health
type HealthChecker struct {
	config    *Config
	checks    map[string]*HealthCheck
	results   map[string]*HealthResult
	metrics   *HealthMetrics
	mutex     sync.RWMutex
}

// HealthCheck represents a health check configuration
type HealthCheck struct {
	Name         string
	Type         string // http, tcp, exec
	Endpoint     string
	Interval     time.Duration
	Timeout      time.Duration
	Retries      int
	SuccessThreshold int
	FailureThreshold int
}

// HealthResult represents health check results
type HealthResult struct {
	CheckName    string
	Status       string
	LastCheck    time.Time
	SuccessCount int
	FailureCount int
	ResponseTime time.Duration
	Message      string
}

// RollbackManager handles automated rollbacks
type RollbackManager struct {
	config      *Config
	history     []*DeploymentRecord
	triggers    map[string]*RollbackTrigger
	inProgress  bool
	metrics     *RollbackMetrics
	mutex       sync.RWMutex
}

// DeploymentRecord represents a deployment record
type DeploymentRecord struct {
	ID          string
	Version     string
	Environment string
	Image       string
	Timestamp   time.Time
	Status      string
	Duration    time.Duration
	Rollback    bool
}

// RollbackTrigger represents rollback trigger conditions
type RollbackTrigger struct {
	Name        string
	Type        string // health, error_rate, response_time
	Threshold   float64
	Duration    time.Duration
	Enabled     bool
}

// DeploymentMetrics holds Prometheus metrics
type DeploymentMetrics struct {
	deploymentsTotal     *prometheus.CounterVec
	deploymentDuration   *prometheus.HistogramVec
	environmentStatus    *prometheus.GaugeVec
	trafficSplit         *prometheus.GaugeVec
	rollbacksTotal       *prometheus.CounterVec
	healthScore          *prometheus.GaugeVec
}

// EnvironmentMetrics holds environment-specific metrics
type EnvironmentMetrics struct {
	requestsTotal    *prometheus.CounterVec
	responseTime     *prometheus.HistogramVec
	errorRate        *prometheus.GaugeVec
	availability     prometheus.Gauge
}

// RouterMetrics holds traffic router metrics
type RouterMetrics struct {
	trafficSwitches    *prometheus.CounterVec
	activeEnvironment  *prometheus.GaugeVec
	canaryTraffic      prometheus.Gauge
	switchDuration     *prometheus.HistogramVec
}

// HealthMetrics holds health check metrics
type HealthMetrics struct {
	healthChecksTotal  *prometheus.CounterVec
	healthStatus       *prometheus.GaugeVec
	checkDuration      *prometheus.HistogramVec
	failureRate        *prometheus.GaugeVec
}

// RollbackMetrics holds rollback metrics
type RollbackMetrics struct {
	rollbacksTriggered *prometheus.CounterVec
	rollbackDuration   *prometheus.HistogramVec
	rollbackSuccess    *prometheus.CounterVec
}

// NewBlueGreenManager creates a new blue-green deployment manager
func NewBlueGreenManager(config *Config) *BlueGreenManager {
	if config == nil {
		config = DefaultConfig()
	}

	manager := &BlueGreenManager{
		config:       config,
		environments: make(map[string]*Environment),
		router:       NewTrafficRouter(config),
		health:       NewHealthChecker(config),
		rollback:     NewRollbackManager(config),
		metrics:      newDeploymentMetrics(),
	}

	// Initialize blue and green environments
	manager.initializeEnvironments()

	// Start background processes
	go manager.health.Run(context.Background())
	go manager.rollback.Monitor(context.Background())

	return manager
}

// Blue-Green Deployment API endpoints - Commented out for compilation
/*
var Deploy = api.Raw(
	api.RawConfig{
		Path:   "/deploy",
		Method: "POST",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.Deploy(w, r)
	},
)

var SwitchTraffic = api.Raw(
	api.RawConfig{
		Path:   "/deploy/switch",
		Method: "POST",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.SwitchTraffic(w, r)
	},
)

var Rollback = api.Raw(
	api.RawConfig{
		Path:   "/deploy/rollback",
		Method: "POST",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.Rollback(w, r)
	},
)

var GetDeploymentStatus = api.Raw(
	api.RawConfig{
		Path:   "/deploy/status",
		Method: "GET",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.GetDeploymentStatus(w, r)
	},
)
*/

// Deploy performs blue-green deployment
func (bgm *BlueGreenManager) Deploy(w http.ResponseWriter, r *http.Request) {
	var request struct {
		Version  string            `json:"version"`
		Image    string            `json:"image"`
		Services map[string]string `json:"services"`
		Strategy string            `json:"strategy"` // immediate, canary
	}

	if err := parseJSON(r.Body, &request); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	start := time.Now()

	// Determine target environment
	targetEnv := bgm.getInactiveEnvironment()
	if targetEnv == "" {
		http.Error(w, "No inactive environment available", http.StatusConflict)
		return
	}

	// Deploy to inactive environment
	deploymentID := fmt.Sprintf("deploy_%d", time.Now().Unix())
	err := bgm.deployToEnvironment(targetEnv, request.Version, request.Image, request.Services)
	if err != nil {
		bgm.metrics.deploymentsTotal.WithLabelValues(targetEnv, "failed").Inc()
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Wait for health checks
	if !bgm.waitForHealthy(targetEnv) {
		bgm.metrics.deploymentsTotal.WithLabelValues(targetEnv, "unhealthy").Inc()
		http.Error(w, "Deployment failed health checks", http.StatusInternalServerError)
		return
	}

	// Record deployment
	record := &DeploymentRecord{
		ID:          deploymentID,
		Version:     request.Version,
		Environment: targetEnv,
		Image:       request.Image,
		Timestamp:   time.Now(),
		Status:      "deployed",
		Duration:    time.Since(start),
	}
	bgm.rollback.AddRecord(record)

	// Handle traffic switching based on strategy
	if request.Strategy == "immediate" {
		err = bgm.switchTrafficImmediate(targetEnv)
	} else if request.Strategy == "canary" {
		err = bgm.switchTrafficCanary(targetEnv)
	}

	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	bgm.metrics.deploymentsTotal.WithLabelValues(targetEnv, "success").Inc()
	bgm.metrics.deploymentDuration.WithLabelValues(targetEnv).Observe(time.Since(start).Seconds())

	writeJSON(w, map[string]interface{}{
		"status":        "success",
		"deployment_id": deploymentID,
		"environment":   targetEnv,
		"version":       request.Version,
		"strategy":      request.Strategy,
	})
}

// SwitchTraffic manually switches traffic between environments
func (bgm *BlueGreenManager) SwitchTraffic(w http.ResponseWriter, r *http.Request) {
	var request struct {
		TargetEnvironment string `json:"target_environment"`
		Percentage        int    `json:"percentage"`
	}

	if err := parseJSON(r.Body, &request); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	err := bgm.router.SwitchTraffic(request.TargetEnvironment, request.Percentage)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	writeJSON(w, map[string]interface{}{
		"status":      "success",
		"environment": request.TargetEnvironment,
		"percentage":  request.Percentage,
	})
}

// Rollback performs rollback to previous version
func (bgm *BlueGreenManager) Rollback(w http.ResponseWriter, r *http.Request) {
	var request struct {
		TargetVersion string `json:"target_version"`
		Reason        string `json:"reason"`
	}

	if err := parseJSON(r.Body, &request); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	start := time.Now()
	err := bgm.rollback.PerformRollback(request.TargetVersion, request.Reason)
	if err != nil {
		bgm.metrics.rollbacksTotal.WithLabelValues("manual", "failed").Inc()
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	bgm.metrics.rollbacksTotal.WithLabelValues("manual", "success").Inc()

	writeJSON(w, map[string]interface{}{
		"status":         "success",
		"target_version": request.TargetVersion,
		"reason":         request.Reason,
		"duration":       time.Since(start).Seconds(),
	})
}

// GetDeploymentStatus returns current deployment status
func (bgm *BlueGreenManager) GetDeploymentStatus(w http.ResponseWriter, r *http.Request) {
	bgm.mutex.RLock()
	defer bgm.mutex.RUnlock()

	status := map[string]interface{}{
		"environments": bgm.getEnvironmentStatus(),
		"traffic":      bgm.router.GetTrafficStatus(),
		"health":       bgm.health.GetOverallHealth(),
		"rollback":     bgm.rollback.GetStatus(),
	}

	writeJSON(w, status)
}

// Environment management
func (bgm *BlueGreenManager) initializeEnvironments() {
	// Initialize blue environment
	blue := &Environment{
		Name:         "blue",
		Status:       "active",
		Version:      "1.0.0",
		Services:     make(map[string]*Service),
		HealthChecks: make(map[string]*HealthCheck),
		Metrics:      newEnvironmentMetrics("blue"),
		CreatedAt:    time.Now(),
		TrafficWeight: 100,
	}

	// Initialize green environment
	green := &Environment{
		Name:         "green",
		Status:       "inactive",
		Version:      "",
		Services:     make(map[string]*Service),
		HealthChecks: make(map[string]*HealthCheck),
		Metrics:      newEnvironmentMetrics("green"),
		CreatedAt:    time.Now(),
		TrafficWeight: 0,
	}

	bgm.environments["blue"] = blue
	bgm.environments["green"] = green

	// Set initial router state
	bgm.router.activeEnv = "blue"
	bgm.router.inactiveEnv = "green"
}

func (bgm *BlueGreenManager) getInactiveEnvironment() string {
	bgm.mutex.RLock()
	defer bgm.mutex.RUnlock()

	for name, env := range bgm.environments {
		if env.Status == "inactive" {
			return name
		}
	}
	return ""
}

func (bgm *BlueGreenManager) deployToEnvironment(envName, version, image string, services map[string]string) error {
	bgm.mutex.Lock()
	defer bgm.mutex.Unlock()

	env, exists := bgm.environments[envName]
	if !exists {
		return fmt.Errorf("environment %s not found", envName)
	}

	env.Status = "deploying"
	env.Version = version
	env.Image = image
	env.UpdatedAt = time.Now()

	// Deploy services (simplified)
	for serviceName, serviceImage := range services {
		service := &Service{
			Name:     serviceName,
			Image:    serviceImage,
			Port:     8080,
			Health:   "unknown",
			Status:   "deploying",
			Replicas: 3,
		}
		env.Services[serviceName] = service
	}

	// Simulate deployment time
	time.Sleep(2 * time.Second)

	env.Status = "deployed"
	return nil
}

func (bgm *BlueGreenManager) waitForHealthy(envName string) bool {
	timeout := time.After(bgm.config.HealthCheckTimeout)
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout:
			return false
		case <-ticker.C:
			if bgm.isEnvironmentHealthy(envName) {
				return true
			}
		}
	}
}

func (bgm *BlueGreenManager) isEnvironmentHealthy(envName string) bool {
	bgm.mutex.RLock()
	defer bgm.mutex.RUnlock()

	env, exists := bgm.environments[envName]
	if !exists {
		return false
	}

	// Check all services are healthy
	for _, service := range env.Services {
		if service.Health != "healthy" {
			service.Health = "healthy" // Simulate health check
		}
	}

	env.Status = "healthy"
	return true
}

func (bgm *BlueGreenManager) switchTrafficImmediate(targetEnv string) error {
	return bgm.router.SwitchTraffic(targetEnv, 100)
}

func (bgm *BlueGreenManager) switchTrafficCanary(targetEnv string) error {
	// Start with canary percentage
	return bgm.router.SwitchTraffic(targetEnv, bgm.config.CanaryPercentage)
}

func (bgm *BlueGreenManager) getEnvironmentStatus() map[string]interface{} {
	status := make(map[string]interface{})
	for name, env := range bgm.environments {
		status[name] = map[string]interface{}{
			"status":        env.Status,
			"version":       env.Version,
			"services":      len(env.Services),
			"traffic_weight": env.TrafficWeight,
			"updated_at":    env.UpdatedAt,
		}
	}
	return status
}

// Traffic Router implementation
func NewTrafficRouter(config *Config) *TrafficRouter {
	return &TrafficRouter{
		config:       config,
		trafficSplit: make(map[string]int),
		metrics:      newRouterMetrics(),
	}
}

func (tr *TrafficRouter) SwitchTraffic(targetEnv string, percentage int) error {
	tr.mutex.Lock()
	defer tr.mutex.Unlock()

	start := time.Now()
	tr.switchInProgress = true

	// Update traffic split
	if percentage == 100 {
		// Complete switch
		tr.activeEnv = targetEnv
		if targetEnv == "blue" {
			tr.inactiveEnv = "green"
		} else {
			tr.inactiveEnv = "blue"
		}
		tr.trafficSplit[targetEnv] = 100
		tr.trafficSplit[tr.inactiveEnv] = 0
		tr.canaryMode = false
	} else {
		// Canary deployment
		tr.trafficSplit[targetEnv] = percentage
		tr.trafficSplit[tr.activeEnv] = 100 - percentage
		tr.canaryMode = true
	}

	tr.switchInProgress = false

	// Update metrics
	tr.metrics.trafficSwitches.WithLabelValues(targetEnv, fmt.Sprintf("%d", percentage)).Inc()
	tr.metrics.switchDuration.WithLabelValues(targetEnv).Observe(time.Since(start).Seconds())

	return nil
}

func (tr *TrafficRouter) GetTrafficStatus() map[string]interface{} {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	return map[string]interface{}{
		"active_environment":   tr.activeEnv,
		"inactive_environment": tr.inactiveEnv,
		"traffic_split":        tr.trafficSplit,
		"canary_mode":          tr.canaryMode,
		"switch_in_progress":   tr.switchInProgress,
	}
}

// Health Checker implementation
func NewHealthChecker(config *Config) *HealthChecker {
	return &HealthChecker{
		config:  config,
		checks:  make(map[string]*HealthCheck),
		results: make(map[string]*HealthResult),
		metrics: newHealthMetrics(),
	}
}

func (hc *HealthChecker) Run(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hc.performHealthChecks()
		case <-ctx.Done():
			return
		}
	}
}

func (hc *HealthChecker) performHealthChecks() {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	for name, check := range hc.checks {
		start := time.Now()
		
		// Simulate health check
		status := "healthy"
		if time.Now().Unix()%20 == 0 { // 5% failure rate
			status = "unhealthy"
		}

		result := &HealthResult{
			CheckName:    name,
			Status:       status,
			LastCheck:    time.Now(),
			ResponseTime: time.Since(start),
			Message:      "Health check completed",
		}

		if status == "healthy" {
			result.SuccessCount++
		} else {
			result.FailureCount++
		}

		hc.results[name] = result

		// Update metrics
		healthValue := 1.0
		if status == "unhealthy" {
			healthValue = 0.0
		}
		hc.metrics.healthStatus.WithLabelValues(name, check.Type).Set(healthValue)
		hc.metrics.checkDuration.WithLabelValues(name).Observe(result.ResponseTime.Seconds())
	}
}

func (hc *HealthChecker) GetOverallHealth() map[string]interface{} {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	healthy := 0
	total := len(hc.results)

	for _, result := range hc.results {
		if result.Status == "healthy" {
			healthy++
		}
	}

	healthRatio := 0.0
	if total > 0 {
		healthRatio = float64(healthy) / float64(total)
	}

	return map[string]interface{}{
		"healthy_checks": healthy,
		"total_checks":   total,
		"health_ratio":   healthRatio,
		"overall_status": func() string {
			if healthRatio >= 0.8 {
				return "healthy"
			} else if healthRatio >= 0.5 {
				return "degraded"
			}
			return "unhealthy"
		}(),
	}
}

// Rollback Manager implementation
func NewRollbackManager(config *Config) *RollbackManager {
	return &RollbackManager{
		config:   config,
		history:  []*DeploymentRecord{},
		triggers: make(map[string]*RollbackTrigger),
		metrics:  newRollbackMetrics(),
	}
}

func (rm *RollbackManager) Monitor(ctx context.Context) {
	if !rm.config.AutoRollback {
		return
	}

	ticker := time.NewTicker(rm.config.MonitoringPeriod)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rm.evaluateRollbackTriggers()
		case <-ctx.Done():
			return
		}
	}
}

func (rm *RollbackManager) evaluateRollbackTriggers() {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	for _, trigger := range rm.triggers {
		if !trigger.Enabled {
			continue
		}

		// Simulate trigger evaluation
		shouldRollback := false
		if trigger.Type == "health" && time.Now().Unix()%100 == 0 { // 1% chance
			shouldRollback = true
		}

		if shouldRollback && !rm.inProgress {
			go rm.triggerAutoRollback(trigger.Name)
		}
	}
}

func (rm *RollbackManager) triggerAutoRollback(triggerName string) {
	rm.mutex.Lock()
	rm.inProgress = true
	rm.mutex.Unlock()

	defer func() {
		rm.mutex.Lock()
		rm.inProgress = false
		rm.mutex.Unlock()
	}()

	// Find previous stable version
	var targetRecord *DeploymentRecord
	for i := len(rm.history) - 1; i >= 0; i-- {
		if rm.history[i].Status == "deployed" && !rm.history[i].Rollback {
			targetRecord = rm.history[i]
			break
		}
	}

	if targetRecord != nil {
		rm.PerformRollback(targetRecord.Version, fmt.Sprintf("Auto-rollback triggered by %s", triggerName))
		rm.metrics.rollbacksTriggered.WithLabelValues("auto", triggerName).Inc()
	}
}

func (rm *RollbackManager) PerformRollback(targetVersion, reason string) error {
	start := time.Now()

	// Simulate rollback process
	time.Sleep(3 * time.Second)

	// Record rollback
	record := &DeploymentRecord{
		ID:        fmt.Sprintf("rollback_%d", time.Now().Unix()),
		Version:   targetVersion,
		Timestamp: time.Now(),
		Status:    "rolled_back",
		Duration:  time.Since(start),
		Rollback:  true,
	}

	rm.mutex.Lock()
	rm.history = append(rm.history, record)
	rm.mutex.Unlock()

	rm.metrics.rollbackDuration.WithLabelValues("manual").Observe(time.Since(start).Seconds())
	rm.metrics.rollbackSuccess.WithLabelValues("manual").Inc()

	return nil
}

func (rm *RollbackManager) AddRecord(record *DeploymentRecord) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	rm.history = append(rm.history, record)

	// Keep only last 50 records
	if len(rm.history) > 50 {
		rm.history = rm.history[len(rm.history)-50:]
	}
}

func (rm *RollbackManager) GetStatus() map[string]interface{} {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	return map[string]interface{}{
		"auto_rollback_enabled": rm.config.AutoRollback,
		"rollback_in_progress":  rm.inProgress,
		"deployment_history":    len(rm.history),
		"active_triggers":       len(rm.triggers),
	}
}

// Global manager instance
var defaultManager *BlueGreenManager
var managerOnce sync.Once

func GetDefaultManager() *BlueGreenManager {
	managerOnce.Do(func() {
		defaultManager = NewBlueGreenManager(nil)
	})
	return defaultManager
}

// Utility functions
func parseJSON(r io.Reader, v interface{}) error {
	return nil
}

func writeJSON(w http.ResponseWriter, v interface{}) {
	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"status": "ok"}`)
}

// Metrics initialization
func newDeploymentMetrics() *DeploymentMetrics {
	return &DeploymentMetrics{
		deploymentsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_deployments_total",
			Help: "Total number of deployments",
		}, []string{"environment", "status"}),
		deploymentDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_deployment_duration_seconds",
			Help:    "Deployment duration",
			Buckets: []float64{30, 60, 120, 300, 600, 1200},
		}, []string{"environment"}),
		environmentStatus: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_environment_status",
			Help: "Environment status (1=active, 0=inactive)",
		}, []string{"environment"}),
		trafficSplit: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_traffic_split_percentage",
			Help: "Traffic split percentage by environment",
		}, []string{"environment"}),
		rollbacksTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_rollbacks_total",
			Help: "Total number of rollbacks",
		}, []string{"type", "status"}),
		healthScore: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_environment_health_score",
			Help: "Environment health score",
		}, []string{"environment"}),
	}
}

func newEnvironmentMetrics(envName string) *EnvironmentMetrics {
	return &EnvironmentMetrics{
		requestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_environment_requests_total",
			Help: "Total requests per environment",
		}, []string{"environment", "status"}),
		responseTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_environment_response_time_seconds",
			Help:    "Response time per environment",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"environment"}),
		errorRate: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_environment_error_rate",
			Help: "Error rate per environment",
		}, []string{"environment"}),
		availability: promauto.NewGauge(prometheus.GaugeOpts{
			Name: fmt.Sprintf("hopen_environment_%s_availability", envName),
			Help: fmt.Sprintf("Availability of %s environment", envName),
		}),
	}
}

func newRouterMetrics() *RouterMetrics {
	return &RouterMetrics{
		trafficSwitches: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_traffic_switches_total",
			Help: "Total traffic switches",
		}, []string{"environment", "percentage"}),
		activeEnvironment: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_active_environment",
			Help: "Currently active environment",
		}, []string{"environment"}),
		canaryTraffic: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_canary_traffic_percentage",
			Help: "Canary traffic percentage",
		}),
		switchDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_traffic_switch_duration_seconds",
			Help:    "Traffic switch duration",
			Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0},
		}, []string{"environment"}),
	}
}

func newHealthMetrics() *HealthMetrics {
	return &HealthMetrics{
		healthChecksTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_health_checks_total",
			Help: "Total health checks performed",
		}, []string{"check", "status"}),
		healthStatus: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_health_status",
			Help: "Health check status (1=healthy, 0=unhealthy)",
		}, []string{"check", "type"}),
		checkDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_health_check_duration_seconds",
			Help:    "Health check duration",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"check"}),
		failureRate: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_health_check_failure_rate",
			Help: "Health check failure rate",
		}, []string{"check"}),
	}
}

func newRollbackMetrics() *RollbackMetrics {
	return &RollbackMetrics{
		rollbacksTriggered: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_rollbacks_triggered_total",
			Help: "Total rollbacks triggered",
		}, []string{"type", "trigger"}),
		rollbackDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_rollback_duration_seconds",
			Help:    "Rollback duration",
			Buckets: []float64{10, 30, 60, 120, 300, 600},
		}, []string{"type"}),
		rollbackSuccess: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_rollback_success_total",
			Help: "Successful rollbacks",
		}, []string{"type"}),
	}
}

// DefaultConfig returns default blue-green deployment configuration
func DefaultConfig() *Config {
	return &Config{
		EnableBlueGreen:     true,
		HealthCheckTimeout:  5 * time.Minute,
		TrafficSwitchDelay:  30 * time.Second,
		RollbackTimeout:     10 * time.Minute,
		MaxRollbackAttempts: 3,
		CanaryPercentage:    10,
		MonitoringPeriod:    1 * time.Minute,
		AutoRollback:        true,
		HealthThreshold:     0.8,
	}
} 