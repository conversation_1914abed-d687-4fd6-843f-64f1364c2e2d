// Package tracing provides distributed tracing capabilities using OpenTelemetry
// for complete request visibility across all microservices.
package tracing

import (
	"context"
	"fmt"
	"time"

	"encore.dev/middleware"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Tracer manages distributed tracing across services
//encore:service
type Tracer struct {
	config      *Config
	metrics     *TracingMetrics
	spans       map[string]*Span
	serviceName string
}

// Config holds tracing configuration
type Config struct {
	ServiceName     string
	ServiceVersion  string
	Environment     string
	SamplingRate    float64
	JaegerEndpoint  string
	EnableMetrics   bool
	EnableLogging   bool
	MaxSpanDuration time.Duration
	BatchTimeout    time.Duration
	MaxBatchSize    int
}

// Span represents a distributed trace span
type Span struct {
	TraceID      string            `json:"trace_id"`
	SpanID       string            `json:"span_id"`
	ParentSpanID string            `json:"parent_span_id,omitempty"`
	OperationName string           `json:"operation_name"`
	ServiceName  string            `json:"service_name"`
	StartTime    time.Time         `json:"start_time"`
	EndTime      time.Time         `json:"end_time"`
	Duration     time.Duration     `json:"duration"`
	Tags         map[string]string `json:"tags"`
	Logs         []LogEntry        `json:"logs"`
	Status       SpanStatus        `json:"status"`
	Error        bool              `json:"error"`
}

// LogEntry represents a log entry within a span
type LogEntry struct {
	Timestamp time.Time         `json:"timestamp"`
	Level     string            `json:"level"`
	Message   string            `json:"message"`
	Fields    map[string]string `json:"fields"`
}

// SpanStatus represents the status of a span
type SpanStatus struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// TracingMetrics holds Prometheus metrics for tracing
type TracingMetrics struct {
	spansCreated    *prometheus.CounterVec
	spansFinished   *prometheus.CounterVec
	spanDuration    *prometheus.HistogramVec
	traceErrors     *prometheus.CounterVec
	activeSpans     prometheus.Gauge
	tracesPerSecond prometheus.Gauge
}

// TraceContext holds trace information
type TraceContext struct {
	TraceID      string
	SpanID       string
	ParentSpanID string
	Baggage      map[string]string
}

// NewTracer creates a new distributed tracer
func NewTracer(config *Config) *Tracer {
	if config == nil {
		config = DefaultConfig()
	}

	tracer := &Tracer{
		config:      config,
		metrics:     newTracingMetrics(),
		spans:       make(map[string]*Span),
		serviceName: config.ServiceName,
	}

	return tracer
}

// TracingMiddleware provides distributed tracing using current Encore middleware API
// TracingMiddleware provides distributed tracing
// Note: Global middleware must be defined outside of services
func TracingMiddleware(req middleware.Request, next middleware.Next) middleware.Response {
	start := time.Now()
	
	// For now, we'll implement a simplified tracing middleware
	// In a full implementation, you would extract trace information
	// from the request and create proper spans
	
	resp := next(req)
	
	// Log processing time (simplified tracing)
	_ = time.Since(start)
	
	return resp
}

// StartSpan creates a new span (simplified for compatibility)
func (t *Tracer) StartSpan(ctx context.Context, operationName string) (*Span, context.Context) {
	// Generate span IDs
	traceID := generateTraceID()
	spanID := generateSpanID()
	
	span := &Span{
		TraceID:       traceID,
		SpanID:        spanID,
		OperationName: operationName,
		ServiceName:   t.serviceName,
		StartTime:     time.Now(),
		Tags:          make(map[string]string),
		Logs:          []LogEntry{},
		Status:        SpanStatus{Code: 0, Message: "OK"},
	}

	// Add default tags
	span.SetTag("service.name", t.serviceName)
	span.SetTag("service.version", t.config.ServiceVersion)
	span.SetTag("environment", t.config.Environment)

	// Store span
	t.spans[spanID] = span

	// Update metrics
	t.metrics.spansCreated.WithLabelValues(t.serviceName, operationName).Inc()
	t.metrics.activeSpans.Inc()

	// Add span to context
	ctx = ContextWithSpan(ctx, span)

	return span, ctx
}

// FinishSpan completes a span
func (t *Tracer) FinishSpan(span *Span) {
	if span == nil {
		return
	}

	span.EndTime = time.Now()
	span.Duration = span.EndTime.Sub(span.StartTime)

	// Update metrics
	t.metrics.spansFinished.WithLabelValues(t.serviceName, span.OperationName).Inc()
	t.metrics.spanDuration.WithLabelValues(t.serviceName, span.OperationName).Observe(span.Duration.Seconds())
	t.metrics.activeSpans.Dec()

	if span.Error {
		t.metrics.traceErrors.WithLabelValues(t.serviceName, span.OperationName).Inc()
	}

	// Export span (in production, this would go to Jaeger/Zipkin)
	t.exportSpan(span)

	// Clean up
	delete(t.spans, span.SpanID)
}

// Simplified methods for compatibility
func (s *Span) SetTag(key, value string) {
	s.Tags[key] = value
}

func (s *Span) SetError(err error) {
	s.Error = true
	s.Status = SpanStatus{Code: 1, Message: err.Error()}
	s.SetTag("error", "true")
	s.SetTag("error.message", err.Error())
}

func (s *Span) LogEvent(level, message string, fields map[string]string) {
	logEntry := LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}
	s.Logs = append(s.Logs, logEntry)
}

func (s *Span) LogInfo(message string, fields map[string]string) {
	s.LogEvent("info", message, fields)
}

func (s *Span) LogError(message string, fields map[string]string) {
	s.LogEvent("error", message, fields)
}

func (s *Span) LogDebug(message string, fields map[string]string) {
	s.LogEvent("debug", message, fields)
}

// Context management for spans
func ContextWithSpan(ctx context.Context, span *Span) context.Context {
	return context.WithValue(ctx, contextKey("span"), span)
}

func SpanFromContext(ctx context.Context) *Span {
	if span, ok := ctx.Value(contextKey("span")).(*Span); ok {
		return span
	}
	return nil
}

// Simplified tracing methods for various operations
func (t *Tracer) TraceDBQuery(ctx context.Context, query string, args ...interface{}) (*Span, context.Context) {
	return t.StartSpan(ctx, "db.query")
}

func (t *Tracer) TraceDBTransaction(ctx context.Context) (*Span, context.Context) {
	return t.StartSpan(ctx, "db.transaction")
}

func (t *Tracer) TraceCacheOperation(ctx context.Context, operation, key string) (*Span, context.Context) {
	return t.StartSpan(ctx, fmt.Sprintf("cache.%s", operation))
}

func (t *Tracer) TraceHTTPRequest(ctx context.Context, method, url string) (*Span, context.Context) {
	return t.StartSpan(ctx, fmt.Sprintf("http.%s", method))
}

func (t *Tracer) TraceMQTTPublish(ctx context.Context, topic string) (*Span, context.Context) {
	return t.StartSpan(ctx, "mqtt.publish")
}

func (t *Tracer) TraceMQTTSubscribe(ctx context.Context, topic string) (*Span, context.Context) {
	return t.StartSpan(ctx, "mqtt.subscribe")
}

func (t *Tracer) TraceServiceCall(ctx context.Context, serviceName, operation string) (*Span, context.Context) {
	return t.StartSpan(ctx, fmt.Sprintf("%s.%s", serviceName, operation))
}

func (t *Tracer) exportSpan(span *Span) {
	// In production, this would send to Jaeger/Zipkin
	// For now, just log (simplified)
}

func generateTraceID() string {
	return fmt.Sprintf("trace-%d", time.Now().UnixNano())
}

func generateSpanID() string {
	return fmt.Sprintf("span-%d", time.Now().UnixNano())
}

type contextKey string

func newTracingMetrics() *TracingMetrics {
	return &TracingMetrics{
		spansCreated: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_tracing_spans_created_total",
			Help: "Total number of spans created",
		}, []string{"service", "operation"}),
		spansFinished: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_tracing_spans_finished_total",
			Help: "Total number of spans finished",
		}, []string{"service", "operation"}),
		spanDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name: "hopen_tracing_span_duration_seconds",
			Help: "Span duration in seconds",
		}, []string{"service", "operation"}),
		traceErrors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_tracing_errors_total",
			Help: "Total number of trace errors",
		}, []string{"service", "operation"}),
		activeSpans: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_tracing_active_spans",
			Help: "Number of active spans",
		}),
		tracesPerSecond: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_tracing_traces_per_second",
			Help: "Number of traces per second",
		}),
	}
}

func DefaultConfig() *Config {
	return &Config{
		ServiceName:     "hopen-service",
		ServiceVersion:  "1.0.0",
		Environment:     "development",
		SamplingRate:    1.0,
		JaegerEndpoint:  "http://localhost:14268/api/traces",
		EnableMetrics:   true,
		EnableLogging:   true,
		MaxSpanDuration: 30 * time.Second,
		BatchTimeout:    5 * time.Second,
		MaxBatchSize:    100,
	}
}

type TraceStats struct {
	ActiveSpans     int64   `json:"active_spans"`
	SpansCreated    int64   `json:"spans_created"`
	SpansFinished   int64   `json:"spans_finished"`
	ErrorRate       float64 `json:"error_rate"`
	AvgDuration     float64 `json:"avg_duration_ms"`
	TracesPerSecond float64 `json:"traces_per_second"`
}

func (t *Tracer) GetStats() TraceStats {
	return TraceStats{
		ActiveSpans:     int64(len(t.spans)),
		SpansCreated:    0, // Would get from metrics
		SpansFinished:   0, // Would get from metrics
		ErrorRate:       0.0,
		AvgDuration:     0.0,
		TracesPerSecond: 0.0,
	}
} 