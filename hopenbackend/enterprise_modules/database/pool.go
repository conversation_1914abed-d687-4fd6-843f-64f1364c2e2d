// Package database provides enterprise-grade database connection management
// with read/write splitting, connection pooling, and comprehensive monitoring.
package database

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// DatabaseManager manages database connections with read/write splitting
type DatabaseManager struct {
	masterPool *pgxpool.Pool
	readPool   *pgxpool.Pool
	metrics    *DatabaseMetrics
	config     *Config
	mu         sync.RWMutex
}

// Config holds database configuration
type Config struct {
	MasterDSN         string
	ReadDSN           string
	MaxConns          int32
	MinConns          int32
	MaxConnLifetime   time.Duration
	MaxConnIdleTime   time.Duration
	HealthCheckPeriod time.Duration
	ApplicationName   string
}

// DatabaseMetrics holds Prometheus metrics for database operations
type DatabaseMetrics struct {
	activeConnections  prometheus.Gauge
	idleConnections    prometheus.Gauge
	waitingConnections prometheus.Gauge
	queryDuration      prometheus.Histogram
	connectionErrors   prometheus.Counter
	transactionCount   prometheus.Counter
	queryCount         *prometheus.CounterVec
}

// newDatabaseMetrics initializes Prometheus metrics
func newDatabaseMetrics() *DatabaseMetrics {
	return &DatabaseMetrics{
		activeConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_db_active_connections_total",
			Help: "Total number of active database connections",
		}),
		idleConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_db_idle_connections_total",
			Help: "Total number of idle database connections",
		}),
		waitingConnections: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_db_waiting_connections_total",
			Help: "Total number of connections waiting for availability",
		}),
		queryDuration: promauto.NewHistogram(prometheus.HistogramOpts{
			Name:    "hopen_db_query_duration_seconds",
			Help:    "Database query duration in seconds",
			Buckets: prometheus.DefBuckets,
		}),
		connectionErrors: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_db_connection_errors_total",
			Help: "Total number of database connection errors",
		}),
		transactionCount: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_db_transactions_total",
			Help: "Total number of database transactions",
		}),
		queryCount: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_db_queries_total",
			Help: "Total number of database queries by type",
		}, []string{"operation", "pool"}),
	}
}

// NewDatabaseManager creates a new database manager with read/write splitting
func NewDatabaseManager(config *Config) (*DatabaseManager, error) {
	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Master pool configuration (writes)
	masterConfig, err := pgxpool.ParseConfig(config.MasterDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to parse master DSN: %w", err)
	}

	configurePGXPool(masterConfig, config, "master")

	// Read replica pool configuration
	readConfig, err := pgxpool.ParseConfig(config.ReadDSN)
	if err != nil {
		return nil, fmt.Errorf("failed to parse read DSN: %w", err)
	}

	configurePGXPool(readConfig, config, "read")

	// Create connection pools
	masterPool, err := pgxpool.NewWithConfig(context.Background(), masterConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create master pool: %w", err)
	}

	readPool, err := pgxpool.NewWithConfig(context.Background(), readConfig)
	if err != nil {
		masterPool.Close()
		return nil, fmt.Errorf("failed to create read pool: %w", err)
	}

	dm := &DatabaseManager{
		masterPool: masterPool,
		readPool:   readPool,
		metrics:    newDatabaseMetrics(),
		config:     config,
	}

	// Start metrics collection
	dm.startMetricsCollection()

	return dm, nil
}

// configurePGXPool configures a pgxpool.Config with our settings
func configurePGXPool(config *pgxpool.Config, dbConfig *Config, poolType string) {
	config.MaxConns = dbConfig.MaxConns
	config.MinConns = dbConfig.MinConns
	config.MaxConnLifetime = dbConfig.MaxConnLifetime
	config.MaxConnIdleTime = dbConfig.MaxConnIdleTime
	config.HealthCheckPeriod = dbConfig.HealthCheckPeriod

	// Connection hooks for monitoring and setup
	config.BeforeAcquire = func(ctx context.Context, conn *pgx.Conn) bool {
		// Add custom logic if needed (e.g., connection validation)
		return true
	}

	config.AfterConnect = func(ctx context.Context, conn *pgx.Conn) error {
		// Set connection-level parameters
		queries := []string{
			fmt.Sprintf("SET application_name = '%s'", dbConfig.ApplicationName),
			"SET timezone = 'UTC'",
			"SET statement_timeout = '30s'",
			"SET lock_timeout = '10s'",
		}

		for _, query := range queries {
			if _, err := conn.Exec(ctx, query); err != nil {
				return fmt.Errorf("failed to execute setup query '%s': %w", query, err)
			}
		}

		return nil
	}

	config.BeforeClose = func(conn *pgx.Conn) {
		// Cleanup logic if needed
	}
}

// QueryRead executes a read query using the read replica pool
func (dm *DatabaseManager) QueryRead(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.queryCount.WithLabelValues("read", "read").Inc()
	}()

	rows, err := dm.readPool.Query(ctx, query, args...)
	if err != nil {
		dm.metrics.connectionErrors.Inc()
		return nil, fmt.Errorf("read query failed: %w", err)
	}

	return rows, nil
}

// QueryWrite executes a write query using the master pool
func (dm *DatabaseManager) QueryWrite(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.queryCount.WithLabelValues("write", "master").Inc()
	}()

	rows, err := dm.masterPool.Query(ctx, query, args...)
	if err != nil {
		dm.metrics.connectionErrors.Inc()
		return nil, fmt.Errorf("write query failed: %w", err)
	}

	return rows, nil
}

// ExecWrite executes a write command using the master pool
func (dm *DatabaseManager) ExecWrite(ctx context.Context, query string, args ...interface{}) error {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.queryCount.WithLabelValues("exec", "master").Inc()
	}()

	_, err := dm.masterPool.Exec(ctx, query, args...)
	if err != nil {
		dm.metrics.connectionErrors.Inc()
		return fmt.Errorf("exec write failed: %w", err)
	}

	return nil
}

// QueryRowRead executes a single-row read query
func (dm *DatabaseManager) QueryRowRead(ctx context.Context, query string, args ...interface{}) pgx.Row {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.queryCount.WithLabelValues("query_row_read", "read").Inc()
	}()

	return dm.readPool.QueryRow(ctx, query, args...)
}

// QueryRowWrite executes a single-row write query
func (dm *DatabaseManager) QueryRowWrite(ctx context.Context, query string, args ...interface{}) pgx.Row {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.queryCount.WithLabelValues("query_row_write", "master").Inc()
	}()

	return dm.masterPool.QueryRow(ctx, query, args...)
}

// TransactionWrite executes a function within a write transaction
func (dm *DatabaseManager) TransactionWrite(ctx context.Context, fn func(tx pgx.Tx) error) error {
	start := time.Now()
	defer func() {
		dm.metrics.queryDuration.Observe(time.Since(start).Seconds())
		dm.metrics.transactionCount.Inc()
	}()

	return pgx.BeginFunc(ctx, dm.masterPool, fn)
}

// HealthCheck verifies both pools are healthy
func (dm *DatabaseManager) HealthCheck(ctx context.Context) error {
	// Check master pool
	if err := dm.masterPool.Ping(ctx); err != nil {
		return fmt.Errorf("master pool health check failed: %w", err)
	}

	// Check read pool
	if err := dm.readPool.Ping(ctx); err != nil {
		return fmt.Errorf("read pool health check failed: %w", err)
	}

	return nil
}

// GetStats returns current pool statistics
func (dm *DatabaseManager) GetStats() (masterStats, readStats *pgxpool.Stat) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	return dm.masterPool.Stat(), dm.readPool.Stat()
}

// startMetricsCollection starts a goroutine to collect pool metrics
func (dm *DatabaseManager) startMetricsCollection() {
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for range ticker.C {
			masterStats := dm.masterPool.Stat()
			readStats := dm.readPool.Stat()

			// Update metrics
			dm.metrics.activeConnections.Set(float64(
				masterStats.AcquiredConns() + readStats.AcquiredConns(),
			))
			dm.metrics.idleConnections.Set(float64(
				masterStats.IdleConns() + readStats.IdleConns(),
			))
			dm.metrics.waitingConnections.Set(float64(
				masterStats.EmptyAcquireCount() + readStats.EmptyAcquireCount(),
			))
		}
	}()
}

// Close gracefully closes both connection pools
func (dm *DatabaseManager) Close() {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	if dm.masterPool != nil {
		dm.masterPool.Close()
	}
	if dm.readPool != nil {
		dm.readPool.Close()
	}
}

// DefaultConfig returns a sensible default configuration
func DefaultConfig() *Config {
	return &Config{
		MaxConns:          30,
		MinConns:          5,
		MaxConnLifetime:   time.Hour,
		MaxConnIdleTime:   30 * time.Minute,
		HealthCheckPeriod: time.Minute,
		ApplicationName:   "hopen-backend",
	}
} 