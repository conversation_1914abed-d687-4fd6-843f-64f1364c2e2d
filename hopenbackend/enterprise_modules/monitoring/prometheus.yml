global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Hopen Backend Application
  - job_name: 'hopen-backend'
    static_configs:
      - targets: ['hopen-backend:4000']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # Node Exporter (System metrics)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # cAdvisor (Container metrics)
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']

  # PostgreSQL Database
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    metrics_path: '/metrics'

  # Redis Cache
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']

  # EMQX MQTT Broker
  - job_name: 'emqx'
    static_configs:
      - targets: ['emqx:18083']
    metrics_path: '/api/v5/prometheus/stats'

  # MinIO Object Storage
  - job_name: 'minio'
    static_configs:
      - targets: ['minio:9000']
    metrics_path: '/minio/v2/metrics/cluster'

  # Ory Kratos
  - job_name: 'ory-kratos'
    static_configs:
      - targets: ['ory-kratos:4434']
    metrics_path: '/admin/metrics/prometheus'

  # Ory Hydra
  - job_name: 'ory-hydra'
    static_configs:
      - targets: ['ory-hydra:4445']
    metrics_path: '/admin/metrics/prometheus'

  # Docker containers
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323'] 