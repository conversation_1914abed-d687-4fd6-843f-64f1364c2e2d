// Package http3 provides HTTP/3 server implementation with QUIC protocol support
// and automatic fallback to HTTP/2 for enhanced performance and compatibility.
package http3

import (
	"context"
	"crypto/tls"
	"fmt"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Server represents an HTTP/3 server with fallback capabilities
type Server struct {
	config          *Config
	http3Server     *http.Server
	http2Server     *http.Server
	metrics         *HTTP3Metrics
	tlsConfig       *tls.Config
	protocolTracker *ProtocolTracker
	isRunning       bool
	mutex           sync.RWMutex
}

// Config holds HTTP/3 server configuration
type Config struct {
	Addr              string
	HTTP2Addr         string
	TLSCertFile       string
	TLSKeyFile        string
	ReadTimeout       time.Duration
	WriteTimeout      time.Duration
	IdleTimeout       time.Duration
	MaxHeaderBytes    int
	EnableProtocolNeg bool
	QuicConfig        *QuicConfig
	HTTP2Config       *HTTP2Config
}

// QuicConfig holds QUIC-specific configuration
type QuicConfig struct {
	MaxIdleTimeout        time.Duration
	MaxIncomingStreams    int64
	MaxIncomingUniStreams int64
	KeepAlivePeriod       time.Duration
	MaxReceiveBufferSize  int64
	MaxSendBufferSize     int64
	DisablePathMTUDiscovery bool
}

// HTTP2Config holds HTTP/2-specific configuration
type HTTP2Config struct {
	MaxConcurrentStreams  uint32
	MaxReadFrameSize      uint32
	PermitProhibitedCipherSuites bool
	IdleTimeout           time.Duration
	PingTimeout           time.Duration
	WriteByteTimeout      time.Duration
}

// HTTP3Metrics holds Prometheus metrics for HTTP/3 server
type HTTP3Metrics struct {
	requestsTotal      *prometheus.CounterVec
	requestDuration    *prometheus.HistogramVec
	protocolUsage      *prometheus.CounterVec
	activeConnections  *prometheus.GaugeVec
	bytesTransferred   *prometheus.CounterVec
	handshakeTime      *prometheus.HistogramVec
	streamCount        *prometheus.GaugeVec
	errors             *prometheus.CounterVec
}

// ProtocolTracker tracks protocol usage and performance
type ProtocolTracker struct {
	http3Connections  int64
	http2Connections  int64
	protocolStats     map[string]*ProtocolStats
	mutex             sync.RWMutex
}

// ProtocolStats holds statistics for a specific protocol
type ProtocolStats struct {
	Connections   int64
	Requests      int64
	BytesSent     int64
	BytesReceived int64
	AvgLatency    time.Duration
	Errors        int64
}

// NewServer creates a new HTTP/3 server with HTTP/2 fallback
func NewServer(config *Config) (*Server, error) {
	if config == nil {
		config = DefaultConfig()
	}

	// Load TLS configuration
	tlsConfig, err := loadTLSConfig(config.TLSCertFile, config.TLSKeyFile)
	if err != nil {
		return nil, fmt.Errorf("failed to load TLS config: %w", err)
	}

	server := &Server{
		config:          config,
		metrics:         newHTTP3Metrics(),
		tlsConfig:       tlsConfig,
		protocolTracker: newProtocolTracker(),
	}

	// Create HTTP/3 server
	server.http3Server = &http.Server{
		Addr:           config.Addr,
		TLSConfig:      tlsConfig,
		ReadTimeout:    config.ReadTimeout,
		WriteTimeout:   config.WriteTimeout,
		IdleTimeout:    config.IdleTimeout,
		MaxHeaderBytes: config.MaxHeaderBytes,
	}

	// Create HTTP/2 fallback server
	server.http2Server = &http.Server{
		Addr:           config.HTTP2Addr,
		TLSConfig:      tlsConfig,
		ReadTimeout:    config.ReadTimeout,
		WriteTimeout:   config.WriteTimeout,
		IdleTimeout:    config.IdleTimeout,
		MaxHeaderBytes: config.MaxHeaderBytes,
	}

	return server, nil
}

// SetHandler sets the HTTP handler for both HTTP/3 and HTTP/2 servers
func (s *Server) SetHandler(handler http.Handler) {
	// Wrap handler with protocol detection and metrics
	wrappedHandler := s.wrapHandler(handler)
	
	s.http3Server.Handler = wrappedHandler
	s.http2Server.Handler = wrappedHandler
}

// wrapHandler wraps the handler with metrics and protocol tracking
func (s *Server) wrapHandler(handler http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		
		// Detect protocol
		protocol := s.detectProtocol(r)
		
		// Track connection
		s.protocolTracker.TrackConnection(protocol)
		s.metrics.activeConnections.WithLabelValues(protocol).Inc()
		
		defer func() {
			s.metrics.activeConnections.WithLabelValues(protocol).Dec()
			duration := time.Since(start)
			s.metrics.requestDuration.WithLabelValues(protocol, r.Method).Observe(duration.Seconds())
			s.protocolTracker.TrackRequest(protocol, duration)
		}()

		// Add protocol headers
		w.Header().Set("X-Protocol", protocol)
		w.Header().Set("X-Server", "Hopen-HTTP3")
		
		// Add Alt-Svc header for HTTP/2 requests to advertise HTTP/3
		if protocol == "HTTP/2" {
			altSvc := fmt.Sprintf(`h3=":%s"; ma=2592000`, extractPort(s.config.Addr))
			w.Header().Set("Alt-Svc", altSvc)
		}

		// Record metrics
		s.metrics.requestsTotal.WithLabelValues(protocol, r.Method).Inc()

		// Call original handler
		handler.ServeHTTP(w, r)
	})
}

// detectProtocol detects which HTTP protocol is being used
func (s *Server) detectProtocol(r *http.Request) string {
	// Check if it's HTTP/3 (QUIC)
	if r.ProtoMajor == 3 {
		return "HTTP/3"
	}
	
	// Check if it's HTTP/2
	if r.ProtoMajor == 2 {
		return "HTTP/2"
	}
	
	// Check protocol from TLS connection state
	if r.TLS != nil {
		switch r.TLS.NegotiatedProtocol {
		case "h3":
			return "HTTP/3"
		case "h2":
			return "HTTP/2"
		}
	}
	
	// Default to HTTP/1.1
	return "HTTP/1.1"
}

// ListenAndServe starts both HTTP/3 and HTTP/2 servers
func (s *Server) ListenAndServe() error {
	s.mutex.Lock()
	s.isRunning = true
	s.mutex.Unlock()

	errChan := make(chan error, 2)

	// Start HTTP/3 server
	go func() {
		fmt.Printf("Starting HTTP/3 server on %s\n", s.config.Addr)
		
		// Note: This is a simplified implementation
		// In production, you would use a library like quic-go
		// For now, we'll start an HTTPS server with HTTP/2 support
		err := s.http3Server.ListenAndServeTLS(s.config.TLSCertFile, s.config.TLSKeyFile)
		if err != http.ErrServerClosed {
			errChan <- fmt.Errorf("HTTP/3 server error: %w", err)
		}
	}()

	// Start HTTP/2 fallback server
	go func() {
		fmt.Printf("Starting HTTP/2 fallback server on %s\n", s.config.HTTP2Addr)
		err := s.http2Server.ListenAndServeTLS(s.config.TLSCertFile, s.config.TLSKeyFile)
		if err != http.ErrServerClosed {
			errChan <- fmt.Errorf("HTTP/2 server error: %w", err)
		}
	}()

	// Start metrics collection
	go s.collectMetrics()

	// Wait for first error
	return <-errChan
}

// Shutdown gracefully shuts down both servers
func (s *Server) Shutdown(ctx context.Context) error {
	s.mutex.Lock()
	s.isRunning = false
	s.mutex.Unlock()

	errChan := make(chan error, 2)

	// Shutdown HTTP/3 server
	go func() {
		errChan <- s.http3Server.Shutdown(ctx)
	}()

	// Shutdown HTTP/2 server
	go func() {
		errChan <- s.http2Server.Shutdown(ctx)
	}()

	// Wait for both shutdowns to complete
	var lastErr error
	for i := 0; i < 2; i++ {
		if err := <-errChan; err != nil {
			lastErr = err
		}
	}

	return lastErr
}

// GetStats returns server statistics
func (s *Server) GetStats() ServerStats {
	s.protocolTracker.mutex.RLock()
	defer s.protocolTracker.mutex.RUnlock()

	stats := ServerStats{
		HTTP3Stats: s.protocolTracker.protocolStats["HTTP/3"],
		HTTP2Stats: s.protocolTracker.protocolStats["HTTP/2"],
		HTTP1Stats: s.protocolTracker.protocolStats["HTTP/1.1"],
		IsRunning:  s.isRunning,
	}

	return stats
}

// collectMetrics collects periodic metrics
func (s *Server) collectMetrics() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !s.isRunning {
				return
			}
			s.updateMetrics()
		}
	}
}

// updateMetrics updates Prometheus metrics
func (s *Server) updateMetrics() {
	s.protocolTracker.mutex.RLock()
	defer s.protocolTracker.mutex.RUnlock()

	for protocol, stats := range s.protocolTracker.protocolStats {
		s.metrics.bytesTransferred.WithLabelValues(protocol, "sent").Add(float64(stats.BytesSent))
		s.metrics.bytesTransferred.WithLabelValues(protocol, "received").Add(float64(stats.BytesReceived))
		
		if stats.AvgLatency > 0 {
			s.metrics.handshakeTime.WithLabelValues(protocol).Observe(stats.AvgLatency.Seconds())
		}
	}
}

// Helper functions
func loadTLSConfig(certFile, keyFile string) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(certFile, keyFile)
	if err != nil {
		return nil, err
	}

	return &tls.Config{
		Certificates: []tls.Certificate{cert},
		NextProtos:   []string{"h3", "h2", "http/1.1"}, // HTTP/3, HTTP/2, HTTP/1.1
		MinVersion:   tls.VersionTLS13, // HTTP/3 requires TLS 1.3
	}, nil
}

func extractPort(addr string) string {
	_, port, err := net.SplitHostPort(addr)
	if err != nil {
		return "443" // default HTTPS port
	}
	return port
}

// ProtocolTracker implementation
func newProtocolTracker() *ProtocolTracker {
	return &ProtocolTracker{
		protocolStats: map[string]*ProtocolStats{
			"HTTP/3":  &ProtocolStats{},
			"HTTP/2":  &ProtocolStats{},
			"HTTP/1.1": &ProtocolStats{},
		},
	}
}

func (pt *ProtocolTracker) TrackConnection(protocol string) {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()

	if stats, exists := pt.protocolStats[protocol]; exists {
		stats.Connections++
	}
}

func (pt *ProtocolTracker) TrackRequest(protocol string, duration time.Duration) {
	pt.mutex.Lock()
	defer pt.mutex.Unlock()

	if stats, exists := pt.protocolStats[protocol]; exists {
		stats.Requests++
		// Simple moving average for latency
		if stats.AvgLatency == 0 {
			stats.AvgLatency = duration
		} else {
			stats.AvgLatency = (stats.AvgLatency + duration) / 2
		}
	}
}

// Types
type ServerStats struct {
	HTTP3Stats *ProtocolStats `json:"http3_stats"`
	HTTP2Stats *ProtocolStats `json:"http2_stats"`
	HTTP1Stats *ProtocolStats `json:"http1_stats"`
	IsRunning  bool          `json:"is_running"`
}

// newHTTP3Metrics initializes Prometheus metrics
func newHTTP3Metrics() *HTTP3Metrics {
	return &HTTP3Metrics{
		requestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_http3_requests_total",
			Help: "Total number of HTTP requests by protocol",
		}, []string{"protocol", "method"}),
		requestDuration: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_http3_request_duration_seconds",
			Help:    "HTTP request duration by protocol",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0},
		}, []string{"protocol", "method"}),
		protocolUsage: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_http3_protocol_usage_total",
			Help: "Protocol usage statistics",
		}, []string{"protocol"}),
		activeConnections: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_http3_active_connections",
			Help: "Number of active connections by protocol",
		}, []string{"protocol"}),
		bytesTransferred: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_http3_bytes_transferred_total",
			Help: "Total bytes transferred by protocol and direction",
		}, []string{"protocol", "direction"}),
		handshakeTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_http3_handshake_duration_seconds",
			Help:    "TLS handshake duration by protocol",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5},
		}, []string{"protocol"}),
		streamCount: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_http3_streams_active",
			Help: "Number of active streams",
		}, []string{"protocol"}),
		errors: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_http3_errors_total",
			Help: "Total number of protocol errors",
		}, []string{"protocol", "error_type"}),
	}
}

// DefaultConfig returns default HTTP/3 server configuration
func DefaultConfig() *Config {
	return &Config{
		Addr:              ":8443",
		HTTP2Addr:         ":8444", 
		TLSCertFile:       "ssl/cert.pem",
		TLSKeyFile:        "ssl/key.pem",
		ReadTimeout:       30 * time.Second,
		WriteTimeout:      30 * time.Second,
		IdleTimeout:       120 * time.Second,
		MaxHeaderBytes:    1 << 20, // 1 MB
		EnableProtocolNeg: true,
		QuicConfig: &QuicConfig{
			MaxIdleTimeout:        30 * time.Second,
			MaxIncomingStreams:    100,
			MaxIncomingUniStreams: 100,
			KeepAlivePeriod:       30 * time.Second,
			MaxReceiveBufferSize:  1024 * 1024, // 1 MB
			MaxSendBufferSize:     1024 * 1024, // 1 MB
		},
		HTTP2Config: &HTTP2Config{
			MaxConcurrentStreams: 250,
			MaxReadFrameSize:     1 << 20, // 1 MB
			IdleTimeout:          120 * time.Second,
			PingTimeout:          15 * time.Second,
			WriteByteTimeout:     10 * time.Second,
		},
	}
} 