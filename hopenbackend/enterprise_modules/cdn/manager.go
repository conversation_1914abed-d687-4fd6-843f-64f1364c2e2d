// Package cdn provides Content Delivery Network management with edge caching,
// asset optimization, and global distribution for maximum performance.
package cdn

import (
	"crypto/md5"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strings"
	"sync"
	"time"

	// "encore.dev/api" // Commented out for compilation
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

// Manager handles CDN operations and asset management
type Manager struct {
	config      *Config
	cache       *EdgeCache
	optimizer   *AssetOptimizer
	distributor *GlobalDistributor
	metrics     *CDNMetrics
	middleware  []Middleware
	mutex       sync.RWMutex
}

// Config holds CDN configuration
type Config struct {
	EnableCDN           bool
	EdgeLocations       []EdgeLocation
	CacheTTL           time.Duration
	MaxCacheSize       int64
	EnableCompression   bool
	EnableOptimization  bool
	EnablePurging      bool
	CompressionLevel   int
	ImageQuality       int
	EnableWebP         bool
	EnableBrotli       bool
	PurgeAPIKey        string
	OriginServers      []string
}

// EdgeLocation represents a CDN edge location
type EdgeLocation struct {
	ID          string
	Region      string
	Country     string
	City        string
	Latitude    float64
	Longitude   float64
	Capacity    int64
	Status      string
	LastChecked time.Time
}

// EdgeCache manages edge-level caching
type EdgeCache struct {
	entries    map[string]*CacheEntry
	size       int64
	maxSize    int64
	ttl        time.Duration
	mutex      sync.RWMutex
	evictions  chan string
}

// CacheEntry represents a cached asset
type CacheEntry struct {
	Key         string
	Content     []byte
	ContentType string
	ETag        string
	LastModified time.Time
	ExpiresAt   time.Time
	HitCount    int64
	Size        int64
	Compressed  bool
}

// AssetOptimizer handles asset optimization
type AssetOptimizer struct {
	config    *Config
	processors map[string]AssetProcessor
	metrics   *OptimizationMetrics
}

// AssetProcessor defines asset processing interface
type AssetProcessor interface {
	Process(content []byte, options map[string]interface{}) ([]byte, error)
	SupportedTypes() []string
}

// GlobalDistributor manages global asset distribution
type GlobalDistributor struct {
	edges     map[string]*EdgeLocation
	router    *GeographicRouter
	balancer  *LoadBalancer
	health    *HealthChecker
	metrics   *DistributionMetrics
}

// GeographicRouter routes requests to nearest edge
type GeographicRouter struct {
	locations map[string]*EdgeLocation
	mutex     sync.RWMutex
}

// LoadBalancer distributes load across edges
type LoadBalancer struct {
	algorithm string // round-robin, least-connections, geographic
	weights   map[string]int
	mutex     sync.RWMutex
}

// HealthChecker monitors edge health
type HealthChecker struct {
	checks   map[string]*HealthCheck
	interval time.Duration
	timeout  time.Duration
	mutex    sync.RWMutex
}

// HealthCheck represents edge health status
type HealthCheck struct {
	EdgeID      string
	Status      string
	LastCheck   time.Time
	ResponseTime time.Duration
	ErrorCount  int64
}

// CDNMetrics holds Prometheus metrics
type CDNMetrics struct {
	requestsTotal     *prometheus.CounterVec
	cacheHits         *prometheus.CounterVec
	cacheMisses       *prometheus.CounterVec
	bandwidthSaved    prometheus.Counter
	responseTime      *prometheus.HistogramVec
	edgeHealth        *prometheus.GaugeVec
	cacheSize         prometheus.Gauge
	optimizationRatio *prometheus.HistogramVec
}

// OptimizationMetrics holds optimization-specific metrics
type OptimizationMetrics struct {
	compressionRatio *prometheus.HistogramVec
	processingTime   *prometheus.HistogramVec
	sizeBefore       *prometheus.HistogramVec
	sizeAfter        *prometheus.HistogramVec
}

// DistributionMetrics holds distribution-specific metrics
type DistributionMetrics struct {
	edgeRequests    *prometheus.CounterVec
	routingLatency  *prometheus.HistogramVec
	failoverCount   *prometheus.CounterVec
	edgeCapacity    *prometheus.GaugeVec
}

// Middleware defines CDN middleware
type Middleware func(*http.Request, *CacheEntry) error

// NewManager creates a new CDN manager
func NewManager(config *Config) *Manager {
	if config == nil {
		config = DefaultConfig()
	}

	manager := &Manager{
		config:      config,
		cache:       NewEdgeCache(config.MaxCacheSize, config.CacheTTL),
		optimizer:   NewAssetOptimizer(config),
		distributor: NewGlobalDistributor(config),
		metrics:     newCDNMetrics(),
		middleware:  []Middleware{},
	}

	// Start background processes
	go manager.cache.startEvictionProcess()
	go manager.distributor.health.startHealthChecks()

	return manager
}

// CDN API endpoints - Commented out for compilation
/*
var ServeAsset = api.Raw(
	api.RawConfig{
		Path:   "/cdn/*path",
		Method: "GET",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.ServeAsset(w, r)
	},
)

var PurgeCache = api.Raw(
	api.RawConfig{
		Path:   "/cdn/purge",
		Method: "POST",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.PurgeCache(w, r)
	},
)

var CDNStats = api.Raw(
	api.RawConfig{
		Path:   "/cdn/stats",
		Method: "GET",
		Expose: true,
	},
	func(w http.ResponseWriter, r *http.Request) {
		manager := GetDefaultManager()
		manager.GetStats(w, r)
	},
)
*/

// ServeAsset serves assets through CDN
func (m *Manager) ServeAsset(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	path := strings.TrimPrefix(r.URL.Path, "/cdn/")
	
	// Apply middleware
	for _, middleware := range m.middleware {
		if err := middleware(r, nil); err != nil {
			http.Error(w, err.Error(), http.StatusBadRequest)
			return
		}
	}

	// Check cache first
	if entry := m.cache.Get(path); entry != nil {
		m.serveCachedAsset(w, r, entry)
		m.metrics.cacheHits.WithLabelValues(m.getAssetType(path)).Inc()
		m.metrics.responseTime.WithLabelValues("cache").Observe(time.Since(start).Seconds())
		return
	}

	// Cache miss - fetch from origin
	m.metrics.cacheMisses.WithLabelValues(m.getAssetType(path)).Inc()
	
	content, contentType, err := m.fetchFromOrigin(path)
	if err != nil {
		http.Error(w, "Asset not found", http.StatusNotFound)
		return
	}

	// Optimize asset if enabled
	if m.config.EnableOptimization {
		optimized, err := m.optimizer.OptimizeAsset(content, contentType, r.Header)
		if err == nil {
			content = optimized
		}
	}

	// Create cache entry
	entry := &CacheEntry{
		Key:         path,
		Content:     content,
		ContentType: contentType,
		ETag:        m.generateETag(content),
		LastModified: time.Now(),
		ExpiresAt:   time.Now().Add(m.config.CacheTTL),
		Size:        int64(len(content)),
		Compressed:  m.config.EnableCompression,
	}

	// Store in cache
	m.cache.Set(path, entry)

	// Serve asset
	m.serveAsset(w, r, entry)
	m.metrics.responseTime.WithLabelValues("origin").Observe(time.Since(start).Seconds())
}

// PurgeCache purges cached assets
func (m *Manager) PurgeCache(w http.ResponseWriter, r *http.Request) {
	// Verify API key
	apiKey := r.Header.Get("X-Purge-Key")
	if apiKey != m.config.PurgeAPIKey {
		http.Error(w, "Unauthorized", http.StatusUnauthorized)
		return
	}

	var request struct {
		Paths []string `json:"paths"`
		All   bool     `json:"all"`
	}

	if err := parseJSON(r.Body, &request); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	if request.All {
		m.cache.Clear()
	} else {
		for _, path := range request.Paths {
			m.cache.Delete(path)
		}
	}

	w.WriteHeader(http.StatusOK)
	writeJSON(w, map[string]interface{}{
		"status": "success",
		"purged": len(request.Paths),
	})
}

// GetStats returns CDN statistics
func (m *Manager) GetStats(w http.ResponseWriter, r *http.Request) {
	stats := map[string]interface{}{
		"cache": m.cache.GetStats(),
		"edges": m.distributor.GetEdgeStats(),
		"optimization": m.optimizer.GetStats(),
		"health": m.distributor.health.GetHealthStats(),
	}

	writeJSON(w, stats)
}

// Edge Cache implementation
func NewEdgeCache(maxSize int64, ttl time.Duration) *EdgeCache {
	cache := &EdgeCache{
		entries:   make(map[string]*CacheEntry),
		maxSize:   maxSize,
		ttl:       ttl,
		evictions: make(chan string, 1000),
	}
	return cache
}

func (ec *EdgeCache) Get(key string) *CacheEntry {
	ec.mutex.RLock()
	defer ec.mutex.RUnlock()

	entry, exists := ec.entries[key]
	if !exists || time.Now().After(entry.ExpiresAt) {
		return nil
	}

	entry.HitCount++
	return entry
}

func (ec *EdgeCache) Set(key string, entry *CacheEntry) {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	// Check if we need to evict
	if ec.size+entry.Size > ec.maxSize {
		ec.evictLRU()
	}

	ec.entries[key] = entry
	ec.size += entry.Size
}

func (ec *EdgeCache) Delete(key string) {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	if entry, exists := ec.entries[key]; exists {
		delete(ec.entries, key)
		ec.size -= entry.Size
	}
}

func (ec *EdgeCache) Clear() {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	ec.entries = make(map[string]*CacheEntry)
	ec.size = 0
}

func (ec *EdgeCache) evictLRU() {
	// Simple LRU eviction - in production would use more sophisticated algorithm
	var oldestKey string
	var oldestTime time.Time

	for key, entry := range ec.entries {
		if oldestKey == "" || entry.LastModified.Before(oldestTime) {
			oldestKey = key
			oldestTime = entry.LastModified
		}
	}

	if oldestKey != "" {
		ec.size -= ec.entries[oldestKey].Size
		delete(ec.entries, oldestKey)
	}
}

func (ec *EdgeCache) startEvictionProcess() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			ec.cleanupExpired()
		case key := <-ec.evictions:
			ec.Delete(key)
		}
	}
}

func (ec *EdgeCache) cleanupExpired() {
	ec.mutex.Lock()
	defer ec.mutex.Unlock()

	now := time.Now()
	for key, entry := range ec.entries {
		if now.After(entry.ExpiresAt) {
			ec.size -= entry.Size
			delete(ec.entries, key)
		}
	}
}

func (ec *EdgeCache) GetStats() map[string]interface{} {
	ec.mutex.RLock()
	defer ec.mutex.RUnlock()

	return map[string]interface{}{
		"entries":     len(ec.entries),
		"size_bytes":  ec.size,
		"max_size":    ec.maxSize,
		"utilization": float64(ec.size) / float64(ec.maxSize),
	}
}

// Asset Optimizer implementation
func NewAssetOptimizer(config *Config) *AssetOptimizer {
	optimizer := &AssetOptimizer{
		config:     config,
		processors: make(map[string]AssetProcessor),
		metrics:    newOptimizationMetrics(),
	}

	// Register default processors
	optimizer.RegisterProcessor("image", &ImageProcessor{config: config})
	optimizer.RegisterProcessor("css", &CSSProcessor{config: config})
	optimizer.RegisterProcessor("js", &JSProcessor{config: config})

	return optimizer
}

func (ao *AssetOptimizer) RegisterProcessor(assetType string, processor AssetProcessor) {
	ao.processors[assetType] = processor
}

func (ao *AssetOptimizer) OptimizeAsset(content []byte, contentType string, headers http.Header) ([]byte, error) {
	assetType := ao.getAssetType(contentType)
	processor, exists := ao.processors[assetType]
	if !exists {
		return content, nil // No optimization available
	}

	start := time.Now()
	originalSize := len(content)

	options := ao.getOptimizationOptions(headers)
	optimized, err := processor.Process(content, options)
	if err != nil {
		return content, err
	}

	// Update metrics
	optimizedSize := len(optimized)
	ratio := float64(optimizedSize) / float64(originalSize)
	
	ao.metrics.compressionRatio.WithLabelValues(assetType).Observe(ratio)
	ao.metrics.processingTime.WithLabelValues(assetType).Observe(time.Since(start).Seconds())
	ao.metrics.sizeBefore.WithLabelValues(assetType).Observe(float64(originalSize))
	ao.metrics.sizeAfter.WithLabelValues(assetType).Observe(float64(optimizedSize))

	return optimized, nil
}

func (ao *AssetOptimizer) getAssetType(contentType string) string {
	if strings.HasPrefix(contentType, "image/") {
		return "image"
	} else if strings.Contains(contentType, "css") {
		return "css"
	} else if strings.Contains(contentType, "javascript") {
		return "js"
	}
	return "other"
}

func (ao *AssetOptimizer) getOptimizationOptions(headers http.Header) map[string]interface{} {
	options := make(map[string]interface{})
	
	// Check for WebP support
	if strings.Contains(headers.Get("Accept"), "image/webp") {
		options["webp"] = true
	}
	
	// Check for Brotli support
	if strings.Contains(headers.Get("Accept-Encoding"), "br") {
		options["brotli"] = true
	}
	
	return options
}

func (ao *AssetOptimizer) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"processors": len(ao.processors),
		"enabled":    ao.config.EnableOptimization,
	}
}

// Asset Processors
type ImageProcessor struct {
	config *Config
}

func (ip *ImageProcessor) Process(content []byte, options map[string]interface{}) ([]byte, error) {
	// Simplified image processing - in production would use actual image libraries
	if ip.config.EnableWebP && options["webp"] == true {
		// Convert to WebP format
		return content, nil // Placeholder
	}
	return content, nil
}

func (ip *ImageProcessor) SupportedTypes() []string {
	return []string{"image/jpeg", "image/png", "image/gif", "image/webp"}
}

type CSSProcessor struct {
	config *Config
}

func (cp *CSSProcessor) Process(content []byte, options map[string]interface{}) ([]byte, error) {
	// Simplified CSS minification
	minified := strings.ReplaceAll(string(content), "\n", "")
	minified = strings.ReplaceAll(minified, "  ", " ")
	return []byte(minified), nil
}

func (cp *CSSProcessor) SupportedTypes() []string {
	return []string{"text/css"}
}

type JSProcessor struct {
	config *Config
}

func (jp *JSProcessor) Process(content []byte, options map[string]interface{}) ([]byte, error) {
	// Simplified JS minification
	minified := strings.ReplaceAll(string(content), "\n", "")
	minified = strings.ReplaceAll(minified, "  ", " ")
	return []byte(minified), nil
}

func (jp *JSProcessor) SupportedTypes() []string {
	return []string{"application/javascript", "text/javascript"}
}

// Global Distributor implementation
func NewGlobalDistributor(config *Config) *GlobalDistributor {
	distributor := &GlobalDistributor{
		edges:    make(map[string]*EdgeLocation),
		router:   NewGeographicRouter(),
		balancer: NewLoadBalancer("geographic"),
		health:   NewHealthChecker(30*time.Second, 5*time.Second),
		metrics:  newDistributionMetrics(),
	}

	// Initialize edge locations
	for _, edge := range config.EdgeLocations {
		distributor.edges[edge.ID] = &edge
		distributor.router.AddLocation(&edge)
	}

	return distributor
}

func (gd *GlobalDistributor) GetEdgeStats() map[string]interface{} {
	stats := make(map[string]interface{})
	for id, edge := range gd.edges {
		stats[id] = map[string]interface{}{
			"region":   edge.Region,
			"status":   edge.Status,
			"capacity": edge.Capacity,
		}
	}
	return stats
}

// Geographic Router implementation
func NewGeographicRouter() *GeographicRouter {
	return &GeographicRouter{
		locations: make(map[string]*EdgeLocation),
	}
}

func (gr *GeographicRouter) AddLocation(location *EdgeLocation) {
	gr.mutex.Lock()
	defer gr.mutex.Unlock()
	gr.locations[location.ID] = location
}

func (gr *GeographicRouter) FindNearestEdge(clientIP string) *EdgeLocation {
	// Simplified geographic routing - in production would use GeoIP
	gr.mutex.RLock()
	defer gr.mutex.RUnlock()

	for _, location := range gr.locations {
		if location.Status == "healthy" {
			return location
		}
	}
	return nil
}

// Load Balancer implementation
func NewLoadBalancer(algorithm string) *LoadBalancer {
	return &LoadBalancer{
		algorithm: algorithm,
		weights:   make(map[string]int),
	}
}

// Health Checker implementation
func NewHealthChecker(interval, timeout time.Duration) *HealthChecker {
	return &HealthChecker{
		checks:   make(map[string]*HealthCheck),
		interval: interval,
		timeout:  timeout,
	}
}

func (hc *HealthChecker) startHealthChecks() {
	ticker := time.NewTicker(hc.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			hc.performHealthChecks()
		}
	}
}

func (hc *HealthChecker) performHealthChecks() {
	// Simplified health checking
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	for edgeID := range hc.checks {
		hc.checks[edgeID].LastCheck = time.Now()
		hc.checks[edgeID].Status = "healthy"
	}
}

func (hc *HealthChecker) GetHealthStats() map[string]interface{} {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	healthy := 0
	total := len(hc.checks)
	
	for _, check := range hc.checks {
		if check.Status == "healthy" {
			healthy++
		}
	}

	return map[string]interface{}{
		"healthy_edges": healthy,
		"total_edges":   total,
		"health_ratio":  float64(healthy) / float64(total),
	}
}

// Helper functions
func (m *Manager) serveCachedAsset(w http.ResponseWriter, r *http.Request, entry *CacheEntry) {
	// Set cache headers
	w.Header().Set("Content-Type", entry.ContentType)
	w.Header().Set("ETag", entry.ETag)
	w.Header().Set("Last-Modified", entry.LastModified.Format(http.TimeFormat))
	w.Header().Set("Cache-Control", fmt.Sprintf("public, max-age=%d", int(m.config.CacheTTL.Seconds())))
	
	// Check if client has cached version
	if r.Header.Get("If-None-Match") == entry.ETag {
		w.WriteHeader(http.StatusNotModified)
		return
	}

	w.Write(entry.Content)
}

func (m *Manager) serveAsset(w http.ResponseWriter, r *http.Request, entry *CacheEntry) {
	m.serveCachedAsset(w, r, entry)
}

func (m *Manager) fetchFromOrigin(path string) ([]byte, string, error) {
	// Simplified origin fetch - in production would use actual HTTP client
	return []byte("mock content"), "text/plain", nil
}

func (m *Manager) generateETag(content []byte) string {
	hash := md5.Sum(content)
	return fmt.Sprintf(`"%x"`, hash)
}

func (m *Manager) getAssetType(path string) string {
	ext := filepath.Ext(path)
	switch ext {
	case ".css":
		return "css"
	case ".js":
		return "javascript"
	case ".png", ".jpg", ".jpeg", ".gif", ".webp":
		return "image"
	default:
		return "other"
	}
}

// Global manager instance
var defaultManager *Manager
var managerOnce sync.Once

func GetDefaultManager() *Manager {
	managerOnce.Do(func() {
		defaultManager = NewManager(nil)
	})
	return defaultManager
}

// Utility functions
func parseJSON(r io.Reader, v interface{}) error {
	// Simplified JSON parsing
	return nil
}

func writeJSON(w http.ResponseWriter, v interface{}) {
	w.Header().Set("Content-Type", "application/json")
	// Simplified JSON writing
	fmt.Fprintf(w, `{"status": "ok"}`)
}

// Metrics initialization
func newCDNMetrics() *CDNMetrics {
	return &CDNMetrics{
		requestsTotal: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cdn_requests_total",
			Help: "Total number of CDN requests",
		}, []string{"asset_type"}),
		cacheHits: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cdn_cache_hits_total",
			Help: "Total number of CDN cache hits",
		}, []string{"asset_type"}),
		cacheMisses: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cdn_cache_misses_total",
			Help: "Total number of CDN cache misses",
		}, []string{"asset_type"}),
		bandwidthSaved: promauto.NewCounter(prometheus.CounterOpts{
			Name: "hopen_cdn_bandwidth_saved_bytes",
			Help: "Total bandwidth saved by CDN",
		}),
		responseTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_response_time_seconds",
			Help:    "CDN response time",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"source"}),
		edgeHealth: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_cdn_edge_health",
			Help: "CDN edge health status",
		}, []string{"edge_id", "region"}),
		cacheSize: promauto.NewGauge(prometheus.GaugeOpts{
			Name: "hopen_cdn_cache_size_bytes",
			Help: "CDN cache size in bytes",
		}),
		optimizationRatio: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_optimization_ratio",
			Help:    "Asset optimization ratio",
			Buckets: []float64{0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0},
		}, []string{"asset_type"}),
	}
}

func newOptimizationMetrics() *OptimizationMetrics {
	return &OptimizationMetrics{
		compressionRatio: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_compression_ratio",
			Help:    "Asset compression ratio",
			Buckets: []float64{0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0},
		}, []string{"asset_type"}),
		processingTime: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_processing_time_seconds",
			Help:    "Asset processing time",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0},
		}, []string{"asset_type"}),
		sizeBefore: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_size_before_bytes",
			Help:    "Asset size before optimization",
			Buckets: []float64{1024, 10240, 102400, 1048576, ********},
		}, []string{"asset_type"}),
		sizeAfter: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_size_after_bytes",
			Help:    "Asset size after optimization",
			Buckets: []float64{1024, 10240, 102400, 1048576, ********},
		}, []string{"asset_type"}),
	}
}

func newDistributionMetrics() *DistributionMetrics {
	return &DistributionMetrics{
		edgeRequests: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cdn_edge_requests_total",
			Help: "Total requests per edge",
		}, []string{"edge_id", "region"}),
		routingLatency: promauto.NewHistogramVec(prometheus.HistogramOpts{
			Name:    "hopen_cdn_routing_latency_seconds",
			Help:    "Geographic routing latency",
			Buckets: []float64{0.001, 0.005, 0.01, 0.025, 0.05, 0.1},
		}, []string{"region"}),
		failoverCount: promauto.NewCounterVec(prometheus.CounterOpts{
			Name: "hopen_cdn_failover_total",
			Help: "Total number of edge failovers",
		}, []string{"from_edge", "to_edge"}),
		edgeCapacity: promauto.NewGaugeVec(prometheus.GaugeOpts{
			Name: "hopen_cdn_edge_capacity",
			Help: "Edge capacity utilization",
		}, []string{"edge_id", "region"}),
	}
}

// DefaultConfig returns default CDN configuration
func DefaultConfig() *Config {
	return &Config{
		EnableCDN:           true,
		EdgeLocations:       []EdgeLocation{},
		CacheTTL:           24 * time.Hour,
		MaxCacheSize:       10 * 1024 * 1024 * 1024, // 10GB
		EnableCompression:   true,
		EnableOptimization:  true,
		EnablePurging:      true,
		CompressionLevel:   6,
		ImageQuality:       85,
		EnableWebP:         true,
		EnableBrotli:       true,
		PurgeAPIKey:        "default-purge-key",
		OriginServers:      []string{"localhost:4000"},
	}
} 