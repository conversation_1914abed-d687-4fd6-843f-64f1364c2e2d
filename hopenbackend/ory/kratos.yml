version: v1.0.0

dsn: ***************************************/kratos?sslmode=disable

serve:
  public:
    base_url: http://*********:4433/
    cors:
      enabled: true
      allowed_origins:
        - http://*********:4000
        - http://localhost:4000
        - http://localhost:3000
        - "*"
      allowed_methods:
        - POST
        - GET
        - PUT
        - PATCH
        - DELETE
      allowed_headers:
        - Authorization
        - Content-Type
        - X-Session-Token
      exposed_headers:
        - Content-Type
        - Set-Cookie
  admin:
    base_url: http://*********:4434/

selfservice:
  default_browser_return_url: http://*********:4000/auth/callback
  allowed_return_urls:
    - http://*********:4000
    - http://localhost:4000
    - http://localhost:3000

  methods:
    password:
      enabled: true
      config:
        haveibeenpwned_enabled: true
        max_breaches: 0
        ignore_network_errors: true
    totp:
      config:
        issuer: Hopen
      enabled: true
    lookup_secret:
      enabled: true
    link:
      enabled: true
    code:
      enabled: true

  flows:
    error:
      ui_url: http://*********:4000/auth/error

    settings:
      ui_url: http://*********:4000/auth/settings
      privileged_session_max_age: 15m
      required_aal: highest_available

    recovery:
      enabled: true
      ui_url: http://*********:4000/auth/recovery
      use: code

    verification:
      enabled: true
      ui_url: http://*********:4000/auth/verification
      use: code
      after:
        default_browser_return_url: http://*********:4000/auth/welcome

    logout:
      after:
        default_browser_return_url: http://*********:4000/auth/login

    login:
      ui_url: http://*********:4000/auth/login
      lifespan: 10m

    registration:
      lifespan: 10m
      ui_url: http://*********:4000/auth/registration
      after:
        password:
          hooks:
            - hook: session
        oidc:
          hooks:
            - hook: session

log:
  level: debug
  format: text
  leak_sensitive_values: true

secrets:
  cookie:
    - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
  cipher:
    - 32-LONG-SECRET-NOT-SECURE-AT-ALL

ciphers:
  algorithm: xchacha20-poly1305

hashers:
  algorithm: bcrypt
  bcrypt:
    cost: 8

identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file:///etc/config/kratos/identity.schema.json

courier:
  smtp:
    connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true

feature_flags:
  cacheable_sessions: false 