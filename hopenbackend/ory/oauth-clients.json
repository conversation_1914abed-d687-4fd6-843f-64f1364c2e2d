[{"client_id": "hopen-google-oauth", "client_name": "Hopen Google OAuth", "client_secret": "hopen-google-secret-change-in-production", "redirect_uris": ["http://localhost:4000/auth/callback/google", "https://hopenapp.com/auth/callback/google"], "grant_types": ["authorization_code", "refresh_token"], "response_types": ["code"], "scope": "openid profile email", "token_endpoint_auth_method": "client_secret_basic", "userinfo_signed_response_alg": "none", "subject_type": "public"}, {"client_id": "hopen-apple-oauth", "client_name": "<PERSON>n <PERSON>uth", "client_secret": "hopen-apple-secret-change-in-production", "redirect_uris": ["http://localhost:4000/auth/callback/apple", "https://hopenapp.com/auth/callback/apple"], "grant_types": ["authorization_code", "refresh_token"], "response_types": ["code"], "scope": "openid profile email", "token_endpoint_auth_method": "client_secret_basic", "userinfo_signed_response_alg": "none", "subject_type": "public"}, {"client_id": "hopen-flutter-app", "client_name": "Hopen Flutter Mobile App", "client_secret": "hopen-flutter-secret-change-in-production", "redirect_uris": ["com.hopen.app://auth/callback", "http://localhost:4000/auth/callback/mobile"], "grant_types": ["authorization_code", "refresh_token", "client_credentials"], "response_types": ["code"], "scope": "openid profile email offline_access", "token_endpoint_auth_method": "client_secret_basic", "userinfo_signed_response_alg": "none", "subject_type": "public"}]