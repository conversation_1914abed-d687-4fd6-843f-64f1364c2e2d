{"$id": "https://schemas.ory.sh/presets/kratos/quickstart/email-password/identity.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON><PERSON>r", "type": "object", "properties": {"traits": {"type": "object", "properties": {"email": {"type": "string", "format": "email", "title": "E-Mail", "minLength": 3, "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}, "verification": {"via": "email"}, "recovery": {"via": "email"}}}, "first_name": {"type": "string", "title": "First Name", "minLength": 1, "maxLength": 50}, "last_name": {"type": "string", "title": "Last Name", "minLength": 1, "maxLength": 50}, "username": {"type": "string", "title": "Username", "minLength": 3, "maxLength": 30, "pattern": "^[a-zA-Z0-9_]+$"}, "avatar_url": {"type": "string", "format": "uri", "title": "Avatar URL"}, "phone": {"type": "string", "title": "Phone Number", "pattern": "^\\+[1-9]\\d{1,14}$"}, "date_of_birth": {"type": "string", "format": "date", "title": "Date of Birth"}, "bio": {"type": "string", "title": "Bio", "maxLength": 500}, "location": {"type": "string", "title": "Location", "maxLength": 100}, "website": {"type": "string", "format": "uri", "title": "Website"}, "hasCompletedOnboarding": {"type": "boolean", "title": "Has Completed Onboarding", "default": false}}, "required": ["email", "first_name", "last_name", "username"], "additionalProperties": false}}}