version: v2.2.0

dsn: ***************************************/hydra?sslmode=disable

serve:
  cookies:
    same_site_mode: Lax
  public:
    host: 0.0.0.0
    port: 4444
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:4000
        - http://localhost:3000
        - "*"
      allowed_methods:
        - POST
        - GET
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      allowed_headers:
        - Authorization
        - Content-Type
        - Accept
        - Origin
        - X-Requested-With
      exposed_headers:
        - Content-Type
        - Cache-Control
        - Set-Cookie
  admin:
    host: 0.0.0.0
    port: 4445

urls:
  self:
    issuer: http://localhost:4444/
    public: http://localhost:4444/
  consent: http://localhost:4000/auth/consent
  login: http://localhost:4000/auth/login
  logout: http://localhost:4000/auth/logout
  error: http://localhost:4000/auth/error
  post_logout_redirect: http://localhost:4000/

strategies:
  access_token: jwt

ttl:
  login_consent_request: 1h
  access_token: 1h
  refresh_token: 720h
  id_token: 1h
  auth_code: 10m

oauth2:
  session:
    encrypt_at_rest: true
  pkce:
    enforced: false
    enforced_for_public_clients: true
  expose_internal_errors: true
  hashers:
    bcrypt:
      cost: 10

secrets:
  system:
    - this-is-the-primary-secret-replace-it-in-production
  cookie:
    - this-is-the-secondary-secret-replace-it-in-production

log:
  level: debug
  format: json 