78[0J[1000D[2K[36m  ⠋ Building Encore application graph... [0m
8[0J[1000D[2K[36m  ⠙ Building Encore application graph... [0m
[1000D[2K[36m  ⠋ Analyzing service topology... [0m
8[0J[1000D[2K[36m  ⠚ Building Encore application graph... [0m
[1000D[2K[36m  ⠙ Analyzing service topology... [0m
8[0J[1000D[2K[36m  ⠒ Building Encore application graph... [0m
[1000D[2K[36m  ⠚ Analyzing service topology... [0m
8[0J[1000D[2K[36m  ⠂ Building Encore application graph... [0m
[1000D[2K[36m  ⠒ Analyzing service topology... [0m
8[0J[1000D[2K[36m  ⠂ Building Encore application graph... [0m
[1000D[2K[36m  ⠂ Analyzing service topology... [0m
8[0J[1000D[2K[36m  ⠒ Building Encore application graph... [0m
[1000D[2K[36m  ⠂ Analyzing service topology... [0m
[1000D[2K[36m  ⠋ Creating PostgreSQL database cluster... [0m
8[0J[1000D[2K[36m  ⠲ Building Encore application graph... [0m
[1000D[2K[36m  ⠒ Analyzing service topology... [0m
[1000D[2K[36m  ⠙ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠋ Starting PubSub daemon... [0m
8[0J[1000D[2K[36m  ⠴ Building Encore application graph... [0m
[1000D[2K[36m  ⠲ Analyzing service topology... [0m
[1000D[2K[36m  ⠚ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠙ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠋ Starting Redis server... [0m
8[0J[1000D[2K[36m  ⠦ Building Encore application graph... [0m
[1000D[2K[36m  ⠴ Analyzing service topology... [0m
[1000D[2K[36m  ⠒ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠚ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠙ Starting Redis server... [0m
[1000D[2K[36m  ⠋ Fetching application secrets... [0m
8[0J[1000D[2K[36m  ⠖ Building Encore application graph... [0m
[1000D[2K[36m  ⠦ Analyzing service topology... [0m
[1000D[2K[36m  ⠂ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠒ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠚ Starting Redis server... [0m
[1000D[2K[36m  ⠙ Fetching application secrets... [0m
[1000D[2K[36m  ⠋ Generating boilerplate code... [0m
8[0J[1000D[2K[36m  ⠒ Building Encore application graph... [0m
[1000D[2K[36m  ⠖ Analyzing service topology... [0m
[1000D[2K[36m  ⠂ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠂ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠒ Starting Redis server... [0m
[1000D[2K[36m  ⠚ Fetching application secrets... [0m
[1000D[2K[36m  ⠙ Generating boilerplate code... [0m
8[0J[1000D[2K[36m  ⠐ Building Encore application graph... [0m
[1000D[2K[36m  ⠒ Analyzing service topology... [0m
[1000D[2K[36m  ⠒ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠂ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠂ Starting Redis server... [0m
[1000D[2K[36m  ⠒ Fetching application secrets... [0m
[1000D[2K[36m  ⠚ Generating boilerplate code... [0m
8[0J[1000D[2K[36m  ⠐ Building Encore application graph... [0m
[1000D[2K[36m  ⠐ Analyzing service topology... [0m
[1000D[2K[36m  ⠲ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠒ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠂ Starting Redis server... [0m
[1000D[2K[36m  ⠂ Fetching application secrets... [0m
[1000D[2K[36m  ⠒ Generating boilerplate code... [0m
8[0J[1000D[2K[36m  ⠒ Building Encore application graph... [0m
[1000D[2K[36m  ⠐ Analyzing service topology... [0m
[1000D[2K[36m  ⠴ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠲ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠒ Starting Redis server... [0m
[1000D[2K[36m  ⠂ Fetching application secrets... [0m
[1000D[2K[36m  ⠂ Generating boilerplate code... [0m
8[0J[1000D[2K[36m  ⠓ Building Encore application graph... [0m
[1000D[2K[36m  ⠒ Analyzing service topology... [0m
[1000D[2K[36m  ⠦ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠴ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠲ Starting Redis server... [0m
[1000D[2K[36m  ⠒ Fetching application secrets... [0m
[1000D[2K[36m  ⠂ Generating boilerplate code... [0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
8[0J[1000D[2K[36m  ⠋ Building Encore application graph... [0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[36m  ⠖ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠦ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠴ Starting Redis server... [0m
[1000D[2K[36m  ⠲ Fetching application secrets... [0m
[1000D[2K[36m  ⠒ Generating boilerplate code... [0m
[1000D[2K[36m  ⠙ Compiling application source code... [0m
8[0J[1000D[2K[36m  ⠋ Building Encore application graph... [0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[36m  ⠒ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠖ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠦ Starting Redis server... [0m
[1000D[2K[36m  ⠴ Fetching application secrets... [0m
[1000D[2K[36m  ⠲ Generating boilerplate code... [0m
[1000D[2K[36m  ⠚ Compiling application source code... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[36m  ⠐ Creating PostgreSQL database cluster... [0m
[1000D[2K[36m  ⠒ Starting PubSub daemon... [0m
[1000D[2K[36m  ⠖ Starting Redis server... [0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠴ Generating boilerplate code... [0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[36m  ⠐ Creating PostgreSQL database cluster... [0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠦ Generating boilerplate code... [0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[36m  ⠒ Creating PostgreSQL database cluster... [0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠖ Generating boilerplate code... [0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
[1000D[2K[36m  ⠋ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠒ Generating boilerplate code... [0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[36m  ⠙ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠐ Generating boilerplate code... [0m
[1000D[2K[36m  ⠲ Compiling application source code... [0m
[1000D[2K[36m  ⠚ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[36m  ⠐ Generating boilerplate code... [0m
[1000D[2K[36m  ⠴ Compiling application source code... [0m
[1000D[2K[36m  ⠒ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠦ Compiling application source code... [0m
[1000D[2K[36m  ⠂ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠖ Compiling application source code... [0m
[1000D[2K[36m  ⠂ Running database migrations... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠓ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠙ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠚ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠲ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠴ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠦ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠖ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠓ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠙ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠚ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠂ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠲ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠴ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠦ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠖ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠐ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠒ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠓ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠋ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[36m  ⠙ Compiling application source code... [0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[32m  ✔ Compiling application source code... Done![0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[32m  ✔ Compiling application source code... Done![0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
[1000D[2K[36m  ⠋ Starting Encore application... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[32m  ✔ Compiling application source code... Done![0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
[1000D[2K[36m  ⠙ Starting Encore application... [0m
8[0J[1000D[2K[32m  ✔ Building Encore application graph... Done![0m
[1000D[2K[32m  ✔ Analyzing service topology... Done![0m
[1000D[2K[32m  ✔ Creating PostgreSQL database cluster... Done![0m
[1000D[2K[32m  ✔ Starting PubSub daemon... Done![0m
[1000D[2K[32m  ✔ Starting Redis server... Done![0m
[1000D[2K[32m  ✔ Fetching application secrets... Done![0m
[1000D[2K[32m  ✔ Generating boilerplate code... Done![0m
[1000D[2K[32m  ✔ Compiling application source code... Done![0m
[1000D[2K[32m  ✔ Running database migrations... Done![0m
[1000D[2K[32m  ✔ Starting Encore application... Done![0m

  Encore development server running!

  Your API is running at:     [36mhttp://127.0.0.1:4000[0m
  Development Dashboard URL:  [36mhttp://127.0.0.1:9400/7ua2q[0m
  MCP SSE URL:                [36mhttp://127.0.0.1:9900/sse?appID=7ua2q[0m

3:44PM INF registered subscription service=friendship subscription=friendship-create-from-event-sub topic=mutual-friendship
3:44PM INF registered 57 API endpoints
3:44PM INF listening for incoming HTTP requests
3:44PM INF connected to MQTT broker

[31mwarning: secrets not defined: AppleClientID, AppleClientSecret, GoogleClientID, HydraAdminURL, HydraJWKSURL, HydraPublicURL, KratosAdminURL, KratosPublicURL[0m
[38;5;248mnote: undefined secrets are left empty for local development only.
see https://encore.dev/docs/primitives/secrets for more information[0m

3:44PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/
3:44PM INF starting request endpoint=Health service=auth trace_id=libhqoc89c7sackh738ki86o7c
3:44PM INF request completed code=ok duration=0.215 endpoint=Health service=auth trace_id=libhqoc89c7sackh738ki86o7c
3:44PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/user/health
3:44PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/
3:45PM INF starting request endpoint=Health service=auth trace_id=ogqm698g1sn90og1m4g1e0t2mo
3:45PM INF request completed code=ok duration=0.043667 endpoint=Health service=auth trace_id=ogqm698g1sn90og1m4g1e0t2mo
3:45PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/notification/health
3:45PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/friendship/requests
3:45PM INF starting request endpoint=SendRequest service=friendship trace_id=9tg1pcglfrrkoctu1jeoaau7mo
3:45PM ERR request failed error=unauthorized code=unknown endpoint=SendRequest service=friendship trace_id=9tg1pcglfrrkoctu1jeoaau7mo
3:45PM INF request completed code=unknown duration=0.073667 endpoint=SendRequest service=friendship trace_id=9tg1pcglfrrkoctu1jeoaau7mo
3:45PM TRC endpoint not found gateway=true hosting=["api_gateway","auth","bubble","friendship","gateway","idempotency","middleware","mqtt","notification","realtime","tracing","user"] path=/api_gateway/graphql
3:46PM INF starting request endpoint=Health service=auth trace_id=s007bp1vsqltaauetut99nju5s
3:46PM INF request completed code=ok duration=0.037291 endpoint=Health service=auth trace_id=s007bp1vsqltaauetut99nju5s
