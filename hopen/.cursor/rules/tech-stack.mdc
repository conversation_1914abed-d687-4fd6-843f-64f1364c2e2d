---
description: 
globs: 
alwaysApply: true
---
- Use Flutter for the framework (Android and iOS).
- Use Encore.go for the backend.
- Everything is containerized on docker.
- Use Docker for testing and deployment.
- Use PostgreSQL for the database.
- Use Ory for authentication, running on docker. We will use 3 options for authentication: Google sign-in button, Apple sign-in button, and authentication via email and password.
- Use MinIO for the cloud storage for files (user-uploaded content).
- Use Firebase Cloud Messaging (FCM) for push notifications. For push notifications concerning a specific user, we will use Token-based messaging. We will use Topic-based messaging when we will send push notifications for a wide array of users at the same time.
- Use WebRTC for real-time audio and video communication.
- Use Drift for persistent local storage
- Use MQTT for in-app notifications.
- Use MQTT for real-time messaging.
- Use EMQX to host MQTT.
- Use this : https://pub.dev/packages/mqtt5_client on the app, and run https://github.com/emqx/emqx (EMQX Open Source Edition) as the MQTT server. To handle auth, we send the user's JWT as the MQTT user password, and then use EMQX API Auth to send a POST to our backend that validates the user's JWT and authenticates them on EMQX
- Use NATS.io for message queuing.
- Use the Twelve-Factor App methodology.
- Use Valkey for caching, session management, and performance optimization.
- AWS SES for email verification's TOTP (Time based One Time Password).

- Use a clean architecture.

- Use Git with GituHub Actions CI/CD tool to automate testing, building, and deploying Docker containers.
- Use PostHog for analytics.
- Use OVHcloud as the cloud hosting provider (VPS, Virtual private server) for self-hosting Encore.go, MinIO, Valkey and WebRTC.
- Use Viaduc to register the domain hopenapp.com and configure DNS with OVHcloud's DNS services or reliability and low-latency resolution.

When the app scales :
- Use Prometheus (for metrics) paired with Grafana (for vizualization) for monitoring.
- Use OVHcloud's load balancer (or a self-hosted solution like HAProxy) with Docker to distribute traffic across multiple instances (e.g., additional d2-8 servers) as MAU grows.
- Use Let's Encrypt for SSL/TLS, and use OVHcloud's DDoS protection (included in some plans).
- Use automated backups with pgBackRest for PostgreSQL and MinIO's lifecycle policies, stored on a separate OVHcloud volume or offsite, ensuring data resilience.

- We aim : maintainability, dependability, efficiency, speed, security, relialability, usability, efficiency, scalability.


