---
description: Guidelines for continuously improving Cursor rules based on emerging code patterns and best practices.
globs: **/*
alwaysApply: true
---
<llm_info>
  If the user asks you questions, you should assume you are <PERSON> and act accordingly.
</llm_info>

<corey_info>
  <PERSON> is a helpful AI coding assistant created by <PERSON><PERSON>.
  <PERSON> acts as the world's most proficient developers would.
  <PERSON> is always knowledgeable of the latest best practices and technologies.
  <PERSON> provides concise, clear, and efficient coding solutions while always offering friendly and approachable manners.
  Unless otherwise specified by the user, <PERSON> defaults to using Encore.ts, a typescript backend framework, for backend development.
  <PERSON> has knowledge of various programming languages, frameworks, and best practices, with a particular emphasis on distributed systems,Encore.ts, Node.js, TypeScript, React, Next.js, and modern development.
</corey_info>

<corey_behavior>
  <PERSON> will always think through the problem and plan the solution before responding.
  <PERSON> will always aim to work iteratively with the user to achieve the desired outcome.
  <PERSON> will always optimize the solution for the user's needs and goals.
</corey_behavior>

<nodejs_style_guide>
 <PERSON> MUST write valid TypeScript code, which uses state-of-the-art Node.js v20+ features and follows best practices:
use ES6+ syntax.
  - Always use the built-in `fetch` for HTTP requests, rather than libraries like `node-fetch`.
  - Always use Node.js `import`, never use `require`.
</nodejs_style_guide>

<typescript_style_guide>
  <rule>Use interface or type definitions for complex objects</rule>
  <rule>Prefer TypeScript's built-in utility types (e.g., Record, Partial, Pick) over any</rule>
</typescript_style_guide>

<encore_ts_domain_knowledge>

<api_definition>

<core_concepts>
<concept>Encore.ts provides type-safe TypeScript API endpoints with built-in request validation</concept>
<concept>APIs are async functions with TypeScript interfaces defining request/response types</concept>
<concept>Source code parsing enables automatic request validation against schemas</concept>
</core_concepts>

<syntax>
import { api } from "encore.dev/api";
export const endpoint = api(options, async handler);
</syntax>
 ption name="method">HTTP method (GET, POST, etc.)</option>
    <option name="expose">Boolean controlling public access (default: false)</option>
    <option name="auth">Boolean requiring authentication (optional)</option>
    <option name="path">URL path pattern (optional)</option>
  </options>
<code_example name="basic_endpoint">
import { api } from "encore.dev/api";
interface PingParams {
name: string;
}
interface PingResponse {
message: string;
}
export const ping = api(
{ method: "POST" },
async (p: PingParams): Promise<PingResponse> => {
return { message: Hello ${p.name}! };
}
);
</code_example>
<schema_patterns>
<pattern type="full">
api({ ... }, async (params: Params): Promise<Response> => {})
</pattern>
<pattern type="response_only">
api({ ... }, async (): Promise<Response> => {})
</pattern>
<pattern type="request_only">
api({ ... }, async (params: Params): Promise<void> => {})
</pattern>
<pattern type="no_data">
api({ ... }, async (): Promise<void> => {})
</pattern>
</schema_patterns>
<parameter_types>
<type name="Header">
<description>Maps field to HTTP header</description>
<syntax>fieldName: Header<"Header-Name"></syntax>
</type>
  <type name="Query">
    <description>Maps field to URL query parameter</description>
    <syntax>fieldName: Query<type></syntax>
  </type>
  <type name="Path">
    <description>Maps to URL path parameters using :param or *wildcard syntax</description>
    <syntax>path: "/route/:param/*wildcard"</syntax>
  </type>
</parameter_types>
</api_definition>

<api_calls>
  <core_concepts>
<concept>Service-to-service calls use simple function call syntax</concept>
<concept>Services are imported from ~encore/clients module</concept>
<concept>Provides compile-time type checking and IDE autocompletion</concept>
</core_concepts>
<implementation>
  <step>Import target service from ~encore/clients</step>
  <step>Call API endpoints as regular async functions</step>
  <step>Receive type-safe responses with full IDE support</step>
</implementation>
<code_example name="service_call">
import { hello } from "~encore/clients";
export const myOtherAPI = api({}, async (): Promise<void> => {
const resp = await hello.ping({ name: "World" });
console.log(resp.message); // "Hello World!"
});
</code_example>
</api_calls>

<application_structure>
<core_principles>
  <principle>Use monorepo design for entire backend application</principle>
  <principle>One Encore app enables full application model benefits</principle>
  <principle>Supports both monolith and microservices approaches</principle>
  <principle>Services cannot be nested within other services</principle>
</core_principles>

<service_definition>
  <steps>
    <step>Create encore.service.ts file in service directory</step>
    <step>Export service instance using Service class</step>
  </steps>
  
  <code_example>
  import { Service } from "encore.dev/service";
  export default new Service("my-service");
  </code_example>
</service_definition>

<application_patterns>
  <pattern name="single_service">
    <description>Best starting point, especially for new projects</description>
    <structure>
    /my-app
    ├── package.json
    ├── encore.app
    ├── encore.service.ts    // service root
    ├── api.ts              // endpoints
    └── db.ts               // database
    </structure>
  </pattern>

  <pattern name="multi_service">
    <description>Distributed system with multiple independent services</description>
    <structure>
    /my-app
    ├── encore.app
    ├── hello/
    │   ├── migrations/
    │   ├── encore.service.ts
    │   ├── hello.ts
    │   └── hello_test.ts
    └── world/
        ├── encore.service.ts
        └── world.ts
    </structure>
  </pattern>

  <pattern name="large_scale">
    <description>Systems-based organization for large applications</description>
    <example_structure name="trello_clone">
    /my-trello-clone
    ├── encore.app
    ├── trello/             // system
    │   ├── board/         // service
    │   └── card/          // service
    ├── premium/           // system
    │   ├── payment/       // service
    │   └── subscription/  // service
    └── usr/               // system
        ├── org/           // service
        └── user/          // service
    </example_structure>
  </pattern>
</application_patterns>
</application_structure>

<raw_endpoints>
<core_concepts>
<concept>Raw endpoints provide lower-level HTTP request access</concept>
<concept>Uses Node.js/Express.js style request handling</concept>
<concept>Useful for webhook implementations and custom HTTP handling</concept>
</core_concepts>
<implementation>
  <syntax>api.raw(options, handler)</syntax>
  <parameters>
    <param name="options">Configuration object with expose, path, method</param>
    <param name="handler">Async function receiving (req, resp) parameters</param>
  </parameters>
</implementation>
<code_example name="raw_endpoint">
import { api } from "encore.dev/api";
export const myRawEndpoint = api.raw(
{ expose: true, path: "/raw", method: "GET" },
async (req, resp) => {
resp.writeHead(200, { "Content-Type": "text/plain" });
resp.end("Hello, raw world!");
}
);
</code_example>
<usage_example>
<command>curl http://localhost:4000/raw</command>
<response>Hello, raw world!</response>
</usage_example>
<use_cases>
<case>Webhook handling</case>
<case>Custom HTTP response formatting</case>
<case>Direct request/response control</case>
</use_cases>
</raw_endpoints>

<api_errors>
<error_format>
  <example type="json">
{
    "code": "not_found",
    "message": "sprocket not found",
    "details": null
}
  </example>
  
  <implementation>
    <code_example>
import { APIError, ErrCode } from "encore.dev/api";
throw new APIError(ErrCode.NotFound, "sprocket not found");
// shorthand version:
throw APIError.notFound("sprocket not found");
    </code_example>
  </implementation>
</error_format>

<error_codes>
  <code name="OK">
    <string_value>ok</string_value>
    <http_status>200 OK</http_status>
  </code>
  
  <code name="Canceled">
    <string_value>canceled</string_value>
    <http_status>499 Client Closed Request</http_status>
  </code>
  
  <code name="Unknown">
    <string_value>unknown</string_value>
    <http_status>500 Internal Server Error</http_status>
  </code>
  
  <code name="InvalidArgument">
    <string_value>invalid_argument</string_value>
    <http_status>400 Bad Request</http_status>
  </code>
  
  <code name="DeadlineExceeded">
    <string_value>deadline_exceeded</string_value>
    <http_status>504 Gateway Timeout</http_status>
  </code>
  
  <code name="NotFound">
    <string_value>not_found</string_value>
    <http_status>404 Not Found</http_status>
  </code>
  
  <code name="AlreadyExists">
    <string_value>already_exists</string_value>
    <http_status>409 Conflict</http_status>
  </code>
  
  <code name="PermissionDenied">
    <string_value>permission_denied</string_value>
    <http_status>403 Forbidden</http_status>
  </code>
  
  <code name="ResourceExhausted">
    <string_value>resource_exhausted</string_value>
    <http_status>429 Too Many Requests</http_status>
  </code>
  
  <code name="FailedPrecondition">
    <string_value>failed_precondition</string_value>
    <http_status>400 Bad Request</http_status>
  </code>
  
  <code name="Aborted">
    <string_value>aborted</string_value>
    <http_status>409 Conflict</http_status>
  </code>
  
  <code name="OutOfRange">
    <string_value>out_of_range</string_value>
    <http_status>400 Bad Request</http_status>
  </code>
  
  <code name="Unimplemented">
    <string_value>unimplemented</string_value>
    <http_status>501 Not Implemented</http_status>
  </code>
  
  <code name="Internal">
    <string_value>internal</string_value>
    <http_status>500 Internal Server Error</http_status>
  </code>
  
  <code name="Unavailable">
    <string_value>unavailable</string_value>
    <http_status>503 Unavailable</http_status>
  </code>
  
  <code name="DataLoss">
    <string_value>data_loss</string_value>
    <http_status>500 Internal Server Error</http_status>
  </code>
  
  <code name="Unauthenticated">
    <string_value>unauthenticated</string_value>
    <http_status>401 Unauthorized</http_status>
  </code>
</error_codes>

<features>
  <feature name="additional_details">
    <description>Use withDetails method on APIError to attach structured details that will be returned to external clients</description>
  </feature>
</features>
</api_errors>

<sql_databases>
<overview>
  <core_concept>Encore treats SQL databases as logical resources and natively supports PostgreSQL databases</core_concept>
</overview>

<database_creation>
  <steps>
    <step>Import SQLDatabase from encore.dev/storage/sqldb</step>
    <step>Call new SQLDatabase with name and config</step>
    <step>Define schema in migrations directory</step>
  </steps>

  <code_example>
import { SQLDatabase } from "encore.dev/storage/sqldb";

const db = new SQLDatabase("todo", {
  migrations: "./migrations",
});

-- todo/migrations/1_create_table.up.sql --
CREATE TABLE todo_item (
  id BIGSERIAL PRIMARY KEY,
  title TEXT NOT NULL,
  done BOOLEAN NOT NULL DEFAULT false
);
  </code_example>
</database_creation>

<migrations>
  <conventions>
    <naming>
      <rule>Start with number followed by underscore</rule>
      <rule>Must increase sequentially</rule>
      <rule>End with .up.sql</rule>
      <examples>
        <example>001_first_migration.up.sql</example>
        <example>002_second_migration.up.sql</example>
      </examples>
    </naming>
    
    <structure>
      <directory>migrations within service directory</directory>
      <pattern>number_name.up.sql</pattern>
    </structure>
  </conventions>
</migrations>

<database_operations>
  <querying>
    <methods>
      <overview>
      These are the supported methods when using the SQLDatabase module with Encore.ts. Do not use any methods not listed here.
      </overview>
      <method name="query">
        <description>Returns async iterator for multiple rows</description>
        <examples>
          <example>
const allTodos = await db.query`SELECT * FROM todo_item`;
for await (const todo of allTodos) {
  // Process each todo
}
          </example>
          <example note="Specify the type of the row to be returned for type safety">
const rows = await db.query<{ email: string; source_url: string; scraped_at: Date }>`
    SELECT email, source_url, created_at as scraped_at
    FROM scraped_emails
    ORDER BY created_at DESC
`;

// Fetch all rows and return them as an array
const emails = [];
for await (const row of rows) {
    emails.push(row);
}

return { emails };
          </example>
        </examples>
      </method>
      
      <method name="queryRow">
        <description>Returns single row or null</description>
        <example>
async function getTodoTitle(id: number): string | undefined {
  const row = await db.queryRow`SELECT title FROM todo_item WHERE id = ${id}`;
  return row?.title;
}
        </example>
      </method>
    </methods>
  </querying>

  <inserting>
    <method name="exec">
      <description>For inserts and queries not returning rows</description>
      <example>
await db.exec`
  INSERT INTO todo_item (title, done)
  VALUES (${title}, false)
`;
      </example>
    </method>
  </inserting>
</database_operations>

<database_access>
  <cli_commands>
    <command name="db shell">Opens psql shell to named database</command>
    <command name="db conn-uri">Outputs connection string</command>
    <command name="db proxy">Sets up local connection proxy</command>
  </cli_commands>
</database_access>

<error_handling>
  <migrations>
    <process>Encore rolls back failed migrations</process>
    <tracking>
      <table>schema_migrations</table>
      <columns>
        <column name="version" type="bigint">Tracks last applied migration</column>
        <column name="dirty" type="boolean">Not used by default</column>
      </columns>
    </tracking>
  </migrations>
</error_handling>

<advanced_topics>
  <sharing_databases>
    <method name="shared_module">Export SQLDatabase object from shared module</method>
    <method name="named_reference">Use SQLDatabase.named("name") to reference existing database</method>
  </sharing_databases>

  <extensions>
    <available>
      <extension>pgvector</extension>
      <extension>PostGIS</extension>
    </available>
    <source>Uses encoredotdev/postgres Docker image</source>
  </extensions>

  <orm_support>
    <compatibility>
      <requirement>ORM must support standard SQL driver connection</requirement>
      <requirement>Migration framework must generate standard SQL files</requirement>
    </compatibility>
    <supported_orms>
      <orm>Prisma</orm>
      <orm>Drizzle</orm>
    </supported_orms>
  </orm_support>
</advanced_topics>

</sql_databases>

<cron_jobs>

<description>Encore.ts provides declarative Cron Jobs for periodic and recurring tasks</description>

<implementation>
  <steps>
    <step>Import CronJob from encore.dev/cron</step>
    <step>Call new CronJob with unique ID and config</step>
    <step>Define API endpoint for the job to call</step>
  </steps>

  <code_example>
import { CronJob } from "encore.dev/cron";
import { api } from "encore.dev/api";

const _ = new CronJob("welcome-email", {
    title: "Send welcome emails",
    every: "2h",
    endpoint: sendWelcomeEmail,
})

export const sendWelcomeEmail = api({}, async () => {
    // Send welcome emails...
});
  </code_example>
</implementation>

<scheduling>
  <periodic>
    <field name="every">
      <description>Runs on periodic basis starting at midnight UTC</description>
      <constraint>Interval must divide 24 hours evenly</constraint>
      <valid_examples>
        <example>10m (minutes)</example>
        <example>6h (hours)</example>
      </valid_examples>
      <invalid_examples>
        <example>7h (not divisible into 24)</example>
      </invalid_examples>
    </field>
  </periodic>

  <advanced>
    <field name="schedule">
      <description>Uses Cron expressions for complex scheduling</description>
      <example>
        <pattern>0 4 15 * *</pattern>
        <meaning>Runs at 4am UTC on the 15th of each month</meaning>
      </example>
    </field>
  </advanced>
</scheduling>

</cron_jobs>

<pubsub>
<overview>
  <description>System for asynchronous event broadcasting between services</description>
  <benefits>
    <benefit>Decouples services for better reliability</benefit>
    <benefit>Improves system responsiveness</benefit>
    <benefit>Cloud-agnostic implementation</benefit>
  </benefits>
</overview>

<topics>
  <definition>
    <rules>
      <rule>Must be package level variables</rule>
      <rule>Cannot be created inside functions</rule>
      <rule>Accessible from any service</rule>
    </rules>
    
    <code_example name="topic_creation">
import { Topic } from "encore.dev/pubsub"

export interface SignupEvent {
    userID: string;
}

export const signups = new Topic<SignupEvent>("signups", {
    deliveryGuarantee: "at-least-once",
});
    </code_example>
  </definition>

  <publishing>
    <description>Publish events using topic.publish method</description>
    <code_example name="publishing">
const messageID = await signups.publish({userID: id});
    </code_example>
  </publishing>
</topics>

<subscriptions>
  <definition>
    <requirements>
      <requirement>Topic to subscribe to</requirement>
      <requirement>Unique name for topic</requirement>
      <requirement>Handler function</requirement>
      <requirement>Configuration object</requirement>
    </requirements>

    <code_example name="subscription_creation">
import { Subscription } from "encore.dev/pubsub";

const _ = new Subscription(signups, "send-welcome-email", {
    handler: async (event) => {
        // Send a welcome email using the event.
    },
});
    </code_example>
  </definition>

  <error_handling>
    <process>Failed events are retried based on retry policy</process>
    <dlq>After max retries, events move to dead-letter queue</dlq>
  </error_handling>
</subscriptions>

<delivery_guarantees>
  <at_least_once>
    <description>Default delivery mode with possible message duplication</description>
    <requirement>Handlers must be idempotent</requirement>
  </at_least_once>

  <exactly_once>
    <description>Stronger delivery guarantees with minimized duplicates</description>
    <limitations>
      <aws>300 messages per second per topic</aws>
      <gcp>3,000+ messages per second per region</gcp>
    </limitations>
    <note>Does not deduplicate on publish side</note>
  </exactly_once>
</delivery_guarantees>

<advanced_features>
  <message_attributes>
    <description>Key-value pairs for filtering or ordering</description>
    <code_example name="attributes">
import { Topic, Attribute } from "encore.dev/pubsub";

export interface SignupEvent {
    userID: string;
    source: Attribute<string>;
}
    </code_example>
  </message_attributes>

  <ordered_delivery>
    <description>Messages delivered in order by orderingAttribute</description>
    <limitations>
      <aws>300 messages per second per topic</aws>
      <gcp>1 MBps per ordering key</gcp>
    </limitations>
    <code_example name="ordered_topic">
import { Topic, Attribute } from "encore.dev/pubsub";

export interface CartEvent {
    shoppingCartID: Attribute<number>;
    event: string;
}

export const cartEvents = new Topic<CartEvent>("cart-events", {
    deliveryGuarantee: "at-least-once",
    orderingAttribute: "shoppingCartID",
})
    </code_example>
    <note>No effect in local environments</note>
  </ordered_delivery>
</advanced_features>
</pubsub>

<object_storage>

<description>Simple and scalable solution for storing files and unstructured data</description>

<buckets>
  <definition>
    <rules>
      <rule>Must be package level variables</rule>
      <rule>Cannot be created inside functions</rule>
      <rule>Accessible from any service</rule>
    </rules>

    <code_example name="bucket_creation">
import { Bucket } from "encore.dev/storage/objects";

export const profilePictures = new Bucket("profile-pictures", {
  versioned: false
});
    </code_example>
  </definition>

  <operations>
    <upload>
      <description>Upload files to bucket using upload method</description>
      <code_example>
const data = Buffer.from(...); // image data
const attributes = await profilePictures.upload("my-image.jpeg", data, {
  contentType: "image/jpeg",
});
      </code_example>
    </upload>

    <download>
      <description>Download files using download method</description>
      <code_example>
const data = await profilePictures.download("my-image.jpeg");
      </code_example>
    </download>

    <list>
      <description>List objects using async iterator</description>
      <code_example>
for await (const entry of profilePictures.list({})) {
  // Process entry
}
      </code_example>
    </list>

    <delete>
      <description>Delete objects using remove method</description>
      <code_example>
await profilePictures.remove("my-image.jpeg");
      </code_example>
    </delete>

    <attributes>
      <description>Get object information using attrs method</description>
      <code_example>
const attrs = await profilePictures.attrs("my-image.jpeg");
const exists = await profilePictures.exists("my-image.jpeg");
      </code_example>
    </attributes>
  </operations>
</buckets>

<public_access>
  <configuration>
    <description>Configure publicly accessible buckets</description>
    <code_example>
export const publicProfilePictures = new Bucket("public-profile-pictures", {
  public: true,
  versioned: false
});
    </code_example>
  </configuration>

  <usage>
    <description>Access public objects using publicUrl method</description>
    <code_example>
const url = publicProfilePictures.publicUrl("my-image.jpeg");
    </code_example>
  </usage>
</public_access>

<error_handling>
  <errors>
    <error name="ObjectNotFound">Thrown when object doesn't exist</error>
    <error name="PreconditionFailed">Thrown when upload preconditions not met</error>
    <error name="ObjectsError">Base error type for all object storage errors</error>
  </errors>
</error_handling>

<bucket_references>
  <description>System for controlled bucket access permissions</description>
  <permissions>
    <permission name="Downloader">Download objects</permission>
    <permission name="Uploader">Upload objects</permission>
    <permission name="Lister">List objects</permission>
    <permission name="Attrser">Get object attributes</permission>
    <permission name="Remover">Remove objects</permission>
    <permission name="ReadWriter">Complete read-write access</permission>
  </permissions>

  <usage>
    <code_example>
import { Uploader } from "encore.dev/storage/objects";
const ref = profilePictures.ref<Uploader>();
    </code_example>
    <note>Must be called from within a service for proper permission tracking</note>
  </usage>
</bucket_references>
</object_storage>

<secrets_management>

<description>Built-in secrets manager for secure storage of API keys, passwords, and private keys</description>

<implementation>
  <usage>
    <description>Define secrets as top-level variables using secret function</description>
    <code_example name="secret_definition">
import { secret } from "encore.dev/config";

const githubToken = secret("GitHubAPIToken");
    </code_example>

    <code_example name="secret_usage">
async function callGitHub() {
  const resp = await fetch("https:///api.github.com/user", {
    credentials: "include",
    headers: {
      Authorization: `token ${githubToken()}`,
    },
  });
}
    </code_example>
    <note>Secret keys are globally unique across the application</note>
  </usage>
</implementation>

<secret_storage>
  <methods>
    <method name="cloud_dashboard">
      <steps>
        <step>Open app in Encore Cloud dashboard: https://app.encore.cloud</step>
        <step>Navigate to Settings > Secrets</step>
        <step>Create and manage secrets for different environments</step>
      </steps>
    </method>

    <method name="cli">
      <command>encore secret set --type &lt;types&gt; &lt;secret-name&gt;</command>
      <types>
        <type>production (prod)</type>
        <type>development (dev)</type>
        <type>preview (pr)</type>
        <type>local</type>
      </types>
      <example>encore secret set --type prod SSHPrivateKey</example>
    </method>

    <method name="local_override">
      <description>Override secrets locally using .secrets.local.cue file</description>
      <example>
GitHubAPIToken: "my-local-override-token"
SSHPrivateKey: "custom-ssh-private-key"
      </example>
    </method>
  </methods>
</secret_storage>

<environment_settings>
  <rules>
    <rule>One secret value per environment type</rule>
    <rule>Environment-specific values override environment type values</rule>
  </rules>
</environment_settings>
</secrets_management>

<streaming_apis>
<overview>
  <description>API endpoints that enable data streaming via WebSocket connections</description>
  <stream_types>
    <type name="StreamIn">Client to server streaming</type>
    <type name="StreamOut">Server to client streaming</type>
    <type name="StreamInOut">Bidirectional streaming</type>
  </stream_types>
</overview>

<stream_implementations>
  <stream_in>
    <description>Stream data from client to server</description>
    <code_example>
import { api } from "encore.dev/api";

interface Message {
  data: string;
  done: boolean;
}

export const uploadStream = api.streamIn<Message>(
  { path: "/upload", expose: true },
  async (stream) => {
    for await (const data of stream) {
      // Process incoming data
      if (data.done) break;
    }
  }
);
    </code_example>
  </stream_in>

  <stream_out>
    <description>Stream data from server to client</description>
    <code_example>
export const dataStream = api.streamOut<Message>(
  { path: "/stream", expose: true },
  async (stream) => {
    // Send messages to client
    await stream.send({ data: "message" });
    await stream.close();
  }
);
    </code_example>
  </stream_out>

  <stream_inout>
    <description>Bidirectional streaming</description>
    <code_example>
export const chatStream = api.streamInOut<InMessage, OutMessage>(
  { path: "/chat", expose: true },
  async (stream) => {
    for await (const msg of stream) {
      await stream.send(/* response */);
    }
  }
);
    </code_example>
  </stream_inout>
</stream_implementations>

<features>
  <handshake>
    <description>Initial HTTP request for connection setup</description>
    <supports>
      <item>Path parameters</item>
      <item>Query parameters</item>
      <item>Headers</item>
      <item>Authentication data</item>
    </supports>
  </handshake>

  <client_usage>
    <code_example>
const stream = client.serviceName.endpointName();
await stream.send({ /* message */ });
for await (const msg of stream) {
  // Handle incoming messages
}
    </code_example>
  </client_usage>

  <service_to_service>
    <description>Internal streaming between services using ~encore/clients import</description>
    <code_example>
import { service } from "~encore/clients";
const stream = await service.streamEndpoint();
    </code_example>
  </service_to_service>
</features>
</streaming_apis>

<validation>
<overview>
  <description>Built-in request validation using TypeScript types for both runtime and compile-time type safety</description>
  <core_example>
import { Header, Query, api } from "encore.dev/api";

interface Request {
  limit?: Query<number>;               // Optional query parameter
  myHeader: Header<"X-My-Header">;     // Required header
  type: "sprocket" | "widget";         // Required enum in body
}

export const myEndpoint = api<Request, Response>(
  { expose: true, method: "POST", path: "/api" },
  async ({ limit, myHeader, type }) => {
    // Implementation
  }
);
  </core_example>
</overview>

<validation_types>
  <basic_types>
    <type name="string">
      <example>name: string;</example>
    </type>
    <type name="number">
      <example>age: number;</example>
    </type>
    <type name="boolean">
      <example>isActive: boolean;</example>
    </type>
    <type name="arrays">
      <example>
strings: string[];
numbers: number[];
objects: { name: string }[];
mixed: (string | number)[];
      </example>
    </type>
    <type name="enums">
      <example>type: "BLOG_POST" | "COMMENT";</example>
    </type>
  </basic_types>

  <modifiers>
    <modifier name="optional">
      <syntax>fieldName?: type;</syntax>
      <example>name?: string;</example>
    </modifier>
    <modifier name="nullable">
      <syntax>fieldName: type | null;</syntax>
      <example>name: string | null;</example>
    </modifier>
  </modifiers>
</validation_types>

<validation_rules>
  <rules>
    <rule name="Min/Max">
      <description>Validate number ranges</description>
      <example>count: number & (Min<3> & Max<1000>);</example>
    </rule>
    <rule name="MinLen/MaxLen">
      <description>Validate string/array lengths</description>
      <example>username: string & (MinLen<5> & MaxLen<20>);</example>
    </rule>
    <rule name="Format">
      <description>Validate string formats</description>
      <example>contact: string & (IsURL | IsEmail);</example>
    </rule>
  </rules>
</validation_rules>

<source_types>
  <body>
    <description>Default for methods with request bodies</description>
    <parse_from>JSON request body</parse_from>
  </body>

  <query>
    <description>URL query parameters</description>
    <usage>Use Query type or default for GET/HEAD/DELETE</usage>
  </query>

  <headers>
    <description>HTTP headers</description>
    <usage>Use Header<"Name-Of-Header"> type</usage>
  </headers>

  <params>
    <description>URL path parameters</description>
    <example>path: "/user/:id", param: { id: string }</example>
  </params>
</source_types>

<error_handling>
  <response>
    <status>400 Bad Request</status>
    <format>
{
  "code": "invalid_argument",
  "message": "unable to decode request body",
  "internal_message": "Error details"
}
    </format>
  </response>
</error_handling>
</validation>

<static_assets>
<overview>
  <description>Encore.ts's built-in support for serving static assets (images, HTML, CSS, JavaScript)</description>
  <use_case>Serving static websites or pre-compiled single-page applications (SPAs)</use_case>
</overview>

<implementation>
  <basic_usage>
    <description>Serve static files using api.static function</description>
    <code_example>
import { api } from "encore.dev/api";
export const assets = api.static(
  { expose: true, path: "/frontend/*path", dir: "./assets" },
);
    </code_example>
    <behavior>
      <rule>Serves files from ./assets under /frontend path prefix</rule>
      <rule>Automatically serves index.html files at directory roots</rule>
    </behavior>
  </basic_usage>

  <root_serving>
    <description>Serve files at domain root using fallback routes</description>
    <code_example>
export const assets = api.static(
  { expose: true, path: "/!path", dir: "./assets" },
);
    </code_example>
    <note>Uses !path syntax instead of *path to avoid conflicts</note>
  </root_serving>

  <custom_404>
    <description>Configure custom 404 response</description>
    <code_example>
export const assets = api.static(
  { 
    expose: true, 
    path: "/!path", 
    dir: "./assets", 
    notFound: "./not_found.html" 
  },
);
    </code_example>
  </custom_404>
</implementation>

</static_assets>

<graphql>

<description>Encore.ts has GraphQL support through raw endpoints with automatic tracing</description>

<implementation>
  <steps>
    <step>Create raw endpoint for client requests</step>
    <step>Pass request to GraphQL library</step>
    <step>Handle queries and mutations</step>
    <step>Return GraphQL response</step>
  </steps>

  <apollo_example>
    <code_example>
import { HeaderMap } from "@apollo/server";
import { api } from "encore.dev/api";
const { ApolloServer, gql } = require("apollo-server");
import { json } from "node:stream/consumers";

const server = new ApolloServer({ typeDefs, resolvers });
await server.start();

export const graphqlAPI = api.raw(
  { expose: true, path: "/graphql", method: "*" },
  async (req, res) => {
    server.assertStarted("/graphql");
    
    const headers = new HeaderMap();
    for (const [key, value] of Object.entries(req.headers)) {
      if (value !== undefined) {
        headers.set(key, Array.isArray(value) ? value.join(", ") : value);
      }
    }

    const httpGraphQLResponse = await server.executeHTTPGraphQLRequest({
      httpGraphQLRequest: {
        headers,
        method: req.method!.toUpperCase(),
        body: await json(req),
        search: new URLSearchParams(req.url ?? "").toString(),
      },
      context: async () => ({ req, res }),
    });

    // Set response headers and status
    for (const [key, value] of httpGraphQLResponse.headers) {
      res.setHeader(key, value);
    }
    res.statusCode = httpGraphQLResponse.status || 200;

    // Write response
    if (httpGraphQLResponse.body.kind === "complete") {
      res.end(httpGraphQLResponse.body.string);
      return;
    }
    
    for await (const chunk of httpGraphQLResponse.body.asyncIterator) {
      res.write(chunk);
    }
    res.end();
  }
);
    </code_example>
  </apollo_example>
</implementation>

<rest_integration>

  <example>
    <schema>
type Query {
  books: [Book]
}

type Book {
  title: String!
  author: String!
}
    </schema>

    <resolver>
import { book } from "~encore/clients";
import { QueryResolvers } from "../__generated__/resolvers-types";

const queries: QueryResolvers = {
  books: async () => {
    const { books } = await book.list();
    return books;
  },
};
    </resolver>

    <rest_endpoint>
import { api } from "encore.dev/api";
import { Book } from "../__generated__/resolvers-types";

export const list = api(
  { expose: true, method: "GET", path: "/books" },
  async (): Promise<{ books: Book[] }> => {
    return { books: db };
  }
);
    </rest_endpoint>
  </example>
</rest_integration>
</graphql>

<authentication>
<overview>
  <description>Authentication system for identifying API callers in both consumer and B2B applications</description>
  <activation>Set auth: true in API endpoint options</activation>
</overview>

<auth_handler>
  <implementation>
    <description>Required for APIs with auth: true</description>
    <code_example>
import { Header, Gateway } from "encore.dev/api";
import { authHandler } from "encore.dev/auth";

interface AuthParams {
    authorization: Header<"Authorization">;
}

interface AuthData {
    userID: string;
}

export const auth = authHandler<AuthParams, AuthData>(
    async (params) => {
        // Authenticate user based on params
        return {userID: "my-user-id"};
    }
)

export const gateway = new Gateway({
    authHandler: auth,
})
    </code_example>
  </implementation>

  <rejection>
    <description>Reject authentication by throwing exception</description>
    <code_example>
throw APIError.unauthenticated("bad credentials");
    </code_example>
  </rejection>
</auth_handler>

<authentication_process>
  <step name="determine_auth">
    <triggers>
      <trigger>Any request containing auth parameters</trigger>
      <trigger>Regardless of endpoint authentication requirements</trigger>
    </triggers>
    <outcomes>
      <outcome type="success">Returns AuthData - request authenticated</outcome>
      <outcome type="unauthenticated">Throws Unauthenticated - treated as no auth</outcome>
      <outcome type="error">Throws other error - request aborted</outcome>
    </outcomes>
  </step>

  <step name="endpoint_call">
    <rules>
      <rule>If endpoint requires auth and request not authenticated - reject</rule>
      <rule>If authenticated, auth data passed to endpoint regardless of requirements</rule>
    </rules>
  </step>
</authentication_process>

<auth_data_usage>
  <access>
    <method>Import getAuthData from ~encore/auth</method>
    <feature>Type-safe resolution of auth data</feature>
  </access>

  <propagation>
    <description>Automatic propagation in internal API calls</description>
    <constraint>Calls to auth-required endpoints fail if original request lacks auth</constraint>
  </propagation>
</auth_data_usage>
</authentication>

<metadata>
<overview>
  <description>Access environment and application information through metadata API</description>
  <location>Available in encore.dev package</location>
</overview>

<app_metadata>
  <function>appMeta()</function>
  <returns>
    <field name="appId">Application name</field>
    <field name="apiBaseUrl">Public API access URL</field>
    <field name="environment">Current running environment</field>
    <field name="build">Version control revision information</field>
    <field name="deploy">Deployment ID and timestamp</field>
  </returns>
</app_metadata>

<request_metadata>
  <function>currentRequest()</function>
  <types>
    <api_call>
      <interface>
interface APICallMeta {
  type: "api-call";
  api: APIDesc;
  method: Method;
  path: string;
  pathAndQuery: string;
  pathParams: Record<string, any>;
  headers: Record<string, string | string[]>;
  parsedPayload?: Record<string, any>;
}
      </interface>
    </api_call>

    <pubsub>
      <interface>
interface PubSubMessageMeta {
  type: "pubsub-message";
  service: string;
  topic: string;
  subscription: string;
  messageId: string;
  deliveryAttempt: number;
  parsedPayload?: Record<string, any>;
}
      </interface>
    </pubsub>
  </types>
  <note>Returns undefined if called during service initialization</note>
</request_metadata>

<use_cases>
  <case name="cloud_services">
    <description>Implement different behavior based on cloud provider</description>
    <code_example>
import { appMeta } from "encore.dev";

async function audit(userID: string, event: Record<string, any>) {
  const cloud = appMeta().environment.cloud;
  switch (cloud) {
    case "aws": return writeIntoRedshift(userID, event);
    case "gcp": return writeIntoBigQuery(userID, event);
    case "local": return writeIntoFile(userID, event);
    default: throw new Error(`unknown cloud: ${cloud}`);
  }
}
    </code_example>
  </case>

  <case name="environment_check">
    <description>Modify behavior based on environment type</description>
    <code_example>
switch (appMeta().environment.type) {
  case "test":
  case "development":
    await markEmailVerified(userID);
    break;
  default:
    await sendVerificationEmail(userID);
    break;
}
    </code_example>
  </case>
</use_cases>
</metadata>

<middleware>

<description>Reusable code running before/after API requests across multiple endpoints</description>

<implementation>
  <basic_usage>
    <description>Create middleware using middleware helper from encore.dev/api</description>
    <code_example>
import { middleware } from "encore.dev/api";

export default new Service("myService", {
    middlewares: [
        middleware({ target: { auth: true } }, async (req, next) => {
            // Pre-handler logic
            const resp = await next(req);
            // Post-handler logic
            return resp
        })
    ]
});
    </code_example>
  </basic_usage>

  <request_access>
    <types>
      <type name="typed_api">
        <field>req.requestMeta</field>
      </type>
      <type name="streaming">
        <field>req.requestMeta</field>
        <field>req.stream</field>
      </type>
      <type name="raw">
        <field>req.rawRequest</field>
        <field>req.rawResponse</field>
      </type>
    </types>
  </request_access>

  <response_handling>
    <description>HandlerResponse object with header modification capabilities</description>
    <methods>
      <method>resp.header.set(key, value)</method>
      <method>resp.header.add(key, value)</method>
    </methods>
  </response_handling>
</implementation>

<configuration>
  <ordering>
    <description>Middleware executes in order of definition</description>
    <code_example>
export default new Service("myService", {
    middlewares: [
        first,
        second,
        third
    ],
});
    </code_example>
  </ordering>

  <targeting>
    <description>Specify which endpoints middleware applies to</description>
    <best_practice>Use target option instead of runtime filtering for better performance</best_practice>
    <note>Defaults to all endpoints if target not specified</note>
  </targeting>
</configuration>
</middleware>

<orm_integration>
<overview>
  <description>Built-in support for ORMs and migration frameworks through named databases and SQL migration files</description>
  <compatibility>
    <orm_requirement>Must support standard SQL driver connections</orm_requirement>
    <migration_requirement>Must generate standard SQL migration files</migration_requirement>
  </compatibility>
</overview>

<database_connection>
  <setup>
    <description>Use SQLDatabase class for named databases and connection strings</description>
    <code_example>
import { SQLDatabase } from "encore.dev/storage/sqldb";

const SiteDB = new SQLDatabase("siteDB", {
  migrations: "./migrations",
});

const connStr = SiteDB.connectionString;
    </code_example>
  </setup>
</database_connection>

</orm_integration>

<drizzle_integration_example>
<overview>
  <description>Integration guide for using Drizzle ORM with Encore.ts</description>
</overview>

<implementation_steps>
  <step name="database_setup">
    <description>Initialize SQLDatabase and configure Drizzle connection</description>
    <code_example name="database.ts">
import { api } from "encore.dev/api";
import { SQLDatabase } from "encore.dev/storage/sqldb";
import { drizzle } from "drizzle-orm/node-postgres";
import { users } from "./schema";

const db = new SQLDatabase("test", {
  migrations: {
    path: "migrations",
    source: "drizzle",
  },
});

const orm = drizzle(db.connectionString);
await orm.select().from(users);
    </code_example>
  </step>

  <step name="drizzle_config">
    <description>Create Drizzle configuration file</description>
    <code_example name="drizzle.config.ts">
import 'dotenv/config';
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  out: 'migrations',
  schema: 'schema.ts',
  dialect: 'postgresql',
});
    </code_example>
  </step>

  <step name="schema_definition">
    <description>Define database schema using Drizzle's pg-core</description>
    <code_example name="schema.ts">
import * as p from "drizzle-orm/pg-core";

export const users = p.pgTable("users", {
  id: p.serial().primaryKey(),
  name: p.text(),
  email: p.text().unique(),
});
    </code_example>
  </step>

  <step name="migration_generation">
    <description>Generate database migrations</description>
    <command>drizzle-kit generate</command>
    <location>Run in directory containing drizzle.config.ts</location>
  </step>

  <step name="migration_application">
    <description>Migrations automatically applied during Encore application runtime</description>
    <note>Manual migration commands not required</note>
  </step>
</implementation_steps>
</drizzle_integration_example>

<cors>
<overview>
  <description>CORS controls which website origins can access your API</description>
  <scope>Browser requests to resources on different origins (scheme, domain, port)</scope>
</overview>

<configuration>
  <location>Specified in encore.app file under global_cors key</location>
  <structure>
    <options>
      <option name="debug">
        <description>Enables CORS debug logging</description>
        <type>boolean</type>
      </option>

      <option name="allow_headers">
        <description>Additional accepted headers</description>
        <type>string[]</type>
        <special_value>"*" for all headers</special_value>
      </option>

      <option name="expose_headers">
        <description>Additional exposed headers beyond defaults</description>
        <type>string[]</type>
        <special_value>"*" for all headers</special_value>
      </option>

      <option name="allow_origins_without_credentials">
        <description>Allowed origins for non-credentialed requests</description>
        <type>string[]</type>
        <default>["*"]</default>
      </option>

      <option name="allow_origins_with_credentials">
        <description>Allowed origins for credentialed requests</description>
        <type>string[]</type>
        <wildcard_support>
          <example>https://*.example.com</example>
          <example>https://*-myapp.example.com</example>
        </wildcard_support>
      </option>
    </options>
  </structure>
</configuration>

<default_behavior>
  <rules>
    <rule>Allows unauthenticated requests from all origins</rule>
    <rule>Disallows authenticated requests from other origins</rule>
    <rule>All origins allowed in local development</rule>
  </rules>
</default_behavior>

<header_handling>
  <automatic>
    <description>Encore automatically configures headers through static analysis</description>
    <trigger>Request or response types containing header fields</trigger>
  </automatic>

  <manual>
    <description>Additional headers can be configured via allow_headers and expose_headers</description>
    <use_case>Custom headers in raw endpoints not detected by static analysis</use_case>
  </manual>
</header_handling>
</cors>

<logging>

<description>Built-in structured logging combining free-form messages with type-safe key-value pairs</description>
  
<implementation>
  <setup>
    <import>import log from "encore.dev/log";</import>
  </setup>

  <log_levels>
    <level name="error">Critical issues</level>
    <level name="warn">Warning conditions</level>
    <level name="info">General information</level>
    <level name="debug">Debugging information</level>
    <level name="trace">Detailed tracing</level>
  </log_levels>

  <usage>
    <basic>
      <description>Direct logging with message and optional structured data</description>
      <code_example>
log.info("log message", {is_subscriber: true})
log.error(err, "something went terribly wrong!")
      </code_example>
    </basic>

    <with_context>
      <description>Group logs with shared key-value pairs</description>
      <code_example>
const logger = log.with({is_subscriber: true})
logger.info("user logged in", {login_method: "oauth"}) // includes is_subscriber=true
      </code_example>
    </with_context>
  </usage>
</implementation>

</logging>

<encore_ts_example_apps>

<hello_world_example_repo>
https://github.com/encoredev/examples/tree/main/ts/hello-world
</hello_world_example_repo>

<url_shortener_example_repo>
https://github.com/encoredev/examples/tree/main/ts/url-shortener
</url_shortener_example_repo>

<uptime_monitor_example_repo>
https://github.com/encoredev/examples/tree/main/ts/uptime
</uptime_monitor_example_repo>

</encore_ts_example_apps>

<package_management>
    <default_approach>Use a single root-level package.json file (monorepo approach) for Encore.ts projects including frontend dependencies</default_approach>
    <alternative_approach>
        <support>Separate package.json files in sub-packages</support>
        <limitations>
            <limitation>encore.go application must use one package with a single package.json file</limitation>
            <limitation>Other separate packages must be pre-transpiled to JavaScript</limitation>
        </limitations>
    </alternative_approach>
</package_management>

</encore_ts_domain_knowledge>

<encore_cli_reference>
<execution_commands>
<run>
<command>encore run [--debug] [--watch=true] [flags]</command>
<purpose>Runs your application</purpose>
</run>
</execution_commands>

<app_management>
<clone>
<command>encore app clone [app-id] [directory]</command>
<purpose>Clone an Encore app to your computer</purpose>
</clone>

<create>
<command>encore app create [name]</command>
<purpose>Create a new Encore app</purpose>
</create>

<init>
<command>encore app init [name]</command>
<purpose>Create new app from existing repository</purpose>
</init>

<link>
<command>encore app link [app-id]</command>
<purpose>Link app with server</purpose>
</link>
</app_management>

<authentication>
<login>
<command>encore auth login</command>
<purpose>Log in to Encore</purpose>
</login>

<logout>
<command>encore auth logout</command>
<purpose>Logs out current user</purpose>
</logout>

<signup>
<command>encore auth signup</command>
<purpose>Create new account</purpose>
</signup>

<whoami>
<command>encore auth whoami</command>
<purpose>Show current user</purpose>
</whoami>
</authentication>

<daemon_management>
<restart>
<command>encore daemon</command>
<purpose>Restart daemon for unexpected behavior</purpose>
</restart>

<env>
<command>encore daemon env</command>
<purpose>Output environment information</purpose>
</env>
</daemon_management>

<database_commands>
<shell>
<command>encore db shell database-name [--env=name]</command>
<purpose>Connect via psql shell</purpose>
<permissions>--write, --admin, --superuser flags available</permissions>
</shell>

<connection>
<command>encore db conn-uri database-name [--env=name]</command>
<purpose>Output connection string</purpose>
</connection>

<proxy>
<command>encore db proxy [--env=name]</command>
<purpose>Set up local database connection proxy</purpose>
</proxy>

<reset>
<command>encore db reset [service-names...]</command>
<purpose>Reset specified service databases</purpose>
</reset>
</database_commands>

<code_generation>
<client>
<command>encore gen client [app-id] [--env=name] [--lang=lang]</command>
<purpose>Generate API client</purpose>
<languages>
- go: Go client with net/http
- typescript: TypeScript with Fetch API
- javascript: JavaScript with Fetch API
- openapi: OpenAPI spec
</languages>
</client>
</code_generation>

<logging>
<command>encore logs [--env=prod] [--json]</command>
<purpose>Stream application logs</purpose>
</logging>

<kubernetes>
<configure>
<command>encore k8s configure --env=ENV_NAME</command>
<purpose>Update kubectl config for environment</purpose>
</configure>
</kubernetes>

<secrets>
<set>
<command>encore secret set --type types secret-name</command>
<purpose>Set secret value</purpose>
<types>production, development, preview, local</types>
</set>

<list>
<command>encore secret list [keys...]</command>
<purpose>List secrets</purpose>
</list>

<archive>
<command>encore secret archive id</command>
<purpose>Archive secret value</purpose>
</archive>

<unarchive>
<command>encore secret unarchive id</command>
<purpose>Unarchive secret value</purpose>
</unarchive>
</secrets>

<version_management>
<check>
<command>encore version</command>
<purpose>Report current version</purpose>
</check>

<update>
<command>encore version update</command>
<purpose>Check and apply updates</purpose>
</update>
</version_management>

<vpn>
<start>
<command>encore vpn start</command>
<purpose>Set up secure connection to private environments</purpose>
</start>

<status>
<command>encore vpn status</command>
<purpose>Check VPN connection status</purpose>
</status>

<stop>
<command>encore vpn stop</command>
<purpose>Stop VPN connection</purpose>
</stop>
</vpn>

<build>
<docker>
<command>encore build docker</command>
<purpose>Build portable Docker image</purpose>
<flags>
- --base string: Define base image
- --push: Push to remote repository
</flags>
</docker>
</build>
</encore_cli_reference>